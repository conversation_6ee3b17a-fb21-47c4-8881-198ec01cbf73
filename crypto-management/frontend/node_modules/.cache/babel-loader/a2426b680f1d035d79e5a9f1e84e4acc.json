{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport { deprecateLog, deprecateReplaceLog } from '../../util/log.js';\nvar each = zrUtil.each;\nvar isObject = zrUtil.isObject;\nvar POSSIBLE_STYLES = ['areaStyle', 'lineStyle', 'nodeStyle', 'linkStyle', 'chordStyle', 'label', 'labelLine'];\nfunction compatEC2ItemStyle(opt) {\n  var itemStyleOpt = opt && opt.itemStyle;\n  if (!itemStyleOpt) {\n    return;\n  }\n  for (var i = 0, len = POSSIBLE_STYLES.length; i < len; i++) {\n    var styleName = POSSIBLE_STYLES[i];\n    var normalItemStyleOpt = itemStyleOpt.normal;\n    var emphasisItemStyleOpt = itemStyleOpt.emphasis;\n    if (normalItemStyleOpt && normalItemStyleOpt[styleName]) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(\"itemStyle.normal.\" + styleName, styleName);\n      }\n      opt[styleName] = opt[styleName] || {};\n      if (!opt[styleName].normal) {\n        opt[styleName].normal = normalItemStyleOpt[styleName];\n      } else {\n        zrUtil.merge(opt[styleName].normal, normalItemStyleOpt[styleName]);\n      }\n      normalItemStyleOpt[styleName] = null;\n    }\n    if (emphasisItemStyleOpt && emphasisItemStyleOpt[styleName]) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(\"itemStyle.emphasis.\" + styleName, \"emphasis.\" + styleName);\n      }\n      opt[styleName] = opt[styleName] || {};\n      if (!opt[styleName].emphasis) {\n        opt[styleName].emphasis = emphasisItemStyleOpt[styleName];\n      } else {\n        zrUtil.merge(opt[styleName].emphasis, emphasisItemStyleOpt[styleName]);\n      }\n      emphasisItemStyleOpt[styleName] = null;\n    }\n  }\n}\nfunction convertNormalEmphasis(opt, optType, useExtend) {\n  if (opt && opt[optType] && (opt[optType].normal || opt[optType].emphasis)) {\n    var normalOpt = opt[optType].normal;\n    var emphasisOpt = opt[optType].emphasis;\n    if (normalOpt) {\n      if (process.env.NODE_ENV !== 'production') {\n        // eslint-disable-next-line max-len\n        deprecateLog(\"'normal' hierarchy in \" + optType + \" has been removed since 4.0. All style properties are configured in \" + optType + \" directly now.\");\n      } // Timeline controlStyle has other properties besides normal and emphasis\n\n      if (useExtend) {\n        opt[optType].normal = opt[optType].emphasis = null;\n        zrUtil.defaults(opt[optType], normalOpt);\n      } else {\n        opt[optType] = normalOpt;\n      }\n    }\n    if (emphasisOpt) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog(optType + \".emphasis has been changed to emphasis.\" + optType + \" since 4.0\");\n      }\n      opt.emphasis = opt.emphasis || {};\n      opt.emphasis[optType] = emphasisOpt; // Also compat the case user mix the style and focus together in ec3 style\n      // for example: { itemStyle: { normal: {}, emphasis: {focus, shadowBlur} } }\n\n      if (emphasisOpt.focus) {\n        opt.emphasis.focus = emphasisOpt.focus;\n      }\n      if (emphasisOpt.blurScope) {\n        opt.emphasis.blurScope = emphasisOpt.blurScope;\n      }\n    }\n  }\n}\nfunction removeEC3NormalStatus(opt) {\n  convertNormalEmphasis(opt, 'itemStyle');\n  convertNormalEmphasis(opt, 'lineStyle');\n  convertNormalEmphasis(opt, 'areaStyle');\n  convertNormalEmphasis(opt, 'label');\n  convertNormalEmphasis(opt, 'labelLine'); // treemap\n\n  convertNormalEmphasis(opt, 'upperLabel'); // graph\n\n  convertNormalEmphasis(opt, 'edgeLabel');\n}\nfunction compatTextStyle(opt, propName) {\n  // Check whether is not object (string\\null\\undefined ...)\n  var labelOptSingle = isObject(opt) && opt[propName];\n  var textStyle = isObject(labelOptSingle) && labelOptSingle.textStyle;\n  if (textStyle) {\n    if (process.env.NODE_ENV !== 'production') {\n      // eslint-disable-next-line max-len\n      deprecateLog(\"textStyle hierarchy in \" + propName + \" has been removed since 4.0. All textStyle properties are configured in \" + propName + \" directly now.\");\n    }\n    for (var i = 0, len = modelUtil.TEXT_STYLE_OPTIONS.length; i < len; i++) {\n      var textPropName = modelUtil.TEXT_STYLE_OPTIONS[i];\n      if (textStyle.hasOwnProperty(textPropName)) {\n        labelOptSingle[textPropName] = textStyle[textPropName];\n      }\n    }\n  }\n}\nfunction compatEC3CommonStyles(opt) {\n  if (opt) {\n    removeEC3NormalStatus(opt);\n    compatTextStyle(opt, 'label');\n    opt.emphasis && compatTextStyle(opt.emphasis, 'label');\n  }\n}\nfunction processSeries(seriesOpt) {\n  if (!isObject(seriesOpt)) {\n    return;\n  }\n  compatEC2ItemStyle(seriesOpt);\n  removeEC3NormalStatus(seriesOpt);\n  compatTextStyle(seriesOpt, 'label'); // treemap\n\n  compatTextStyle(seriesOpt, 'upperLabel'); // graph\n\n  compatTextStyle(seriesOpt, 'edgeLabel');\n  if (seriesOpt.emphasis) {\n    compatTextStyle(seriesOpt.emphasis, 'label'); // treemap\n\n    compatTextStyle(seriesOpt.emphasis, 'upperLabel'); // graph\n\n    compatTextStyle(seriesOpt.emphasis, 'edgeLabel');\n  }\n  var markPoint = seriesOpt.markPoint;\n  if (markPoint) {\n    compatEC2ItemStyle(markPoint);\n    compatEC3CommonStyles(markPoint);\n  }\n  var markLine = seriesOpt.markLine;\n  if (markLine) {\n    compatEC2ItemStyle(markLine);\n    compatEC3CommonStyles(markLine);\n  }\n  var markArea = seriesOpt.markArea;\n  if (markArea) {\n    compatEC3CommonStyles(markArea);\n  }\n  var data = seriesOpt.data; // Break with ec3: if `setOption` again, there may be no `type` in option,\n  // then the backward compat based on option type will not be performed.\n\n  if (seriesOpt.type === 'graph') {\n    data = data || seriesOpt.nodes;\n    var edgeData = seriesOpt.links || seriesOpt.edges;\n    if (edgeData && !zrUtil.isTypedArray(edgeData)) {\n      for (var i = 0; i < edgeData.length; i++) {\n        compatEC3CommonStyles(edgeData[i]);\n      }\n    }\n    zrUtil.each(seriesOpt.categories, function (opt) {\n      removeEC3NormalStatus(opt);\n    });\n  }\n  if (data && !zrUtil.isTypedArray(data)) {\n    for (var i = 0; i < data.length; i++) {\n      compatEC3CommonStyles(data[i]);\n    }\n  } // mark point data\n\n  markPoint = seriesOpt.markPoint;\n  if (markPoint && markPoint.data) {\n    var mpData = markPoint.data;\n    for (var i = 0; i < mpData.length; i++) {\n      compatEC3CommonStyles(mpData[i]);\n    }\n  } // mark line data\n\n  markLine = seriesOpt.markLine;\n  if (markLine && markLine.data) {\n    var mlData = markLine.data;\n    for (var i = 0; i < mlData.length; i++) {\n      if (zrUtil.isArray(mlData[i])) {\n        compatEC3CommonStyles(mlData[i][0]);\n        compatEC3CommonStyles(mlData[i][1]);\n      } else {\n        compatEC3CommonStyles(mlData[i]);\n      }\n    }\n  } // Series\n\n  if (seriesOpt.type === 'gauge') {\n    compatTextStyle(seriesOpt, 'axisLabel');\n    compatTextStyle(seriesOpt, 'title');\n    compatTextStyle(seriesOpt, 'detail');\n  } else if (seriesOpt.type === 'treemap') {\n    convertNormalEmphasis(seriesOpt.breadcrumb, 'itemStyle');\n    zrUtil.each(seriesOpt.levels, function (opt) {\n      removeEC3NormalStatus(opt);\n    });\n  } else if (seriesOpt.type === 'tree') {\n    removeEC3NormalStatus(seriesOpt.leaves);\n  } // sunburst starts from ec4, so it does not need to compat levels.\n}\nfunction toArr(o) {\n  return zrUtil.isArray(o) ? o : o ? [o] : [];\n}\nfunction toObj(o) {\n  return (zrUtil.isArray(o) ? o[0] : o) || {};\n}\nexport default function globalCompatStyle(option, isTheme) {\n  each(toArr(option.series), function (seriesOpt) {\n    isObject(seriesOpt) && processSeries(seriesOpt);\n  });\n  var axes = ['xAxis', 'yAxis', 'radiusAxis', 'angleAxis', 'singleAxis', 'parallelAxis', 'radar'];\n  isTheme && axes.push('valueAxis', 'categoryAxis', 'logAxis', 'timeAxis');\n  each(axes, function (axisName) {\n    each(toArr(option[axisName]), function (axisOpt) {\n      if (axisOpt) {\n        compatTextStyle(axisOpt, 'axisLabel');\n        compatTextStyle(axisOpt.axisPointer, 'label');\n      }\n    });\n  });\n  each(toArr(option.parallel), function (parallelOpt) {\n    var parallelAxisDefault = parallelOpt && parallelOpt.parallelAxisDefault;\n    compatTextStyle(parallelAxisDefault, 'axisLabel');\n    compatTextStyle(parallelAxisDefault && parallelAxisDefault.axisPointer, 'label');\n  });\n  each(toArr(option.calendar), function (calendarOpt) {\n    convertNormalEmphasis(calendarOpt, 'itemStyle');\n    compatTextStyle(calendarOpt, 'dayLabel');\n    compatTextStyle(calendarOpt, 'monthLabel');\n    compatTextStyle(calendarOpt, 'yearLabel');\n  }); // radar.name.textStyle\n\n  each(toArr(option.radar), function (radarOpt) {\n    compatTextStyle(radarOpt, 'name'); // Use axisName instead of name because component has name property\n\n    if (radarOpt.name && radarOpt.axisName == null) {\n      radarOpt.axisName = radarOpt.name;\n      delete radarOpt.name;\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog('name property in radar component has been changed to axisName');\n      }\n    }\n    if (radarOpt.nameGap != null && radarOpt.axisNameGap == null) {\n      radarOpt.axisNameGap = radarOpt.nameGap;\n      delete radarOpt.nameGap;\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog('nameGap property in radar component has been changed to axisNameGap');\n      }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      each(radarOpt.indicator, function (indicatorOpt) {\n        if (indicatorOpt.text) {\n          deprecateReplaceLog('text', 'name', 'radar.indicator');\n        }\n      });\n    }\n  });\n  each(toArr(option.geo), function (geoOpt) {\n    if (isObject(geoOpt)) {\n      compatEC3CommonStyles(geoOpt);\n      each(toArr(geoOpt.regions), function (regionObj) {\n        compatEC3CommonStyles(regionObj);\n      });\n    }\n  });\n  each(toArr(option.timeline), function (timelineOpt) {\n    compatEC3CommonStyles(timelineOpt);\n    convertNormalEmphasis(timelineOpt, 'label');\n    convertNormalEmphasis(timelineOpt, 'itemStyle');\n    convertNormalEmphasis(timelineOpt, 'controlStyle', true);\n    var data = timelineOpt.data;\n    zrUtil.isArray(data) && zrUtil.each(data, function (item) {\n      if (zrUtil.isObject(item)) {\n        convertNormalEmphasis(item, 'label');\n        convertNormalEmphasis(item, 'itemStyle');\n      }\n    });\n  });\n  each(toArr(option.toolbox), function (toolboxOpt) {\n    convertNormalEmphasis(toolboxOpt, 'iconStyle');\n    each(toolboxOpt.feature, function (featureOpt) {\n      convertNormalEmphasis(featureOpt, 'iconStyle');\n    });\n  });\n  compatTextStyle(toObj(option.axisPointer), 'label');\n  compatTextStyle(toObj(option.tooltip).axisPointer, 'label'); // Clean logs\n  // storedLogs = {};\n}", "map": {"version": 3, "names": ["zrUtil", "modelUtil", "deprecateLog", "deprecateReplaceLog", "each", "isObject", "POSSIBLE_STYLES", "compatEC2ItemStyle", "opt", "itemStyleOpt", "itemStyle", "i", "len", "length", "styleName", "normalItemStyleOpt", "normal", "emphasisItemStyleOpt", "emphasis", "process", "env", "NODE_ENV", "merge", "convertNormalEmphasis", "optType", "useExtend", "normalOpt", "emphasisOpt", "defaults", "focus", "blurScope", "removeEC3NormalStatus", "compatTextStyle", "propName", "labelOptSingle", "textStyle", "TEXT_STYLE_OPTIONS", "textPropName", "hasOwnProperty", "compatEC3CommonStyles", "processSeries", "seriesOpt", "markPoint", "markLine", "<PERSON><PERSON><PERSON>", "data", "type", "nodes", "edgeData", "links", "edges", "isTypedArray", "categories", "mpData", "mlData", "isArray", "breadcrumb", "levels", "leaves", "toArr", "o", "to<PERSON><PERSON><PERSON>", "globalCompatStyle", "option", "isTheme", "series", "axes", "push", "axisName", "axisOpt", "axisPointer", "parallel", "parallelOpt", "parallelAxisDefault", "calendar", "calendarOpt", "radar", "radarOpt", "name", "nameGap", "axisNameGap", "indicator", "indicatorOpt", "text", "geo", "geoOpt", "regions", "regionObj", "timeline", "timelineOpt", "item", "toolbox", "toolboxOpt", "feature", "featureOpt", "tooltip"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/echarts/lib/preprocessor/helper/compatStyle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport { deprecateLog, deprecateReplaceLog } from '../../util/log.js';\nvar each = zrUtil.each;\nvar isObject = zrUtil.isObject;\nvar POSSIBLE_STYLES = ['areaStyle', 'lineStyle', 'nodeStyle', 'linkStyle', 'chordStyle', 'label', 'labelLine'];\n\nfunction compatEC2ItemStyle(opt) {\n  var itemStyleOpt = opt && opt.itemStyle;\n\n  if (!itemStyleOpt) {\n    return;\n  }\n\n  for (var i = 0, len = POSSIBLE_STYLES.length; i < len; i++) {\n    var styleName = POSSIBLE_STYLES[i];\n    var normalItemStyleOpt = itemStyleOpt.normal;\n    var emphasisItemStyleOpt = itemStyleOpt.emphasis;\n\n    if (normalItemStyleOpt && normalItemStyleOpt[styleName]) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(\"itemStyle.normal.\" + styleName, styleName);\n      }\n\n      opt[styleName] = opt[styleName] || {};\n\n      if (!opt[styleName].normal) {\n        opt[styleName].normal = normalItemStyleOpt[styleName];\n      } else {\n        zrUtil.merge(opt[styleName].normal, normalItemStyleOpt[styleName]);\n      }\n\n      normalItemStyleOpt[styleName] = null;\n    }\n\n    if (emphasisItemStyleOpt && emphasisItemStyleOpt[styleName]) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(\"itemStyle.emphasis.\" + styleName, \"emphasis.\" + styleName);\n      }\n\n      opt[styleName] = opt[styleName] || {};\n\n      if (!opt[styleName].emphasis) {\n        opt[styleName].emphasis = emphasisItemStyleOpt[styleName];\n      } else {\n        zrUtil.merge(opt[styleName].emphasis, emphasisItemStyleOpt[styleName]);\n      }\n\n      emphasisItemStyleOpt[styleName] = null;\n    }\n  }\n}\n\nfunction convertNormalEmphasis(opt, optType, useExtend) {\n  if (opt && opt[optType] && (opt[optType].normal || opt[optType].emphasis)) {\n    var normalOpt = opt[optType].normal;\n    var emphasisOpt = opt[optType].emphasis;\n\n    if (normalOpt) {\n      if (process.env.NODE_ENV !== 'production') {\n        // eslint-disable-next-line max-len\n        deprecateLog(\"'normal' hierarchy in \" + optType + \" has been removed since 4.0. All style properties are configured in \" + optType + \" directly now.\");\n      } // Timeline controlStyle has other properties besides normal and emphasis\n\n\n      if (useExtend) {\n        opt[optType].normal = opt[optType].emphasis = null;\n        zrUtil.defaults(opt[optType], normalOpt);\n      } else {\n        opt[optType] = normalOpt;\n      }\n    }\n\n    if (emphasisOpt) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog(optType + \".emphasis has been changed to emphasis.\" + optType + \" since 4.0\");\n      }\n\n      opt.emphasis = opt.emphasis || {};\n      opt.emphasis[optType] = emphasisOpt; // Also compat the case user mix the style and focus together in ec3 style\n      // for example: { itemStyle: { normal: {}, emphasis: {focus, shadowBlur} } }\n\n      if (emphasisOpt.focus) {\n        opt.emphasis.focus = emphasisOpt.focus;\n      }\n\n      if (emphasisOpt.blurScope) {\n        opt.emphasis.blurScope = emphasisOpt.blurScope;\n      }\n    }\n  }\n}\n\nfunction removeEC3NormalStatus(opt) {\n  convertNormalEmphasis(opt, 'itemStyle');\n  convertNormalEmphasis(opt, 'lineStyle');\n  convertNormalEmphasis(opt, 'areaStyle');\n  convertNormalEmphasis(opt, 'label');\n  convertNormalEmphasis(opt, 'labelLine'); // treemap\n\n  convertNormalEmphasis(opt, 'upperLabel'); // graph\n\n  convertNormalEmphasis(opt, 'edgeLabel');\n}\n\nfunction compatTextStyle(opt, propName) {\n  // Check whether is not object (string\\null\\undefined ...)\n  var labelOptSingle = isObject(opt) && opt[propName];\n  var textStyle = isObject(labelOptSingle) && labelOptSingle.textStyle;\n\n  if (textStyle) {\n    if (process.env.NODE_ENV !== 'production') {\n      // eslint-disable-next-line max-len\n      deprecateLog(\"textStyle hierarchy in \" + propName + \" has been removed since 4.0. All textStyle properties are configured in \" + propName + \" directly now.\");\n    }\n\n    for (var i = 0, len = modelUtil.TEXT_STYLE_OPTIONS.length; i < len; i++) {\n      var textPropName = modelUtil.TEXT_STYLE_OPTIONS[i];\n\n      if (textStyle.hasOwnProperty(textPropName)) {\n        labelOptSingle[textPropName] = textStyle[textPropName];\n      }\n    }\n  }\n}\n\nfunction compatEC3CommonStyles(opt) {\n  if (opt) {\n    removeEC3NormalStatus(opt);\n    compatTextStyle(opt, 'label');\n    opt.emphasis && compatTextStyle(opt.emphasis, 'label');\n  }\n}\n\nfunction processSeries(seriesOpt) {\n  if (!isObject(seriesOpt)) {\n    return;\n  }\n\n  compatEC2ItemStyle(seriesOpt);\n  removeEC3NormalStatus(seriesOpt);\n  compatTextStyle(seriesOpt, 'label'); // treemap\n\n  compatTextStyle(seriesOpt, 'upperLabel'); // graph\n\n  compatTextStyle(seriesOpt, 'edgeLabel');\n\n  if (seriesOpt.emphasis) {\n    compatTextStyle(seriesOpt.emphasis, 'label'); // treemap\n\n    compatTextStyle(seriesOpt.emphasis, 'upperLabel'); // graph\n\n    compatTextStyle(seriesOpt.emphasis, 'edgeLabel');\n  }\n\n  var markPoint = seriesOpt.markPoint;\n\n  if (markPoint) {\n    compatEC2ItemStyle(markPoint);\n    compatEC3CommonStyles(markPoint);\n  }\n\n  var markLine = seriesOpt.markLine;\n\n  if (markLine) {\n    compatEC2ItemStyle(markLine);\n    compatEC3CommonStyles(markLine);\n  }\n\n  var markArea = seriesOpt.markArea;\n\n  if (markArea) {\n    compatEC3CommonStyles(markArea);\n  }\n\n  var data = seriesOpt.data; // Break with ec3: if `setOption` again, there may be no `type` in option,\n  // then the backward compat based on option type will not be performed.\n\n  if (seriesOpt.type === 'graph') {\n    data = data || seriesOpt.nodes;\n    var edgeData = seriesOpt.links || seriesOpt.edges;\n\n    if (edgeData && !zrUtil.isTypedArray(edgeData)) {\n      for (var i = 0; i < edgeData.length; i++) {\n        compatEC3CommonStyles(edgeData[i]);\n      }\n    }\n\n    zrUtil.each(seriesOpt.categories, function (opt) {\n      removeEC3NormalStatus(opt);\n    });\n  }\n\n  if (data && !zrUtil.isTypedArray(data)) {\n    for (var i = 0; i < data.length; i++) {\n      compatEC3CommonStyles(data[i]);\n    }\n  } // mark point data\n\n\n  markPoint = seriesOpt.markPoint;\n\n  if (markPoint && markPoint.data) {\n    var mpData = markPoint.data;\n\n    for (var i = 0; i < mpData.length; i++) {\n      compatEC3CommonStyles(mpData[i]);\n    }\n  } // mark line data\n\n\n  markLine = seriesOpt.markLine;\n\n  if (markLine && markLine.data) {\n    var mlData = markLine.data;\n\n    for (var i = 0; i < mlData.length; i++) {\n      if (zrUtil.isArray(mlData[i])) {\n        compatEC3CommonStyles(mlData[i][0]);\n        compatEC3CommonStyles(mlData[i][1]);\n      } else {\n        compatEC3CommonStyles(mlData[i]);\n      }\n    }\n  } // Series\n\n\n  if (seriesOpt.type === 'gauge') {\n    compatTextStyle(seriesOpt, 'axisLabel');\n    compatTextStyle(seriesOpt, 'title');\n    compatTextStyle(seriesOpt, 'detail');\n  } else if (seriesOpt.type === 'treemap') {\n    convertNormalEmphasis(seriesOpt.breadcrumb, 'itemStyle');\n    zrUtil.each(seriesOpt.levels, function (opt) {\n      removeEC3NormalStatus(opt);\n    });\n  } else if (seriesOpt.type === 'tree') {\n    removeEC3NormalStatus(seriesOpt.leaves);\n  } // sunburst starts from ec4, so it does not need to compat levels.\n\n}\n\nfunction toArr(o) {\n  return zrUtil.isArray(o) ? o : o ? [o] : [];\n}\n\nfunction toObj(o) {\n  return (zrUtil.isArray(o) ? o[0] : o) || {};\n}\n\nexport default function globalCompatStyle(option, isTheme) {\n  each(toArr(option.series), function (seriesOpt) {\n    isObject(seriesOpt) && processSeries(seriesOpt);\n  });\n  var axes = ['xAxis', 'yAxis', 'radiusAxis', 'angleAxis', 'singleAxis', 'parallelAxis', 'radar'];\n  isTheme && axes.push('valueAxis', 'categoryAxis', 'logAxis', 'timeAxis');\n  each(axes, function (axisName) {\n    each(toArr(option[axisName]), function (axisOpt) {\n      if (axisOpt) {\n        compatTextStyle(axisOpt, 'axisLabel');\n        compatTextStyle(axisOpt.axisPointer, 'label');\n      }\n    });\n  });\n  each(toArr(option.parallel), function (parallelOpt) {\n    var parallelAxisDefault = parallelOpt && parallelOpt.parallelAxisDefault;\n    compatTextStyle(parallelAxisDefault, 'axisLabel');\n    compatTextStyle(parallelAxisDefault && parallelAxisDefault.axisPointer, 'label');\n  });\n  each(toArr(option.calendar), function (calendarOpt) {\n    convertNormalEmphasis(calendarOpt, 'itemStyle');\n    compatTextStyle(calendarOpt, 'dayLabel');\n    compatTextStyle(calendarOpt, 'monthLabel');\n    compatTextStyle(calendarOpt, 'yearLabel');\n  }); // radar.name.textStyle\n\n  each(toArr(option.radar), function (radarOpt) {\n    compatTextStyle(radarOpt, 'name'); // Use axisName instead of name because component has name property\n\n    if (radarOpt.name && radarOpt.axisName == null) {\n      radarOpt.axisName = radarOpt.name;\n      delete radarOpt.name;\n\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog('name property in radar component has been changed to axisName');\n      }\n    }\n\n    if (radarOpt.nameGap != null && radarOpt.axisNameGap == null) {\n      radarOpt.axisNameGap = radarOpt.nameGap;\n      delete radarOpt.nameGap;\n\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog('nameGap property in radar component has been changed to axisNameGap');\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      each(radarOpt.indicator, function (indicatorOpt) {\n        if (indicatorOpt.text) {\n          deprecateReplaceLog('text', 'name', 'radar.indicator');\n        }\n      });\n    }\n  });\n  each(toArr(option.geo), function (geoOpt) {\n    if (isObject(geoOpt)) {\n      compatEC3CommonStyles(geoOpt);\n      each(toArr(geoOpt.regions), function (regionObj) {\n        compatEC3CommonStyles(regionObj);\n      });\n    }\n  });\n  each(toArr(option.timeline), function (timelineOpt) {\n    compatEC3CommonStyles(timelineOpt);\n    convertNormalEmphasis(timelineOpt, 'label');\n    convertNormalEmphasis(timelineOpt, 'itemStyle');\n    convertNormalEmphasis(timelineOpt, 'controlStyle', true);\n    var data = timelineOpt.data;\n    zrUtil.isArray(data) && zrUtil.each(data, function (item) {\n      if (zrUtil.isObject(item)) {\n        convertNormalEmphasis(item, 'label');\n        convertNormalEmphasis(item, 'itemStyle');\n      }\n    });\n  });\n  each(toArr(option.toolbox), function (toolboxOpt) {\n    convertNormalEmphasis(toolboxOpt, 'iconStyle');\n    each(toolboxOpt.feature, function (featureOpt) {\n      convertNormalEmphasis(featureOpt, 'iconStyle');\n    });\n  });\n  compatTextStyle(toObj(option.axisPointer), 'label');\n  compatTextStyle(toObj(option.tooltip).axisPointer, 'label'); // Clean logs\n  // storedLogs = {};\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,mBAAmB;AACrE,IAAIC,IAAI,GAAGJ,MAAM,CAACI,IAAI;AACtB,IAAIC,QAAQ,GAAGL,MAAM,CAACK,QAAQ;AAC9B,IAAIC,eAAe,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC;AAE9G,SAASC,kBAAkBA,CAACC,GAAG,EAAE;EAC/B,IAAIC,YAAY,GAAGD,GAAG,IAAIA,GAAG,CAACE,SAAS;EAEvC,IAAI,CAACD,YAAY,EAAE;IACjB;EACF;EAEA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGN,eAAe,CAACO,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC1D,IAAIG,SAAS,GAAGR,eAAe,CAACK,CAAC,CAAC;IAClC,IAAII,kBAAkB,GAAGN,YAAY,CAACO,MAAM;IAC5C,IAAIC,oBAAoB,GAAGR,YAAY,CAACS,QAAQ;IAEhD,IAAIH,kBAAkB,IAAIA,kBAAkB,CAACD,SAAS,CAAC,EAAE;MACvD,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzClB,mBAAmB,CAAC,mBAAmB,GAAGW,SAAS,EAAEA,SAAS,CAAC;MACjE;MAEAN,GAAG,CAACM,SAAS,CAAC,GAAGN,GAAG,CAACM,SAAS,CAAC,IAAI,CAAC,CAAC;MAErC,IAAI,CAACN,GAAG,CAACM,SAAS,CAAC,CAACE,MAAM,EAAE;QAC1BR,GAAG,CAACM,SAAS,CAAC,CAACE,MAAM,GAAGD,kBAAkB,CAACD,SAAS,CAAC;MACvD,CAAC,MAAM;QACLd,MAAM,CAACsB,KAAK,CAACd,GAAG,CAACM,SAAS,CAAC,CAACE,MAAM,EAAED,kBAAkB,CAACD,SAAS,CAAC,CAAC;MACpE;MAEAC,kBAAkB,CAACD,SAAS,CAAC,GAAG,IAAI;IACtC;IAEA,IAAIG,oBAAoB,IAAIA,oBAAoB,CAACH,SAAS,CAAC,EAAE;MAC3D,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzClB,mBAAmB,CAAC,qBAAqB,GAAGW,SAAS,EAAE,WAAW,GAAGA,SAAS,CAAC;MACjF;MAEAN,GAAG,CAACM,SAAS,CAAC,GAAGN,GAAG,CAACM,SAAS,CAAC,IAAI,CAAC,CAAC;MAErC,IAAI,CAACN,GAAG,CAACM,SAAS,CAAC,CAACI,QAAQ,EAAE;QAC5BV,GAAG,CAACM,SAAS,CAAC,CAACI,QAAQ,GAAGD,oBAAoB,CAACH,SAAS,CAAC;MAC3D,CAAC,MAAM;QACLd,MAAM,CAACsB,KAAK,CAACd,GAAG,CAACM,SAAS,CAAC,CAACI,QAAQ,EAAED,oBAAoB,CAACH,SAAS,CAAC,CAAC;MACxE;MAEAG,oBAAoB,CAACH,SAAS,CAAC,GAAG,IAAI;IACxC;EACF;AACF;AAEA,SAASS,qBAAqBA,CAACf,GAAG,EAAEgB,OAAO,EAAEC,SAAS,EAAE;EACtD,IAAIjB,GAAG,IAAIA,GAAG,CAACgB,OAAO,CAAC,KAAKhB,GAAG,CAACgB,OAAO,CAAC,CAACR,MAAM,IAAIR,GAAG,CAACgB,OAAO,CAAC,CAACN,QAAQ,CAAC,EAAE;IACzE,IAAIQ,SAAS,GAAGlB,GAAG,CAACgB,OAAO,CAAC,CAACR,MAAM;IACnC,IAAIW,WAAW,GAAGnB,GAAG,CAACgB,OAAO,CAAC,CAACN,QAAQ;IAEvC,IAAIQ,SAAS,EAAE;MACb,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC;QACAnB,YAAY,CAAC,wBAAwB,GAAGsB,OAAO,GAAG,sEAAsE,GAAGA,OAAO,GAAG,gBAAgB,CAAC;MACxJ,CAAC,CAAC;;MAGF,IAAIC,SAAS,EAAE;QACbjB,GAAG,CAACgB,OAAO,CAAC,CAACR,MAAM,GAAGR,GAAG,CAACgB,OAAO,CAAC,CAACN,QAAQ,GAAG,IAAI;QAClDlB,MAAM,CAAC4B,QAAQ,CAACpB,GAAG,CAACgB,OAAO,CAAC,EAAEE,SAAS,CAAC;MAC1C,CAAC,MAAM;QACLlB,GAAG,CAACgB,OAAO,CAAC,GAAGE,SAAS;MAC1B;IACF;IAEA,IAAIC,WAAW,EAAE;MACf,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCnB,YAAY,CAACsB,OAAO,GAAG,yCAAyC,GAAGA,OAAO,GAAG,YAAY,CAAC;MAC5F;MAEAhB,GAAG,CAACU,QAAQ,GAAGV,GAAG,CAACU,QAAQ,IAAI,CAAC,CAAC;MACjCV,GAAG,CAACU,QAAQ,CAACM,OAAO,CAAC,GAAGG,WAAW,CAAC,CAAC;MACrC;;MAEA,IAAIA,WAAW,CAACE,KAAK,EAAE;QACrBrB,GAAG,CAACU,QAAQ,CAACW,KAAK,GAAGF,WAAW,CAACE,KAAK;MACxC;MAEA,IAAIF,WAAW,CAACG,SAAS,EAAE;QACzBtB,GAAG,CAACU,QAAQ,CAACY,SAAS,GAAGH,WAAW,CAACG,SAAS;MAChD;IACF;EACF;AACF;AAEA,SAASC,qBAAqBA,CAACvB,GAAG,EAAE;EAClCe,qBAAqB,CAACf,GAAG,EAAE,WAAW,CAAC;EACvCe,qBAAqB,CAACf,GAAG,EAAE,WAAW,CAAC;EACvCe,qBAAqB,CAACf,GAAG,EAAE,WAAW,CAAC;EACvCe,qBAAqB,CAACf,GAAG,EAAE,OAAO,CAAC;EACnCe,qBAAqB,CAACf,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC;;EAEzCe,qBAAqB,CAACf,GAAG,EAAE,YAAY,CAAC,CAAC,CAAC;;EAE1Ce,qBAAqB,CAACf,GAAG,EAAE,WAAW,CAAC;AACzC;AAEA,SAASwB,eAAeA,CAACxB,GAAG,EAAEyB,QAAQ,EAAE;EACtC;EACA,IAAIC,cAAc,GAAG7B,QAAQ,CAACG,GAAG,CAAC,IAAIA,GAAG,CAACyB,QAAQ,CAAC;EACnD,IAAIE,SAAS,GAAG9B,QAAQ,CAAC6B,cAAc,CAAC,IAAIA,cAAc,CAACC,SAAS;EAEpE,IAAIA,SAAS,EAAE;IACb,IAAIhB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACAnB,YAAY,CAAC,yBAAyB,GAAG+B,QAAQ,GAAG,0EAA0E,GAAGA,QAAQ,GAAG,gBAAgB,CAAC;IAC/J;IAEA,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGX,SAAS,CAACmC,kBAAkB,CAACvB,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACvE,IAAI0B,YAAY,GAAGpC,SAAS,CAACmC,kBAAkB,CAACzB,CAAC,CAAC;MAElD,IAAIwB,SAAS,CAACG,cAAc,CAACD,YAAY,CAAC,EAAE;QAC1CH,cAAc,CAACG,YAAY,CAAC,GAAGF,SAAS,CAACE,YAAY,CAAC;MACxD;IACF;EACF;AACF;AAEA,SAASE,qBAAqBA,CAAC/B,GAAG,EAAE;EAClC,IAAIA,GAAG,EAAE;IACPuB,qBAAqB,CAACvB,GAAG,CAAC;IAC1BwB,eAAe,CAACxB,GAAG,EAAE,OAAO,CAAC;IAC7BA,GAAG,CAACU,QAAQ,IAAIc,eAAe,CAACxB,GAAG,CAACU,QAAQ,EAAE,OAAO,CAAC;EACxD;AACF;AAEA,SAASsB,aAAaA,CAACC,SAAS,EAAE;EAChC,IAAI,CAACpC,QAAQ,CAACoC,SAAS,CAAC,EAAE;IACxB;EACF;EAEAlC,kBAAkB,CAACkC,SAAS,CAAC;EAC7BV,qBAAqB,CAACU,SAAS,CAAC;EAChCT,eAAe,CAACS,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;;EAErCT,eAAe,CAACS,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;;EAE1CT,eAAe,CAACS,SAAS,EAAE,WAAW,CAAC;EAEvC,IAAIA,SAAS,CAACvB,QAAQ,EAAE;IACtBc,eAAe,CAACS,SAAS,CAACvB,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;;IAE9Cc,eAAe,CAACS,SAAS,CAACvB,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;;IAEnDc,eAAe,CAACS,SAAS,CAACvB,QAAQ,EAAE,WAAW,CAAC;EAClD;EAEA,IAAIwB,SAAS,GAAGD,SAAS,CAACC,SAAS;EAEnC,IAAIA,SAAS,EAAE;IACbnC,kBAAkB,CAACmC,SAAS,CAAC;IAC7BH,qBAAqB,CAACG,SAAS,CAAC;EAClC;EAEA,IAAIC,QAAQ,GAAGF,SAAS,CAACE,QAAQ;EAEjC,IAAIA,QAAQ,EAAE;IACZpC,kBAAkB,CAACoC,QAAQ,CAAC;IAC5BJ,qBAAqB,CAACI,QAAQ,CAAC;EACjC;EAEA,IAAIC,QAAQ,GAAGH,SAAS,CAACG,QAAQ;EAEjC,IAAIA,QAAQ,EAAE;IACZL,qBAAqB,CAACK,QAAQ,CAAC;EACjC;EAEA,IAAIC,IAAI,GAAGJ,SAAS,CAACI,IAAI,CAAC,CAAC;EAC3B;;EAEA,IAAIJ,SAAS,CAACK,IAAI,KAAK,OAAO,EAAE;IAC9BD,IAAI,GAAGA,IAAI,IAAIJ,SAAS,CAACM,KAAK;IAC9B,IAAIC,QAAQ,GAAGP,SAAS,CAACQ,KAAK,IAAIR,SAAS,CAACS,KAAK;IAEjD,IAAIF,QAAQ,IAAI,CAAChD,MAAM,CAACmD,YAAY,CAACH,QAAQ,CAAC,EAAE;MAC9C,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,QAAQ,CAACnC,MAAM,EAAEF,CAAC,EAAE,EAAE;QACxC4B,qBAAqB,CAACS,QAAQ,CAACrC,CAAC,CAAC,CAAC;MACpC;IACF;IAEAX,MAAM,CAACI,IAAI,CAACqC,SAAS,CAACW,UAAU,EAAE,UAAU5C,GAAG,EAAE;MAC/CuB,qBAAqB,CAACvB,GAAG,CAAC;IAC5B,CAAC,CAAC;EACJ;EAEA,IAAIqC,IAAI,IAAI,CAAC7C,MAAM,CAACmD,YAAY,CAACN,IAAI,CAAC,EAAE;IACtC,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,IAAI,CAAChC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACpC4B,qBAAqB,CAACM,IAAI,CAAClC,CAAC,CAAC,CAAC;IAChC;EACF,CAAC,CAAC;;EAGF+B,SAAS,GAAGD,SAAS,CAACC,SAAS;EAE/B,IAAIA,SAAS,IAAIA,SAAS,CAACG,IAAI,EAAE;IAC/B,IAAIQ,MAAM,GAAGX,SAAS,CAACG,IAAI;IAE3B,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,MAAM,CAACxC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACtC4B,qBAAqB,CAACc,MAAM,CAAC1C,CAAC,CAAC,CAAC;IAClC;EACF,CAAC,CAAC;;EAGFgC,QAAQ,GAAGF,SAAS,CAACE,QAAQ;EAE7B,IAAIA,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;IAC7B,IAAIS,MAAM,GAAGX,QAAQ,CAACE,IAAI;IAE1B,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,MAAM,CAACzC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACtC,IAAIX,MAAM,CAACuD,OAAO,CAACD,MAAM,CAAC3C,CAAC,CAAC,CAAC,EAAE;QAC7B4B,qBAAqB,CAACe,MAAM,CAAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC4B,qBAAqB,CAACe,MAAM,CAAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,MAAM;QACL4B,qBAAqB,CAACe,MAAM,CAAC3C,CAAC,CAAC,CAAC;MAClC;IACF;EACF,CAAC,CAAC;;EAGF,IAAI8B,SAAS,CAACK,IAAI,KAAK,OAAO,EAAE;IAC9Bd,eAAe,CAACS,SAAS,EAAE,WAAW,CAAC;IACvCT,eAAe,CAACS,SAAS,EAAE,OAAO,CAAC;IACnCT,eAAe,CAACS,SAAS,EAAE,QAAQ,CAAC;EACtC,CAAC,MAAM,IAAIA,SAAS,CAACK,IAAI,KAAK,SAAS,EAAE;IACvCvB,qBAAqB,CAACkB,SAAS,CAACe,UAAU,EAAE,WAAW,CAAC;IACxDxD,MAAM,CAACI,IAAI,CAACqC,SAAS,CAACgB,MAAM,EAAE,UAAUjD,GAAG,EAAE;MAC3CuB,qBAAqB,CAACvB,GAAG,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIiC,SAAS,CAACK,IAAI,KAAK,MAAM,EAAE;IACpCf,qBAAqB,CAACU,SAAS,CAACiB,MAAM,CAAC;EACzC,CAAC,CAAC;AAEJ;AAEA,SAASC,KAAKA,CAACC,CAAC,EAAE;EAChB,OAAO5D,MAAM,CAACuD,OAAO,CAACK,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC,CAAC,GAAG,EAAE;AAC7C;AAEA,SAASC,KAAKA,CAACD,CAAC,EAAE;EAChB,OAAO,CAAC5D,MAAM,CAACuD,OAAO,CAACK,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,KAAK,CAAC,CAAC;AAC7C;AAEA,eAAe,SAASE,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACzD5D,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACE,MAAM,CAAC,EAAE,UAAUxB,SAAS,EAAE;IAC9CpC,QAAQ,CAACoC,SAAS,CAAC,IAAID,aAAa,CAACC,SAAS,CAAC;EACjD,CAAC,CAAC;EACF,IAAIyB,IAAI,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,OAAO,CAAC;EAC/FF,OAAO,IAAIE,IAAI,CAACC,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,CAAC;EACxE/D,IAAI,CAAC8D,IAAI,EAAE,UAAUE,QAAQ,EAAE;IAC7BhE,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACK,QAAQ,CAAC,CAAC,EAAE,UAAUC,OAAO,EAAE;MAC/C,IAAIA,OAAO,EAAE;QACXrC,eAAe,CAACqC,OAAO,EAAE,WAAW,CAAC;QACrCrC,eAAe,CAACqC,OAAO,CAACC,WAAW,EAAE,OAAO,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFlE,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACQ,QAAQ,CAAC,EAAE,UAAUC,WAAW,EAAE;IAClD,IAAIC,mBAAmB,GAAGD,WAAW,IAAIA,WAAW,CAACC,mBAAmB;IACxEzC,eAAe,CAACyC,mBAAmB,EAAE,WAAW,CAAC;IACjDzC,eAAe,CAACyC,mBAAmB,IAAIA,mBAAmB,CAACH,WAAW,EAAE,OAAO,CAAC;EAClF,CAAC,CAAC;EACFlE,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACW,QAAQ,CAAC,EAAE,UAAUC,WAAW,EAAE;IAClDpD,qBAAqB,CAACoD,WAAW,EAAE,WAAW,CAAC;IAC/C3C,eAAe,CAAC2C,WAAW,EAAE,UAAU,CAAC;IACxC3C,eAAe,CAAC2C,WAAW,EAAE,YAAY,CAAC;IAC1C3C,eAAe,CAAC2C,WAAW,EAAE,WAAW,CAAC;EAC3C,CAAC,CAAC,CAAC,CAAC;;EAEJvE,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACa,KAAK,CAAC,EAAE,UAAUC,QAAQ,EAAE;IAC5C7C,eAAe,CAAC6C,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEnC,IAAIA,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACT,QAAQ,IAAI,IAAI,EAAE;MAC9CS,QAAQ,CAACT,QAAQ,GAAGS,QAAQ,CAACC,IAAI;MACjC,OAAOD,QAAQ,CAACC,IAAI;MAEpB,IAAI3D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCnB,YAAY,CAAC,+DAA+D,CAAC;MAC/E;IACF;IAEA,IAAI2E,QAAQ,CAACE,OAAO,IAAI,IAAI,IAAIF,QAAQ,CAACG,WAAW,IAAI,IAAI,EAAE;MAC5DH,QAAQ,CAACG,WAAW,GAAGH,QAAQ,CAACE,OAAO;MACvC,OAAOF,QAAQ,CAACE,OAAO;MAEvB,IAAI5D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCnB,YAAY,CAAC,qEAAqE,CAAC;MACrF;IACF;IAEA,IAAIiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCjB,IAAI,CAACyE,QAAQ,CAACI,SAAS,EAAE,UAAUC,YAAY,EAAE;QAC/C,IAAIA,YAAY,CAACC,IAAI,EAAE;UACrBhF,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,iBAAiB,CAAC;QACxD;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACFC,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACqB,GAAG,CAAC,EAAE,UAAUC,MAAM,EAAE;IACxC,IAAIhF,QAAQ,CAACgF,MAAM,CAAC,EAAE;MACpB9C,qBAAqB,CAAC8C,MAAM,CAAC;MAC7BjF,IAAI,CAACuD,KAAK,CAAC0B,MAAM,CAACC,OAAO,CAAC,EAAE,UAAUC,SAAS,EAAE;QAC/ChD,qBAAqB,CAACgD,SAAS,CAAC;MAClC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACFnF,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACyB,QAAQ,CAAC,EAAE,UAAUC,WAAW,EAAE;IAClDlD,qBAAqB,CAACkD,WAAW,CAAC;IAClClE,qBAAqB,CAACkE,WAAW,EAAE,OAAO,CAAC;IAC3ClE,qBAAqB,CAACkE,WAAW,EAAE,WAAW,CAAC;IAC/ClE,qBAAqB,CAACkE,WAAW,EAAE,cAAc,EAAE,IAAI,CAAC;IACxD,IAAI5C,IAAI,GAAG4C,WAAW,CAAC5C,IAAI;IAC3B7C,MAAM,CAACuD,OAAO,CAACV,IAAI,CAAC,IAAI7C,MAAM,CAACI,IAAI,CAACyC,IAAI,EAAE,UAAU6C,IAAI,EAAE;MACxD,IAAI1F,MAAM,CAACK,QAAQ,CAACqF,IAAI,CAAC,EAAE;QACzBnE,qBAAqB,CAACmE,IAAI,EAAE,OAAO,CAAC;QACpCnE,qBAAqB,CAACmE,IAAI,EAAE,WAAW,CAAC;MAC1C;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFtF,IAAI,CAACuD,KAAK,CAACI,MAAM,CAAC4B,OAAO,CAAC,EAAE,UAAUC,UAAU,EAAE;IAChDrE,qBAAqB,CAACqE,UAAU,EAAE,WAAW,CAAC;IAC9CxF,IAAI,CAACwF,UAAU,CAACC,OAAO,EAAE,UAAUC,UAAU,EAAE;MAC7CvE,qBAAqB,CAACuE,UAAU,EAAE,WAAW,CAAC;IAChD,CAAC,CAAC;EACJ,CAAC,CAAC;EACF9D,eAAe,CAAC6B,KAAK,CAACE,MAAM,CAACO,WAAW,CAAC,EAAE,OAAO,CAAC;EACnDtC,eAAe,CAAC6B,KAAK,CAACE,MAAM,CAACgC,OAAO,CAAC,CAACzB,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;EAC7D;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module"}