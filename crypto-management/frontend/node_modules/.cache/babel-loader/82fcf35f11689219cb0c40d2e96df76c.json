{"ast": null, "code": "import defaultLocale from \"../locale/en-US/index.js\";\nvar defaultFormat = ['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds'];\n/**\n * @name formatDuration\n * @category Common Helpers\n * @summary Formats a duration in human-readable format\n *\n * @description\n * Return human-readable duration string i.e. \"9 months 2 days\"\n *\n * @param {Duration} duration - the duration to format\n * @param {Object} [options] - an object with options.\n\n * @param {string[]} [options.format=['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds']] - the array of units to format\n * @param {boolean} [options.zero=false] - should be zeros be included in the output?\n * @param {string} [options.delimiter=' '] - delimiter string\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {string} the formatted date string\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Format full duration\n * formatDuration({\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> '2 years 9 months 1 week 7 days 5 hours 9 minutes 30 seconds\n *\n * @example\n * // Format partial duration\n * formatDuration({ months: 9, days: 2 })\n * //=> '9 months 2 days'\n *\n * @example\n * // Customize the format\n * formatDuration(\n *   {\n *     years: 2,\n *     months: 9,\n *     weeks: 1,\n *     days: 7,\n *     hours: 5,\n *     minutes: 9,\n *     seconds: 30\n *   },\n *   { format: ['months', 'weeks'] }\n * ) === '9 months 1 week'\n *\n * @example\n * // Customize the zeros presence\n * formatDuration({ years: 0, months: 9 })\n * //=> '9 months'\n * formatDuration({ years: 0, months: 9 }, { zero: true })\n * //=> '0 years 9 months'\n *\n * @example\n * // Customize the delimiter\n * formatDuration({ years: 2, months: 9, weeks: 3 }, { delimiter: ', ' })\n * //=> '2 years, 9 months, 3 weeks'\n */\n\nexport default function formatDuration(duration, options) {\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 argument required, but only \".concat(arguments.length, \" present\"));\n  }\n  var format = (options === null || options === void 0 ? void 0 : options.format) || defaultFormat;\n  var locale = (options === null || options === void 0 ? void 0 : options.locale) || defaultLocale;\n  var zero = (options === null || options === void 0 ? void 0 : options.zero) || false;\n  var delimiter = (options === null || options === void 0 ? void 0 : options.delimiter) || ' ';\n  var result = format.reduce(function (acc, unit) {\n    var token = \"x\".concat(unit.replace(/(^.)/, function (m) {\n      return m.toUpperCase();\n    }));\n    var addChunk = typeof duration[unit] === 'number' && (zero || duration[unit]);\n    return addChunk ? acc.concat(locale.formatDistance(token, duration[unit])) : acc;\n  }, []).join(delimiter);\n  return result;\n}", "map": {"version": 3, "names": ["defaultLocale", "defaultFormat", "formatDuration", "duration", "options", "arguments", "length", "TypeError", "concat", "format", "locale", "zero", "delimiter", "result", "reduce", "acc", "unit", "token", "replace", "m", "toUpperCase", "addChunk", "formatDistance", "join"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/formatDuration/index.js"], "sourcesContent": ["import defaultLocale from \"../locale/en-US/index.js\";\nvar defaultFormat = ['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds'];\n/**\n * @name formatDuration\n * @category Common Helpers\n * @summary Formats a duration in human-readable format\n *\n * @description\n * Return human-readable duration string i.e. \"9 months 2 days\"\n *\n * @param {Duration} duration - the duration to format\n * @param {Object} [options] - an object with options.\n\n * @param {string[]} [options.format=['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds']] - the array of units to format\n * @param {boolean} [options.zero=false] - should be zeros be included in the output?\n * @param {string} [options.delimiter=' '] - delimiter string\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @returns {string} the formatted date string\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Format full duration\n * formatDuration({\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> '2 years 9 months 1 week 7 days 5 hours 9 minutes 30 seconds\n *\n * @example\n * // Format partial duration\n * formatDuration({ months: 9, days: 2 })\n * //=> '9 months 2 days'\n *\n * @example\n * // Customize the format\n * formatDuration(\n *   {\n *     years: 2,\n *     months: 9,\n *     weeks: 1,\n *     days: 7,\n *     hours: 5,\n *     minutes: 9,\n *     seconds: 30\n *   },\n *   { format: ['months', 'weeks'] }\n * ) === '9 months 1 week'\n *\n * @example\n * // Customize the zeros presence\n * formatDuration({ years: 0, months: 9 })\n * //=> '9 months'\n * formatDuration({ years: 0, months: 9 }, { zero: true })\n * //=> '0 years 9 months'\n *\n * @example\n * // Customize the delimiter\n * formatDuration({ years: 2, months: 9, weeks: 3 }, { delimiter: ', ' })\n * //=> '2 years, 9 months, 3 weeks'\n */\n\nexport default function formatDuration(duration, options) {\n  if (arguments.length < 1) {\n    throw new TypeError(\"1 argument required, but only \".concat(arguments.length, \" present\"));\n  }\n\n  var format = (options === null || options === void 0 ? void 0 : options.format) || defaultFormat;\n  var locale = (options === null || options === void 0 ? void 0 : options.locale) || defaultLocale;\n  var zero = (options === null || options === void 0 ? void 0 : options.zero) || false;\n  var delimiter = (options === null || options === void 0 ? void 0 : options.delimiter) || ' ';\n  var result = format.reduce(function (acc, unit) {\n    var token = \"x\".concat(unit.replace(/(^.)/, function (m) {\n      return m.toUpperCase();\n    }));\n    var addChunk = typeof duration[unit] === 'number' && (zero || duration[unit]);\n    return addChunk ? acc.concat(locale.formatDistance(token, duration[unit])) : acc;\n  }, []).join(delimiter);\n  return result;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0BAA0B;AACpD,IAAIC,aAAa,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,cAAcA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACxD,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,SAAS,CAAC,gCAAgC,CAACC,MAAM,CAACH,SAAS,CAACC,MAAM,EAAE,UAAU,CAAC,CAAC;EAC5F;EAEA,IAAIG,MAAM,GAAG,CAACL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,MAAM,KAAKR,aAAa;EAChG,IAAIS,MAAM,GAAG,CAACN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACM,MAAM,KAAKV,aAAa;EAChG,IAAIW,IAAI,GAAG,CAACP,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACO,IAAI,KAAK,KAAK;EACpF,IAAIC,SAAS,GAAG,CAACR,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACQ,SAAS,KAAK,GAAG;EAC5F,IAAIC,MAAM,GAAGJ,MAAM,CAACK,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;IAC9C,IAAIC,KAAK,GAAG,GAAG,CAACT,MAAM,CAACQ,IAAI,CAACE,OAAO,CAAC,MAAM,EAAE,UAAUC,CAAC,EAAE;MACvD,OAAOA,CAAC,CAACC,WAAW,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;IACH,IAAIC,QAAQ,GAAG,OAAOlB,QAAQ,CAACa,IAAI,CAAC,KAAK,QAAQ,KAAKL,IAAI,IAAIR,QAAQ,CAACa,IAAI,CAAC,CAAC;IAC7E,OAAOK,QAAQ,GAAGN,GAAG,CAACP,MAAM,CAACE,MAAM,CAACY,cAAc,CAACL,KAAK,EAAEd,QAAQ,CAACa,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG;EAClF,CAAC,EAAE,EAAE,CAAC,CAACQ,IAAI,CAACX,SAAS,CAAC;EACtB,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}