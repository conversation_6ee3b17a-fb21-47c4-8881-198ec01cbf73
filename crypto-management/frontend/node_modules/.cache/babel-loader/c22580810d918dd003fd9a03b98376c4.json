{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { capitalCase } from \"capital-case\";\nexport function headerCase(input, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return capitalCase(input, __assign({\n    delimiter: \"-\"\n  }, options));\n}", "map": {"version": 3, "names": ["capitalCase", "headerCase", "input", "options", "__assign", "delimiter"], "sources": ["../src/index.ts"], "sourcesContent": ["import { capitalCase, Options } from \"capital-case\";\n\nexport { Options };\n\nexport function headerCase(input: string, options: Options = {}) {\n  return capitalCase(input, {\n    delimiter: \"-\",\n    ...options,\n  });\n}\n"], "mappings": ";AAAA,SAASA,WAAW,QAAiB,cAAc;AAInD,OAAM,SAAUC,UAAUA,CAACC,KAAa,EAAEC,OAAqB;EAArB,IAAAA,OAAA;IAAAA,OAAA,KAAqB;EAAA;EAC7D,OAAOH,WAAW,CAACE,KAAK,EAAAE,QAAA;IACtBC,SAAS,EAAE;EAAG,GACXF,OAAO,EACV;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}