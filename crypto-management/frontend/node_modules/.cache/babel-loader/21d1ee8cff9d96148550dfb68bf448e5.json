{"ast": null, "code": "import max from \"../max/index.js\";\nimport min from \"../min/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name clamp\n * @category Interval Helpers\n * @summary Return a date bounded by the start and the end of the given interval\n *\n * @description\n * Clamps a date to the lower bound with the start of the interval and the upper\n * bound with the end of the interval.\n *\n * - When the date is less than the start of the interval, the start is returned.\n * - When the date is greater than the end of the interval, the end is returned.\n * - Otherwise the date is returned.\n *\n * @example\n * // What is Mar, 21, 2021 bounded to an interval starting at Mar, 22, 2021 and ending at Apr, 01, 2021\n * const result = clamp(new Date(2021, 2, 21), {\n *   start: new Date(2021, 2, 22),\n *   end: new Date(2021, 3, 1),\n * })\n * //=> Mon Mar 22 2021 00:00:00\n *\n * @param {Date | Number} date - the date to be bounded\n * @param {Interval} interval - the interval to bound to\n * @returns {Date} the date bounded by the start and the end of the interval\n * @throws {TypeError} 2 arguments required\n */\n\nexport default function clamp(date, _ref) {\n  var start = _ref.start,\n    end = _ref.end;\n  requiredArgs(2, arguments);\n  return min([max([date, start]), end]);\n}", "map": {"version": 3, "names": ["max", "min", "requiredArgs", "clamp", "date", "_ref", "start", "end", "arguments"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/clamp/index.js"], "sourcesContent": ["import max from \"../max/index.js\";\nimport min from \"../min/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name clamp\n * @category Interval Helpers\n * @summary Return a date bounded by the start and the end of the given interval\n *\n * @description\n * Clamps a date to the lower bound with the start of the interval and the upper\n * bound with the end of the interval.\n *\n * - When the date is less than the start of the interval, the start is returned.\n * - When the date is greater than the end of the interval, the end is returned.\n * - Otherwise the date is returned.\n *\n * @example\n * // What is Mar, 21, 2021 bounded to an interval starting at Mar, 22, 2021 and ending at Apr, 01, 2021\n * const result = clamp(new Date(2021, 2, 21), {\n *   start: new Date(2021, 2, 22),\n *   end: new Date(2021, 3, 1),\n * })\n * //=> Mon Mar 22 2021 00:00:00\n *\n * @param {Date | Number} date - the date to be bounded\n * @param {Interval} interval - the interval to bound to\n * @returns {Date} the date bounded by the start and the end of the interval\n * @throws {TypeError} 2 arguments required\n */\n\nexport default function clamp(date, _ref) {\n  var start = _ref.start,\n      end = _ref.end;\n  requiredArgs(2, arguments);\n  return min([max([date, start]), end]);\n}"], "mappings": "AAAA,OAAOA,GAAG,MAAM,iBAAiB;AACjC,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,KAAKA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACxC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,GAAG,GAAGF,IAAI,CAACE,GAAG;EAClBL,YAAY,CAAC,CAAC,EAAEM,SAAS,CAAC;EAC1B,OAAOP,GAAG,CAAC,CAACD,GAAG,CAAC,CAACI,IAAI,EAAEE,KAAK,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}