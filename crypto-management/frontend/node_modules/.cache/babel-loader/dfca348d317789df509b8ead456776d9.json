{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to round\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.nearestTo=1] - nearest number of minutes to round to. E.g. `15` to round to quarter hours.\n * @returns {Date} the new date rounded to the closest minute\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.nearestTo` must be between 1 and 30\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * var result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:07:30 to nearest quarter hour:\n * var result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * // rounds up because given date is exactly between 12:00:00 and 12:15:00\n * //=> Thu Jul 10 2014 12:15:00\n */\n\nexport default function roundToNearestMinutes(dirtyDate, options) {\n  if (arguments.length < 1) {\n    throw new TypeError('1 argument required, but only none provided present');\n  }\n  var nearestTo = options && 'nearestTo' in options ? toInteger(options.nearestTo) : 1;\n  if (nearestTo < 1 || nearestTo > 30) {\n    throw new RangeError('`options.nearestTo` must be between 1 and 30');\n  }\n  var date = toDate(dirtyDate);\n  var seconds = date.getSeconds(); // relevant if nearestTo is 1, which is the default case\n\n  var minutes = date.getMinutes() + seconds / 60;\n  var roundedMinutes = Math.floor(minutes / nearestTo) * nearestTo;\n  var remainderMinutes = minutes % nearestTo;\n  var addedMinutes = Math.round(remainderMinutes / nearestTo) * nearestTo;\n  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), roundedMinutes + addedMinutes);\n}", "map": {"version": 3, "names": ["toDate", "toInteger", "roundToNearestMinutes", "dirtyDate", "options", "arguments", "length", "TypeError", "nearestTo", "RangeError", "date", "seconds", "getSeconds", "minutes", "getMinutes", "roundedMinutes", "Math", "floor", "remainderMinutes", "addedMinutes", "round", "Date", "getFullYear", "getMonth", "getDate", "getHours"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/roundToNearestMinutes/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to round\n * @param {Object} [options] - an object with options.\n * @param {Number} [options.nearestTo=1] - nearest number of minutes to round to. E.g. `15` to round to quarter hours.\n * @returns {Date} the new date rounded to the closest minute\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.nearestTo` must be between 1 and 30\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * var result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:07:30 to nearest quarter hour:\n * var result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * // rounds up because given date is exactly between 12:00:00 and 12:15:00\n * //=> Thu Jul 10 2014 12:15:00\n */\n\nexport default function roundToNearestMinutes(dirtyDate, options) {\n  if (arguments.length < 1) {\n    throw new TypeError('1 argument required, but only none provided present');\n  }\n\n  var nearestTo = options && 'nearestTo' in options ? toInteger(options.nearestTo) : 1;\n\n  if (nearestTo < 1 || nearestTo > 30) {\n    throw new RangeError('`options.nearestTo` must be between 1 and 30');\n  }\n\n  var date = toDate(dirtyDate);\n  var seconds = date.getSeconds(); // relevant if nearestTo is 1, which is the default case\n\n  var minutes = date.getMinutes() + seconds / 60;\n  var roundedMinutes = Math.floor(minutes / nearestTo) * nearestTo;\n  var remainderMinutes = minutes % nearestTo;\n  var addedMinutes = Math.round(remainderMinutes / nearestTo) * nearestTo;\n  return new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), roundedMinutes + addedMinutes);\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,SAAS,MAAM,4BAA4B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,qBAAqBA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAChE,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;IACxB,MAAM,IAAIC,SAAS,CAAC,qDAAqD,CAAC;EAC5E;EAEA,IAAIC,SAAS,GAAGJ,OAAO,IAAI,WAAW,IAAIA,OAAO,GAAGH,SAAS,CAACG,OAAO,CAACI,SAAS,CAAC,GAAG,CAAC;EAEpF,IAAIA,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,EAAE,EAAE;IACnC,MAAM,IAAIC,UAAU,CAAC,8CAA8C,CAAC;EACtE;EAEA,IAAIC,IAAI,GAAGV,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIQ,OAAO,GAAGD,IAAI,CAACE,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEjC,IAAIC,OAAO,GAAGH,IAAI,CAACI,UAAU,CAAC,CAAC,GAAGH,OAAO,GAAG,EAAE;EAC9C,IAAII,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACJ,OAAO,GAAGL,SAAS,CAAC,GAAGA,SAAS;EAChE,IAAIU,gBAAgB,GAAGL,OAAO,GAAGL,SAAS;EAC1C,IAAIW,YAAY,GAAGH,IAAI,CAACI,KAAK,CAACF,gBAAgB,GAAGV,SAAS,CAAC,GAAGA,SAAS;EACvE,OAAO,IAAIa,IAAI,CAACX,IAAI,CAACY,WAAW,CAAC,CAAC,EAAEZ,IAAI,CAACa,QAAQ,CAAC,CAAC,EAAEb,IAAI,CAACc,OAAO,CAAC,CAAC,EAAEd,IAAI,CAACe,QAAQ,CAAC,CAAC,EAAEV,cAAc,GAAGI,YAAY,CAAC;AACtH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}