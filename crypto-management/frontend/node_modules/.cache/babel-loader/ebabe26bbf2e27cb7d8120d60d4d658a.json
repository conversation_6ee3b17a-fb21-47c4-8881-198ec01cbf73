{"ast": null, "code": "import startOfMinute from \"../startOfMinute/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameMinute\n * @category Minute Helpers\n * @summary Are the given dates in the same minute?\n *\n * @description\n * Are the given dates in the same minute?\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same minute\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 4 September 2014 06:30:15\n * // in the same minute?\n * var result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 4, 6, 30, 15)\n * )\n * //=> true\n */\n\nexport default function isSameMinute(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfMinute = startOfMinute(dirtyDateLeft);\n  var dateRightStartOfMinute = startOfMinute(dirtyDateRight);\n  return dateLeftStartOfMinute.getTime() === dateRightStartOfMinute.getTime();\n}", "map": {"version": 3, "names": ["startOfMinute", "requiredArgs", "isSameMinute", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeftStartOfMinute", "dateRightStartOfMinute", "getTime"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/isSameMinute/index.js"], "sourcesContent": ["import startOfMinute from \"../startOfMinute/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameMinute\n * @category Minute Helpers\n * @summary Are the given dates in the same minute?\n *\n * @description\n * Are the given dates in the same minute?\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same minute\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 4 September 2014 06:30:15\n * // in the same minute?\n * var result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 4, 6, 30, 15)\n * )\n * //=> true\n */\n\nexport default function isSameMinute(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfMinute = startOfMinute(dirtyDateLeft);\n  var dateRightStartOfMinute = startOfMinute(dirtyDateRight);\n  return dateLeftStartOfMinute.getTime() === dateRightStartOfMinute.getTime();\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,2BAA2B;AACrD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,YAAYA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAClEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,qBAAqB,GAAGN,aAAa,CAACG,aAAa,CAAC;EACxD,IAAII,sBAAsB,GAAGP,aAAa,CAACI,cAAc,CAAC;EAC1D,OAAOE,qBAAqB,CAACE,OAAO,CAAC,CAAC,KAAKD,sBAAsB,CAACC,OAAO,CAAC,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module"}