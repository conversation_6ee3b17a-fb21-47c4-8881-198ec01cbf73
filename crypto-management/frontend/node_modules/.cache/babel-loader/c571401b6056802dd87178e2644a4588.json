{"ast": null, "code": "/*\r\n * The MIT License (MIT)\r\n *\r\n * Copyright (c) 2014 <PERSON> <<EMAIL>>\r\n *\r\n * Permission is hereby granted, free of charge, to any person obtaining a copy\r\n * of this software and associated documentation files (the \"Software\"), to deal\r\n * in the Software without restriction, including without limitation the rights\r\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\n * copies of the Software, and to permit persons to whom the Software is\r\n * furnished to do so, subject to the following conditions:\r\n *\r\n * The above copyright notice and this permission notice shall be included in all\r\n * copies or substantial portions of the Software.\r\n *\r\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\r\n * SOFTWARE.\r\n */\n\n(function (global, undefined) {\n  \"use strict\";\n\n  var POW_2_24 = Math.pow(2, -24),\n    POW_2_32 = Math.pow(2, 32),\n    POW_2_53 = Math.pow(2, 53);\n  function encode(value) {\n    var data = new ArrayBuffer(256);\n    var dataView = new DataView(data);\n    var lastLength;\n    var offset = 0;\n    function ensureSpace(length) {\n      var newByteLength = data.byteLength;\n      var requiredLength = offset + length;\n      while (newByteLength < requiredLength) newByteLength *= 2;\n      if (newByteLength !== data.byteLength) {\n        var oldDataView = dataView;\n        data = new ArrayBuffer(newByteLength);\n        dataView = new DataView(data);\n        var uint32count = offset + 3 >> 2;\n        for (var i = 0; i < uint32count; ++i) dataView.setUint32(i * 4, oldDataView.getUint32(i * 4));\n      }\n      lastLength = length;\n      return dataView;\n    }\n    function write() {\n      offset += lastLength;\n    }\n    function writeFloat64(value) {\n      write(ensureSpace(8).setFloat64(offset, value));\n    }\n    function writeUint8(value) {\n      write(ensureSpace(1).setUint8(offset, value));\n    }\n    function writeUint8Array(value) {\n      var dataView = ensureSpace(value.length);\n      for (var i = 0; i < value.length; ++i) dataView.setUint8(offset + i, value[i]);\n      write();\n    }\n    function writeUint16(value) {\n      write(ensureSpace(2).setUint16(offset, value));\n    }\n    function writeUint32(value) {\n      write(ensureSpace(4).setUint32(offset, value));\n    }\n    function writeUint64(value) {\n      var low = value % POW_2_32;\n      var high = (value - low) / POW_2_32;\n      var dataView = ensureSpace(8);\n      dataView.setUint32(offset, high);\n      dataView.setUint32(offset + 4, low);\n      write();\n    }\n    function writeTypeAndLength(type, length) {\n      if (length < 24) {\n        writeUint8(type << 5 | length);\n      } else if (length < 0x100) {\n        writeUint8(type << 5 | 24);\n        writeUint8(length);\n      } else if (length < 0x10000) {\n        writeUint8(type << 5 | 25);\n        writeUint16(length);\n      } else if (length < 0x100000000) {\n        writeUint8(type << 5 | 26);\n        writeUint32(length);\n      } else {\n        writeUint8(type << 5 | 27);\n        writeUint64(length);\n      }\n    }\n    function encodeItem(value) {\n      var i;\n      if (value === false) return writeUint8(0xf4);\n      if (value === true) return writeUint8(0xf5);\n      if (value === null) return writeUint8(0xf6);\n      if (value === undefined) return writeUint8(0xf7);\n      switch (typeof value) {\n        case \"number\":\n          if (Math.floor(value) === value) {\n            if (0 <= value && value <= POW_2_53) return writeTypeAndLength(0, value);\n            if (-POW_2_53 <= value && value < 0) return writeTypeAndLength(1, -(value + 1));\n          }\n          writeUint8(0xfb);\n          return writeFloat64(value);\n        case \"string\":\n          var utf8data = [];\n          for (i = 0; i < value.length; ++i) {\n            var charCode = value.charCodeAt(i);\n            if (charCode < 0x80) {\n              utf8data.push(charCode);\n            } else if (charCode < 0x800) {\n              utf8data.push(0xc0 | charCode >> 6);\n              utf8data.push(0x80 | charCode & 0x3f);\n            } else if (charCode < 0xd800) {\n              utf8data.push(0xe0 | charCode >> 12);\n              utf8data.push(0x80 | charCode >> 6 & 0x3f);\n              utf8data.push(0x80 | charCode & 0x3f);\n            } else {\n              charCode = (charCode & 0x3ff) << 10;\n              charCode |= value.charCodeAt(++i) & 0x3ff;\n              charCode += 0x10000;\n              utf8data.push(0xf0 | charCode >> 18);\n              utf8data.push(0x80 | charCode >> 12 & 0x3f);\n              utf8data.push(0x80 | charCode >> 6 & 0x3f);\n              utf8data.push(0x80 | charCode & 0x3f);\n            }\n          }\n          writeTypeAndLength(3, utf8data.length);\n          return writeUint8Array(utf8data);\n        default:\n          var length;\n          if (Array.isArray(value)) {\n            length = value.length;\n            writeTypeAndLength(4, length);\n            for (i = 0; i < length; ++i) encodeItem(value[i]);\n          } else if (value instanceof Uint8Array) {\n            writeTypeAndLength(2, value.length);\n            writeUint8Array(value);\n          } else {\n            var keys = Object.keys(value);\n            length = keys.length;\n            writeTypeAndLength(5, length);\n            for (i = 0; i < length; ++i) {\n              var key = keys[i];\n              encodeItem(key);\n              encodeItem(value[key]);\n            }\n          }\n      }\n    }\n    encodeItem(value);\n    if (\"slice\" in data) return data.slice(0, offset);\n    var ret = new ArrayBuffer(offset);\n    var retView = new DataView(ret);\n    for (var i = 0; i < offset; ++i) retView.setUint8(i, dataView.getUint8(i));\n    return ret;\n  }\n  function decode(data, tagger, simpleValue) {\n    var dataView = new DataView(data);\n    var offset = 0;\n    if (typeof tagger !== \"function\") tagger = function (value) {\n      return value;\n    };\n    if (typeof simpleValue !== \"function\") simpleValue = function () {\n      return undefined;\n    };\n    function read(value, length) {\n      offset += length;\n      return value;\n    }\n    function readArrayBuffer(length) {\n      return read(new Uint8Array(data, offset, length), length);\n    }\n    function readFloat16() {\n      var tempArrayBuffer = new ArrayBuffer(4);\n      var tempDataView = new DataView(tempArrayBuffer);\n      var value = readUint16();\n      var sign = value & 0x8000;\n      var exponent = value & 0x7c00;\n      var fraction = value & 0x03ff;\n      if (exponent === 0x7c00) exponent = 0xff << 10;else if (exponent !== 0) exponent += 127 - 15 << 10;else if (fraction !== 0) return fraction * POW_2_24;\n      tempDataView.setUint32(0, sign << 16 | exponent << 13 | fraction << 13);\n      return tempDataView.getFloat32(0);\n    }\n    function readFloat32() {\n      return read(dataView.getFloat32(offset), 4);\n    }\n    function readFloat64() {\n      return read(dataView.getFloat64(offset), 8);\n    }\n    function readUint8() {\n      return read(dataView.getUint8(offset), 1);\n    }\n    function readUint16() {\n      return read(dataView.getUint16(offset), 2);\n    }\n    function readUint32() {\n      return read(dataView.getUint32(offset), 4);\n    }\n    function readUint64() {\n      return readUint32() * POW_2_32 + readUint32();\n    }\n    function readBreak() {\n      if (dataView.getUint8(offset) !== 0xff) return false;\n      offset += 1;\n      return true;\n    }\n    function readLength(additionalInformation) {\n      if (additionalInformation < 24) return additionalInformation;\n      if (additionalInformation === 24) return readUint8();\n      if (additionalInformation === 25) return readUint16();\n      if (additionalInformation === 26) return readUint32();\n      if (additionalInformation === 27) return readUint64();\n      if (additionalInformation === 31) return -1;\n      throw \"Invalid length encoding\";\n    }\n    function readIndefiniteStringLength(majorType) {\n      var initialByte = readUint8();\n      if (initialByte === 0xff) return -1;\n      var length = readLength(initialByte & 0x1f);\n      if (length < 0 || initialByte >> 5 !== majorType) throw \"Invalid indefinite length element\";\n      return length;\n    }\n    function appendUtf16data(utf16data, length) {\n      for (var i = 0; i < length; ++i) {\n        var value = readUint8();\n        if (value & 0x80) {\n          if (value < 0xe0) {\n            value = (value & 0x1f) << 6 | readUint8() & 0x3f;\n            length -= 1;\n          } else if (value < 0xf0) {\n            value = (value & 0x0f) << 12 | (readUint8() & 0x3f) << 6 | readUint8() & 0x3f;\n            length -= 2;\n          } else {\n            value = (value & 0x0f) << 18 | (readUint8() & 0x3f) << 12 | (readUint8() & 0x3f) << 6 | readUint8() & 0x3f;\n            length -= 3;\n          }\n        }\n        if (value < 0x10000) {\n          utf16data.push(value);\n        } else {\n          value -= 0x10000;\n          utf16data.push(0xd800 | value >> 10);\n          utf16data.push(0xdc00 | value & 0x3ff);\n        }\n      }\n    }\n    function decodeItem() {\n      var initialByte = readUint8();\n      var majorType = initialByte >> 5;\n      var additionalInformation = initialByte & 0x1f;\n      var i;\n      var length;\n      if (majorType === 7) {\n        switch (additionalInformation) {\n          case 25:\n            return readFloat16();\n          case 26:\n            return readFloat32();\n          case 27:\n            return readFloat64();\n        }\n      }\n      length = readLength(additionalInformation);\n      if (length < 0 && (majorType < 2 || 6 < majorType)) throw \"Invalid length\";\n      switch (majorType) {\n        case 0:\n          return length;\n        case 1:\n          return -1 - length;\n        case 2:\n          if (length < 0) {\n            var elements = [];\n            var fullArrayLength = 0;\n            while ((length = readIndefiniteStringLength(majorType)) >= 0) {\n              fullArrayLength += length;\n              elements.push(readArrayBuffer(length));\n            }\n            var fullArray = new Uint8Array(fullArrayLength);\n            var fullArrayOffset = 0;\n            for (i = 0; i < elements.length; ++i) {\n              fullArray.set(elements[i], fullArrayOffset);\n              fullArrayOffset += elements[i].length;\n            }\n            return fullArray;\n          }\n          return readArrayBuffer(length);\n        case 3:\n          var utf16data = [];\n          if (length < 0) {\n            while ((length = readIndefiniteStringLength(majorType)) >= 0) appendUtf16data(utf16data, length);\n          } else appendUtf16data(utf16data, length);\n          return String.fromCharCode.apply(null, utf16data);\n        case 4:\n          var retArray;\n          if (length < 0) {\n            retArray = [];\n            while (!readBreak()) retArray.push(decodeItem());\n          } else {\n            retArray = new Array(length);\n            for (i = 0; i < length; ++i) retArray[i] = decodeItem();\n          }\n          return retArray;\n        case 5:\n          var retObject = {};\n          for (i = 0; i < length || length < 0 && !readBreak(); ++i) {\n            var key = decodeItem();\n            retObject[key] = decodeItem();\n          }\n          return retObject;\n        case 6:\n          return tagger(decodeItem(), length);\n        case 7:\n          switch (length) {\n            case 20:\n              return false;\n            case 21:\n              return true;\n            case 22:\n              return null;\n            case 23:\n              return undefined;\n            default:\n              return simpleValue(length);\n          }\n      }\n    }\n    var ret = decodeItem();\n    if (offset !== data.byteLength) throw \"Remaining bytes\";\n    return ret;\n  }\n  var obj = {\n    encode: encode,\n    decode: decode\n  };\n  if (typeof define === \"function\" && define.amd) define(\"cbor/cbor\", obj);else if (typeof module !== 'undefined' && module.exports) module.exports = obj;else if (!global.CBOR) global.CBOR = obj;\n})(this);", "map": {"version": 3, "names": ["global", "undefined", "POW_2_24", "Math", "pow", "POW_2_32", "POW_2_53", "encode", "value", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataView", "DataView", "last<PERSON><PERSON><PERSON>", "offset", "ensureSpace", "length", "newByteLength", "byteLength", "<PERSON><PERSON><PERSON><PERSON>", "oldDataView", "uint32count", "i", "setUint32", "getUint32", "write", "writeFloat64", "setFloat64", "writeUint8", "setUint8", "writeUint8Array", "writeUint16", "setUint16", "writeUint32", "writeUint64", "low", "high", "writeTypeAndLength", "type", "encodeItem", "floor", "utf8data", "charCode", "charCodeAt", "push", "Array", "isArray", "Uint8Array", "keys", "Object", "key", "slice", "ret", "retView", "getUint8", "decode", "tagger", "simpleValue", "read", "read<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readFloat16", "tempA<PERSON><PERSON><PERSON><PERSON><PERSON>", "tempDataView", "readUint16", "sign", "exponent", "fraction", "getFloat32", "readFloat32", "readFloat64", "getFloat64", "readUint8", "getUint16", "readUint32", "readUint64", "readBreak", "readLength", "additionalInformation", "readIndefiniteStringLength", "majorType", "initialByte", "appendUtf16data", "utf16data", "decodeItem", "elements", "fullArray<PERSON><PERSON>th", "fullArray", "fullArrayOffset", "set", "String", "fromCharCode", "apply", "retArray", "retObject", "obj", "define", "amd", "module", "exports", "CBOR"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/cbor-js/cbor.js"], "sourcesContent": ["/*\r\n * The MIT License (MIT)\r\n *\r\n * Copyright (c) 2014 <PERSON> <<EMAIL>>\r\n *\r\n * Permission is hereby granted, free of charge, to any person obtaining a copy\r\n * of this software and associated documentation files (the \"Software\"), to deal\r\n * in the Software without restriction, including without limitation the rights\r\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\n * copies of the Software, and to permit persons to whom the Software is\r\n * furnished to do so, subject to the following conditions:\r\n *\r\n * The above copyright notice and this permission notice shall be included in all\r\n * copies or substantial portions of the Software.\r\n *\r\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\r\n * SOFTWARE.\r\n */\r\n\r\n(function(global, undefined) { \"use strict\";\r\nvar POW_2_24 = Math.pow(2, -24),\r\n    POW_2_32 = Math.pow(2, 32),\r\n    POW_2_53 = Math.pow(2, 53);\r\n\r\nfunction encode(value) {\r\n  var data = new ArrayBuffer(256);\r\n  var dataView = new DataView(data);\r\n  var lastLength;\r\n  var offset = 0;\r\n\r\n  function ensureSpace(length) {\r\n    var newByteLength = data.byteLength;\r\n    var requiredLength = offset + length;\r\n    while (newByteLength < requiredLength)\r\n      newByteLength *= 2;\r\n    if (newByteLength !== data.byteLength) {\r\n      var oldDataView = dataView;\r\n      data = new ArrayBuffer(newByteLength);\r\n      dataView = new DataView(data);\r\n      var uint32count = (offset + 3) >> 2;\r\n      for (var i = 0; i < uint32count; ++i)\r\n        dataView.setUint32(i * 4, oldDataView.getUint32(i * 4));\r\n    }\r\n\r\n    lastLength = length;\r\n    return dataView;\r\n  }\r\n  function write() {\r\n    offset += lastLength;\r\n  }\r\n  function writeFloat64(value) {\r\n    write(ensureSpace(8).setFloat64(offset, value));\r\n  }\r\n  function writeUint8(value) {\r\n    write(ensureSpace(1).setUint8(offset, value));\r\n  }\r\n  function writeUint8Array(value) {\r\n    var dataView = ensureSpace(value.length);\r\n    for (var i = 0; i < value.length; ++i)\r\n      dataView.setUint8(offset + i, value[i]);\r\n    write();\r\n  }\r\n  function writeUint16(value) {\r\n    write(ensureSpace(2).setUint16(offset, value));\r\n  }\r\n  function writeUint32(value) {\r\n    write(ensureSpace(4).setUint32(offset, value));\r\n  }\r\n  function writeUint64(value) {\r\n    var low = value % POW_2_32;\r\n    var high = (value - low) / POW_2_32;\r\n    var dataView = ensureSpace(8);\r\n    dataView.setUint32(offset, high);\r\n    dataView.setUint32(offset + 4, low);\r\n    write();\r\n  }\r\n  function writeTypeAndLength(type, length) {\r\n    if (length < 24) {\r\n      writeUint8(type << 5 | length);\r\n    } else if (length < 0x100) {\r\n      writeUint8(type << 5 | 24);\r\n      writeUint8(length);\r\n    } else if (length < 0x10000) {\r\n      writeUint8(type << 5 | 25);\r\n      writeUint16(length);\r\n    } else if (length < 0x100000000) {\r\n      writeUint8(type << 5 | 26);\r\n      writeUint32(length);\r\n    } else {\r\n      writeUint8(type << 5 | 27);\r\n      writeUint64(length);\r\n    }\r\n  }\r\n  \r\n  function encodeItem(value) {\r\n    var i;\r\n\r\n    if (value === false)\r\n      return writeUint8(0xf4);\r\n    if (value === true)\r\n      return writeUint8(0xf5);\r\n    if (value === null)\r\n      return writeUint8(0xf6);\r\n    if (value === undefined)\r\n      return writeUint8(0xf7);\r\n  \r\n    switch (typeof value) {\r\n      case \"number\":\r\n        if (Math.floor(value) === value) {\r\n          if (0 <= value && value <= POW_2_53)\r\n            return writeTypeAndLength(0, value);\r\n          if (-POW_2_53 <= value && value < 0)\r\n            return writeTypeAndLength(1, -(value + 1));\r\n        }\r\n        writeUint8(0xfb);\r\n        return writeFloat64(value);\r\n\r\n      case \"string\":\r\n        var utf8data = [];\r\n        for (i = 0; i < value.length; ++i) {\r\n          var charCode = value.charCodeAt(i);\r\n          if (charCode < 0x80) {\r\n            utf8data.push(charCode);\r\n          } else if (charCode < 0x800) {\r\n            utf8data.push(0xc0 | charCode >> 6);\r\n            utf8data.push(0x80 | charCode & 0x3f);\r\n          } else if (charCode < 0xd800) {\r\n            utf8data.push(0xe0 | charCode >> 12);\r\n            utf8data.push(0x80 | (charCode >> 6)  & 0x3f);\r\n            utf8data.push(0x80 | charCode & 0x3f);\r\n          } else {\r\n            charCode = (charCode & 0x3ff) << 10;\r\n            charCode |= value.charCodeAt(++i) & 0x3ff;\r\n            charCode += 0x10000;\r\n\r\n            utf8data.push(0xf0 | charCode >> 18);\r\n            utf8data.push(0x80 | (charCode >> 12)  & 0x3f);\r\n            utf8data.push(0x80 | (charCode >> 6)  & 0x3f);\r\n            utf8data.push(0x80 | charCode & 0x3f);\r\n          }\r\n        }\r\n\r\n        writeTypeAndLength(3, utf8data.length);\r\n        return writeUint8Array(utf8data);\r\n\r\n      default:\r\n        var length;\r\n        if (Array.isArray(value)) {\r\n          length = value.length;\r\n          writeTypeAndLength(4, length);\r\n          for (i = 0; i < length; ++i)\r\n            encodeItem(value[i]);\r\n        } else if (value instanceof Uint8Array) {\r\n          writeTypeAndLength(2, value.length);\r\n          writeUint8Array(value);\r\n        } else {\r\n          var keys = Object.keys(value);\r\n          length = keys.length;\r\n          writeTypeAndLength(5, length);\r\n          for (i = 0; i < length; ++i) {\r\n            var key = keys[i];\r\n            encodeItem(key);\r\n            encodeItem(value[key]);\r\n          }\r\n        }\r\n    }\r\n  }\r\n  \r\n  encodeItem(value);\r\n\r\n  if (\"slice\" in data)\r\n    return data.slice(0, offset);\r\n  \r\n  var ret = new ArrayBuffer(offset);\r\n  var retView = new DataView(ret);\r\n  for (var i = 0; i < offset; ++i)\r\n    retView.setUint8(i, dataView.getUint8(i));\r\n  return ret;\r\n}\r\n\r\nfunction decode(data, tagger, simpleValue) {\r\n  var dataView = new DataView(data);\r\n  var offset = 0;\r\n  \r\n  if (typeof tagger !== \"function\")\r\n    tagger = function(value) { return value; };\r\n  if (typeof simpleValue !== \"function\")\r\n    simpleValue = function() { return undefined; };\r\n\r\n  function read(value, length) {\r\n    offset += length;\r\n    return value;\r\n  }\r\n  function readArrayBuffer(length) {\r\n    return read(new Uint8Array(data, offset, length), length);\r\n  }\r\n  function readFloat16() {\r\n    var tempArrayBuffer = new ArrayBuffer(4);\r\n    var tempDataView = new DataView(tempArrayBuffer);\r\n    var value = readUint16();\r\n\r\n    var sign = value & 0x8000;\r\n    var exponent = value & 0x7c00;\r\n    var fraction = value & 0x03ff;\r\n    \r\n    if (exponent === 0x7c00)\r\n      exponent = 0xff << 10;\r\n    else if (exponent !== 0)\r\n      exponent += (127 - 15) << 10;\r\n    else if (fraction !== 0)\r\n      return fraction * POW_2_24;\r\n    \r\n    tempDataView.setUint32(0, sign << 16 | exponent << 13 | fraction << 13);\r\n    return tempDataView.getFloat32(0);\r\n  }\r\n  function readFloat32() {\r\n    return read(dataView.getFloat32(offset), 4);\r\n  }\r\n  function readFloat64() {\r\n    return read(dataView.getFloat64(offset), 8);\r\n  }\r\n  function readUint8() {\r\n    return read(dataView.getUint8(offset), 1);\r\n  }\r\n  function readUint16() {\r\n    return read(dataView.getUint16(offset), 2);\r\n  }\r\n  function readUint32() {\r\n    return read(dataView.getUint32(offset), 4);\r\n  }\r\n  function readUint64() {\r\n    return readUint32() * POW_2_32 + readUint32();\r\n  }\r\n  function readBreak() {\r\n    if (dataView.getUint8(offset) !== 0xff)\r\n      return false;\r\n    offset += 1;\r\n    return true;\r\n  }\r\n  function readLength(additionalInformation) {\r\n    if (additionalInformation < 24)\r\n      return additionalInformation;\r\n    if (additionalInformation === 24)\r\n      return readUint8();\r\n    if (additionalInformation === 25)\r\n      return readUint16();\r\n    if (additionalInformation === 26)\r\n      return readUint32();\r\n    if (additionalInformation === 27)\r\n      return readUint64();\r\n    if (additionalInformation === 31)\r\n      return -1;\r\n    throw \"Invalid length encoding\";\r\n  }\r\n  function readIndefiniteStringLength(majorType) {\r\n    var initialByte = readUint8();\r\n    if (initialByte === 0xff)\r\n      return -1;\r\n    var length = readLength(initialByte & 0x1f);\r\n    if (length < 0 || (initialByte >> 5) !== majorType)\r\n      throw \"Invalid indefinite length element\";\r\n    return length;\r\n  }\r\n\r\n  function appendUtf16data(utf16data, length) {\r\n    for (var i = 0; i < length; ++i) {\r\n      var value = readUint8();\r\n      if (value & 0x80) {\r\n        if (value < 0xe0) {\r\n          value = (value & 0x1f) <<  6\r\n                | (readUint8() & 0x3f);\r\n          length -= 1;\r\n        } else if (value < 0xf0) {\r\n          value = (value & 0x0f) << 12\r\n                | (readUint8() & 0x3f) << 6\r\n                | (readUint8() & 0x3f);\r\n          length -= 2;\r\n        } else {\r\n          value = (value & 0x0f) << 18\r\n                | (readUint8() & 0x3f) << 12\r\n                | (readUint8() & 0x3f) << 6\r\n                | (readUint8() & 0x3f);\r\n          length -= 3;\r\n        }\r\n      }\r\n\r\n      if (value < 0x10000) {\r\n        utf16data.push(value);\r\n      } else {\r\n        value -= 0x10000;\r\n        utf16data.push(0xd800 | (value >> 10));\r\n        utf16data.push(0xdc00 | (value & 0x3ff));\r\n      }\r\n    }\r\n  }\r\n\r\n  function decodeItem() {\r\n    var initialByte = readUint8();\r\n    var majorType = initialByte >> 5;\r\n    var additionalInformation = initialByte & 0x1f;\r\n    var i;\r\n    var length;\r\n\r\n    if (majorType === 7) {\r\n      switch (additionalInformation) {\r\n        case 25:\r\n          return readFloat16();\r\n        case 26:\r\n          return readFloat32();\r\n        case 27:\r\n          return readFloat64();\r\n      }\r\n    }\r\n\r\n    length = readLength(additionalInformation);\r\n    if (length < 0 && (majorType < 2 || 6 < majorType))\r\n      throw \"Invalid length\";\r\n\r\n    switch (majorType) {\r\n      case 0:\r\n        return length;\r\n      case 1:\r\n        return -1 - length;\r\n      case 2:\r\n        if (length < 0) {\r\n          var elements = [];\r\n          var fullArrayLength = 0;\r\n          while ((length = readIndefiniteStringLength(majorType)) >= 0) {\r\n            fullArrayLength += length;\r\n            elements.push(readArrayBuffer(length));\r\n          }\r\n          var fullArray = new Uint8Array(fullArrayLength);\r\n          var fullArrayOffset = 0;\r\n          for (i = 0; i < elements.length; ++i) {\r\n            fullArray.set(elements[i], fullArrayOffset);\r\n            fullArrayOffset += elements[i].length;\r\n          }\r\n          return fullArray;\r\n        }\r\n        return readArrayBuffer(length);\r\n      case 3:\r\n        var utf16data = [];\r\n        if (length < 0) {\r\n          while ((length = readIndefiniteStringLength(majorType)) >= 0)\r\n            appendUtf16data(utf16data, length);\r\n        } else\r\n          appendUtf16data(utf16data, length);\r\n        return String.fromCharCode.apply(null, utf16data);\r\n      case 4:\r\n        var retArray;\r\n        if (length < 0) {\r\n          retArray = [];\r\n          while (!readBreak())\r\n            retArray.push(decodeItem());\r\n        } else {\r\n          retArray = new Array(length);\r\n          for (i = 0; i < length; ++i)\r\n            retArray[i] = decodeItem();\r\n        }\r\n        return retArray;\r\n      case 5:\r\n        var retObject = {};\r\n        for (i = 0; i < length || length < 0 && !readBreak(); ++i) {\r\n          var key = decodeItem();\r\n          retObject[key] = decodeItem();\r\n        }\r\n        return retObject;\r\n      case 6:\r\n        return tagger(decodeItem(), length);\r\n      case 7:\r\n        switch (length) {\r\n          case 20:\r\n            return false;\r\n          case 21:\r\n            return true;\r\n          case 22:\r\n            return null;\r\n          case 23:\r\n            return undefined;\r\n          default:\r\n            return simpleValue(length);\r\n        }\r\n    }\r\n  }\r\n\r\n  var ret = decodeItem();\r\n  if (offset !== data.byteLength)\r\n    throw \"Remaining bytes\";\r\n  return ret;\r\n}\r\n\r\nvar obj = { encode: encode, decode: decode };\r\n\r\nif (typeof define === \"function\" && define.amd)\r\n  define(\"cbor/cbor\", obj);\r\nelse if (typeof module !== 'undefined' && module.exports)\r\n  module.exports = obj;\r\nelse if (!global.CBOR)\r\n  global.CBOR = obj;\r\n\r\n})(this);\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,CAAC,UAASA,MAAM,EAAEC,SAAS,EAAE;EAAE,YAAY;;EAC3C,IAAIC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3BC,QAAQ,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1BE,QAAQ,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;EAE9B,SAASG,MAAMA,CAACC,KAAK,EAAE;IACrB,IAAIC,IAAI,GAAG,IAAIC,WAAW,CAAC,GAAG,CAAC;IAC/B,IAAIC,QAAQ,GAAG,IAAIC,QAAQ,CAACH,IAAI,CAAC;IACjC,IAAII,UAAU;IACd,IAAIC,MAAM,GAAG,CAAC;IAEd,SAASC,WAAWA,CAACC,MAAM,EAAE;MAC3B,IAAIC,aAAa,GAAGR,IAAI,CAACS,UAAU;MACnC,IAAIC,cAAc,GAAGL,MAAM,GAAGE,MAAM;MACpC,OAAOC,aAAa,GAAGE,cAAc,EACnCF,aAAa,IAAI,CAAC;MACpB,IAAIA,aAAa,KAAKR,IAAI,CAACS,UAAU,EAAE;QACrC,IAAIE,WAAW,GAAGT,QAAQ;QAC1BF,IAAI,GAAG,IAAIC,WAAW,CAACO,aAAa,CAAC;QACrCN,QAAQ,GAAG,IAAIC,QAAQ,CAACH,IAAI,CAAC;QAC7B,IAAIY,WAAW,GAAIP,MAAM,GAAG,CAAC,IAAK,CAAC;QACnC,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,WAAW,EAAE,EAAEC,CAAC,EAClCX,QAAQ,CAACY,SAAS,CAACD,CAAC,GAAG,CAAC,EAAEF,WAAW,CAACI,SAAS,CAACF,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3D;MAEAT,UAAU,GAAGG,MAAM;MACnB,OAAOL,QAAQ;IACjB;IACA,SAASc,KAAKA,CAAA,EAAG;MACfX,MAAM,IAAID,UAAU;IACtB;IACA,SAASa,YAAYA,CAAClB,KAAK,EAAE;MAC3BiB,KAAK,CAACV,WAAW,CAAC,CAAC,CAAC,CAACY,UAAU,CAACb,MAAM,EAAEN,KAAK,CAAC,CAAC;IACjD;IACA,SAASoB,UAAUA,CAACpB,KAAK,EAAE;MACzBiB,KAAK,CAACV,WAAW,CAAC,CAAC,CAAC,CAACc,QAAQ,CAACf,MAAM,EAAEN,KAAK,CAAC,CAAC;IAC/C;IACA,SAASsB,eAAeA,CAACtB,KAAK,EAAE;MAC9B,IAAIG,QAAQ,GAAGI,WAAW,CAACP,KAAK,CAACQ,MAAM,CAAC;MACxC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,KAAK,CAACQ,MAAM,EAAE,EAAEM,CAAC,EACnCX,QAAQ,CAACkB,QAAQ,CAACf,MAAM,GAAGQ,CAAC,EAAEd,KAAK,CAACc,CAAC,CAAC,CAAC;MACzCG,KAAK,CAAC,CAAC;IACT;IACA,SAASM,WAAWA,CAACvB,KAAK,EAAE;MAC1BiB,KAAK,CAACV,WAAW,CAAC,CAAC,CAAC,CAACiB,SAAS,CAAClB,MAAM,EAAEN,KAAK,CAAC,CAAC;IAChD;IACA,SAASyB,WAAWA,CAACzB,KAAK,EAAE;MAC1BiB,KAAK,CAACV,WAAW,CAAC,CAAC,CAAC,CAACQ,SAAS,CAACT,MAAM,EAAEN,KAAK,CAAC,CAAC;IAChD;IACA,SAAS0B,WAAWA,CAAC1B,KAAK,EAAE;MAC1B,IAAI2B,GAAG,GAAG3B,KAAK,GAAGH,QAAQ;MAC1B,IAAI+B,IAAI,GAAG,CAAC5B,KAAK,GAAG2B,GAAG,IAAI9B,QAAQ;MACnC,IAAIM,QAAQ,GAAGI,WAAW,CAAC,CAAC,CAAC;MAC7BJ,QAAQ,CAACY,SAAS,CAACT,MAAM,EAAEsB,IAAI,CAAC;MAChCzB,QAAQ,CAACY,SAAS,CAACT,MAAM,GAAG,CAAC,EAAEqB,GAAG,CAAC;MACnCV,KAAK,CAAC,CAAC;IACT;IACA,SAASY,kBAAkBA,CAACC,IAAI,EAAEtB,MAAM,EAAE;MACxC,IAAIA,MAAM,GAAG,EAAE,EAAE;QACfY,UAAU,CAACU,IAAI,IAAI,CAAC,GAAGtB,MAAM,CAAC;MAChC,CAAC,MAAM,IAAIA,MAAM,GAAG,KAAK,EAAE;QACzBY,UAAU,CAACU,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QAC1BV,UAAU,CAACZ,MAAM,CAAC;MACpB,CAAC,MAAM,IAAIA,MAAM,GAAG,OAAO,EAAE;QAC3BY,UAAU,CAACU,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QAC1BP,WAAW,CAACf,MAAM,CAAC;MACrB,CAAC,MAAM,IAAIA,MAAM,GAAG,WAAW,EAAE;QAC/BY,UAAU,CAACU,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QAC1BL,WAAW,CAACjB,MAAM,CAAC;MACrB,CAAC,MAAM;QACLY,UAAU,CAACU,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;QAC1BJ,WAAW,CAAClB,MAAM,CAAC;MACrB;IACF;IAEA,SAASuB,UAAUA,CAAC/B,KAAK,EAAE;MACzB,IAAIc,CAAC;MAEL,IAAId,KAAK,KAAK,KAAK,EACjB,OAAOoB,UAAU,CAAC,IAAI,CAAC;MACzB,IAAIpB,KAAK,KAAK,IAAI,EAChB,OAAOoB,UAAU,CAAC,IAAI,CAAC;MACzB,IAAIpB,KAAK,KAAK,IAAI,EAChB,OAAOoB,UAAU,CAAC,IAAI,CAAC;MACzB,IAAIpB,KAAK,KAAKP,SAAS,EACrB,OAAO2B,UAAU,CAAC,IAAI,CAAC;MAEzB,QAAQ,OAAOpB,KAAK;QAClB,KAAK,QAAQ;UACX,IAAIL,IAAI,CAACqC,KAAK,CAAChC,KAAK,CAAC,KAAKA,KAAK,EAAE;YAC/B,IAAI,CAAC,IAAIA,KAAK,IAAIA,KAAK,IAAIF,QAAQ,EACjC,OAAO+B,kBAAkB,CAAC,CAAC,EAAE7B,KAAK,CAAC;YACrC,IAAI,CAACF,QAAQ,IAAIE,KAAK,IAAIA,KAAK,GAAG,CAAC,EACjC,OAAO6B,kBAAkB,CAAC,CAAC,EAAE,EAAE7B,KAAK,GAAG,CAAC,CAAC,CAAC;UAC9C;UACAoB,UAAU,CAAC,IAAI,CAAC;UAChB,OAAOF,YAAY,CAAClB,KAAK,CAAC;QAE5B,KAAK,QAAQ;UACX,IAAIiC,QAAQ,GAAG,EAAE;UACjB,KAAKnB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,KAAK,CAACQ,MAAM,EAAE,EAAEM,CAAC,EAAE;YACjC,IAAIoB,QAAQ,GAAGlC,KAAK,CAACmC,UAAU,CAACrB,CAAC,CAAC;YAClC,IAAIoB,QAAQ,GAAG,IAAI,EAAE;cACnBD,QAAQ,CAACG,IAAI,CAACF,QAAQ,CAAC;YACzB,CAAC,MAAM,IAAIA,QAAQ,GAAG,KAAK,EAAE;cAC3BD,QAAQ,CAACG,IAAI,CAAC,IAAI,GAAGF,QAAQ,IAAI,CAAC,CAAC;cACnCD,QAAQ,CAACG,IAAI,CAAC,IAAI,GAAGF,QAAQ,GAAG,IAAI,CAAC;YACvC,CAAC,MAAM,IAAIA,QAAQ,GAAG,MAAM,EAAE;cAC5BD,QAAQ,CAACG,IAAI,CAAC,IAAI,GAAGF,QAAQ,IAAI,EAAE,CAAC;cACpCD,QAAQ,CAACG,IAAI,CAAC,IAAI,GAAIF,QAAQ,IAAI,CAAC,GAAK,IAAI,CAAC;cAC7CD,QAAQ,CAACG,IAAI,CAAC,IAAI,GAAGF,QAAQ,GAAG,IAAI,CAAC;YACvC,CAAC,MAAM;cACLA,QAAQ,GAAG,CAACA,QAAQ,GAAG,KAAK,KAAK,EAAE;cACnCA,QAAQ,IAAIlC,KAAK,CAACmC,UAAU,CAAC,EAAErB,CAAC,CAAC,GAAG,KAAK;cACzCoB,QAAQ,IAAI,OAAO;cAEnBD,QAAQ,CAACG,IAAI,CAAC,IAAI,GAAGF,QAAQ,IAAI,EAAE,CAAC;cACpCD,QAAQ,CAACG,IAAI,CAAC,IAAI,GAAIF,QAAQ,IAAI,EAAE,GAAK,IAAI,CAAC;cAC9CD,QAAQ,CAACG,IAAI,CAAC,IAAI,GAAIF,QAAQ,IAAI,CAAC,GAAK,IAAI,CAAC;cAC7CD,QAAQ,CAACG,IAAI,CAAC,IAAI,GAAGF,QAAQ,GAAG,IAAI,CAAC;YACvC;UACF;UAEAL,kBAAkB,CAAC,CAAC,EAAEI,QAAQ,CAACzB,MAAM,CAAC;UACtC,OAAOc,eAAe,CAACW,QAAQ,CAAC;QAElC;UACE,IAAIzB,MAAM;UACV,IAAI6B,KAAK,CAACC,OAAO,CAACtC,KAAK,CAAC,EAAE;YACxBQ,MAAM,GAAGR,KAAK,CAACQ,MAAM;YACrBqB,kBAAkB,CAAC,CAAC,EAAErB,MAAM,CAAC;YAC7B,KAAKM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,EAAE,EAAEM,CAAC,EACzBiB,UAAU,CAAC/B,KAAK,CAACc,CAAC,CAAC,CAAC;UACxB,CAAC,MAAM,IAAId,KAAK,YAAYuC,UAAU,EAAE;YACtCV,kBAAkB,CAAC,CAAC,EAAE7B,KAAK,CAACQ,MAAM,CAAC;YACnCc,eAAe,CAACtB,KAAK,CAAC;UACxB,CAAC,MAAM;YACL,IAAIwC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACxC,KAAK,CAAC;YAC7BQ,MAAM,GAAGgC,IAAI,CAAChC,MAAM;YACpBqB,kBAAkB,CAAC,CAAC,EAAErB,MAAM,CAAC;YAC7B,KAAKM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,EAAE,EAAEM,CAAC,EAAE;cAC3B,IAAI4B,GAAG,GAAGF,IAAI,CAAC1B,CAAC,CAAC;cACjBiB,UAAU,CAACW,GAAG,CAAC;cACfX,UAAU,CAAC/B,KAAK,CAAC0C,GAAG,CAAC,CAAC;YACxB;UACF;MACJ;IACF;IAEAX,UAAU,CAAC/B,KAAK,CAAC;IAEjB,IAAI,OAAO,IAAIC,IAAI,EACjB,OAAOA,IAAI,CAAC0C,KAAK,CAAC,CAAC,EAAErC,MAAM,CAAC;IAE9B,IAAIsC,GAAG,GAAG,IAAI1C,WAAW,CAACI,MAAM,CAAC;IACjC,IAAIuC,OAAO,GAAG,IAAIzC,QAAQ,CAACwC,GAAG,CAAC;IAC/B,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,EAAE,EAAEQ,CAAC,EAC7B+B,OAAO,CAACxB,QAAQ,CAACP,CAAC,EAAEX,QAAQ,CAAC2C,QAAQ,CAAChC,CAAC,CAAC,CAAC;IAC3C,OAAO8B,GAAG;EACZ;EAEA,SAASG,MAAMA,CAAC9C,IAAI,EAAE+C,MAAM,EAAEC,WAAW,EAAE;IACzC,IAAI9C,QAAQ,GAAG,IAAIC,QAAQ,CAACH,IAAI,CAAC;IACjC,IAAIK,MAAM,GAAG,CAAC;IAEd,IAAI,OAAO0C,MAAM,KAAK,UAAU,EAC9BA,MAAM,GAAG,SAAAA,CAAShD,KAAK,EAAE;MAAE,OAAOA,KAAK;IAAE,CAAC;IAC5C,IAAI,OAAOiD,WAAW,KAAK,UAAU,EACnCA,WAAW,GAAG,SAAAA,CAAA,EAAW;MAAE,OAAOxD,SAAS;IAAE,CAAC;IAEhD,SAASyD,IAAIA,CAAClD,KAAK,EAAEQ,MAAM,EAAE;MAC3BF,MAAM,IAAIE,MAAM;MAChB,OAAOR,KAAK;IACd;IACA,SAASmD,eAAeA,CAAC3C,MAAM,EAAE;MAC/B,OAAO0C,IAAI,CAAC,IAAIX,UAAU,CAACtC,IAAI,EAAEK,MAAM,EAAEE,MAAM,CAAC,EAAEA,MAAM,CAAC;IAC3D;IACA,SAAS4C,WAAWA,CAAA,EAAG;MACrB,IAAIC,eAAe,GAAG,IAAInD,WAAW,CAAC,CAAC,CAAC;MACxC,IAAIoD,YAAY,GAAG,IAAIlD,QAAQ,CAACiD,eAAe,CAAC;MAChD,IAAIrD,KAAK,GAAGuD,UAAU,CAAC,CAAC;MAExB,IAAIC,IAAI,GAAGxD,KAAK,GAAG,MAAM;MACzB,IAAIyD,QAAQ,GAAGzD,KAAK,GAAG,MAAM;MAC7B,IAAI0D,QAAQ,GAAG1D,KAAK,GAAG,MAAM;MAE7B,IAAIyD,QAAQ,KAAK,MAAM,EACrBA,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC,KACnB,IAAIA,QAAQ,KAAK,CAAC,EACrBA,QAAQ,IAAK,GAAG,GAAG,EAAE,IAAK,EAAE,CAAC,KAC1B,IAAIC,QAAQ,KAAK,CAAC,EACrB,OAAOA,QAAQ,GAAGhE,QAAQ;MAE5B4D,YAAY,CAACvC,SAAS,CAAC,CAAC,EAAEyC,IAAI,IAAI,EAAE,GAAGC,QAAQ,IAAI,EAAE,GAAGC,QAAQ,IAAI,EAAE,CAAC;MACvE,OAAOJ,YAAY,CAACK,UAAU,CAAC,CAAC,CAAC;IACnC;IACA,SAASC,WAAWA,CAAA,EAAG;MACrB,OAAOV,IAAI,CAAC/C,QAAQ,CAACwD,UAAU,CAACrD,MAAM,CAAC,EAAE,CAAC,CAAC;IAC7C;IACA,SAASuD,WAAWA,CAAA,EAAG;MACrB,OAAOX,IAAI,CAAC/C,QAAQ,CAAC2D,UAAU,CAACxD,MAAM,CAAC,EAAE,CAAC,CAAC;IAC7C;IACA,SAASyD,SAASA,CAAA,EAAG;MACnB,OAAOb,IAAI,CAAC/C,QAAQ,CAAC2C,QAAQ,CAACxC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC3C;IACA,SAASiD,UAAUA,CAAA,EAAG;MACpB,OAAOL,IAAI,CAAC/C,QAAQ,CAAC6D,SAAS,CAAC1D,MAAM,CAAC,EAAE,CAAC,CAAC;IAC5C;IACA,SAAS2D,UAAUA,CAAA,EAAG;MACpB,OAAOf,IAAI,CAAC/C,QAAQ,CAACa,SAAS,CAACV,MAAM,CAAC,EAAE,CAAC,CAAC;IAC5C;IACA,SAAS4D,UAAUA,CAAA,EAAG;MACpB,OAAOD,UAAU,CAAC,CAAC,GAAGpE,QAAQ,GAAGoE,UAAU,CAAC,CAAC;IAC/C;IACA,SAASE,SAASA,CAAA,EAAG;MACnB,IAAIhE,QAAQ,CAAC2C,QAAQ,CAACxC,MAAM,CAAC,KAAK,IAAI,EACpC,OAAO,KAAK;MACdA,MAAM,IAAI,CAAC;MACX,OAAO,IAAI;IACb;IACA,SAAS8D,UAAUA,CAACC,qBAAqB,EAAE;MACzC,IAAIA,qBAAqB,GAAG,EAAE,EAC5B,OAAOA,qBAAqB;MAC9B,IAAIA,qBAAqB,KAAK,EAAE,EAC9B,OAAON,SAAS,CAAC,CAAC;MACpB,IAAIM,qBAAqB,KAAK,EAAE,EAC9B,OAAOd,UAAU,CAAC,CAAC;MACrB,IAAIc,qBAAqB,KAAK,EAAE,EAC9B,OAAOJ,UAAU,CAAC,CAAC;MACrB,IAAII,qBAAqB,KAAK,EAAE,EAC9B,OAAOH,UAAU,CAAC,CAAC;MACrB,IAAIG,qBAAqB,KAAK,EAAE,EAC9B,OAAO,CAAC,CAAC;MACX,MAAM,yBAAyB;IACjC;IACA,SAASC,0BAA0BA,CAACC,SAAS,EAAE;MAC7C,IAAIC,WAAW,GAAGT,SAAS,CAAC,CAAC;MAC7B,IAAIS,WAAW,KAAK,IAAI,EACtB,OAAO,CAAC,CAAC;MACX,IAAIhE,MAAM,GAAG4D,UAAU,CAACI,WAAW,GAAG,IAAI,CAAC;MAC3C,IAAIhE,MAAM,GAAG,CAAC,IAAKgE,WAAW,IAAI,CAAC,KAAMD,SAAS,EAChD,MAAM,mCAAmC;MAC3C,OAAO/D,MAAM;IACf;IAEA,SAASiE,eAAeA,CAACC,SAAS,EAAElE,MAAM,EAAE;MAC1C,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,EAAE,EAAEM,CAAC,EAAE;QAC/B,IAAId,KAAK,GAAG+D,SAAS,CAAC,CAAC;QACvB,IAAI/D,KAAK,GAAG,IAAI,EAAE;UAChB,IAAIA,KAAK,GAAG,IAAI,EAAE;YAChBA,KAAK,GAAG,CAACA,KAAK,GAAG,IAAI,KAAM,CAAC,GACnB+D,SAAS,CAAC,CAAC,GAAG,IAAK;YAC5BvD,MAAM,IAAI,CAAC;UACb,CAAC,MAAM,IAAIR,KAAK,GAAG,IAAI,EAAE;YACvBA,KAAK,GAAG,CAACA,KAAK,GAAG,IAAI,KAAK,EAAE,GACpB,CAAC+D,SAAS,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GACxBA,SAAS,CAAC,CAAC,GAAG,IAAK;YAC5BvD,MAAM,IAAI,CAAC;UACb,CAAC,MAAM;YACLR,KAAK,GAAG,CAACA,KAAK,GAAG,IAAI,KAAK,EAAE,GACpB,CAAC+D,SAAS,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,GAC1B,CAACA,SAAS,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GACxBA,SAAS,CAAC,CAAC,GAAG,IAAK;YAC5BvD,MAAM,IAAI,CAAC;UACb;QACF;QAEA,IAAIR,KAAK,GAAG,OAAO,EAAE;UACnB0E,SAAS,CAACtC,IAAI,CAACpC,KAAK,CAAC;QACvB,CAAC,MAAM;UACLA,KAAK,IAAI,OAAO;UAChB0E,SAAS,CAACtC,IAAI,CAAC,MAAM,GAAIpC,KAAK,IAAI,EAAG,CAAC;UACtC0E,SAAS,CAACtC,IAAI,CAAC,MAAM,GAAIpC,KAAK,GAAG,KAAM,CAAC;QAC1C;MACF;IACF;IAEA,SAAS2E,UAAUA,CAAA,EAAG;MACpB,IAAIH,WAAW,GAAGT,SAAS,CAAC,CAAC;MAC7B,IAAIQ,SAAS,GAAGC,WAAW,IAAI,CAAC;MAChC,IAAIH,qBAAqB,GAAGG,WAAW,GAAG,IAAI;MAC9C,IAAI1D,CAAC;MACL,IAAIN,MAAM;MAEV,IAAI+D,SAAS,KAAK,CAAC,EAAE;QACnB,QAAQF,qBAAqB;UAC3B,KAAK,EAAE;YACL,OAAOjB,WAAW,CAAC,CAAC;UACtB,KAAK,EAAE;YACL,OAAOQ,WAAW,CAAC,CAAC;UACtB,KAAK,EAAE;YACL,OAAOC,WAAW,CAAC,CAAC;QACxB;MACF;MAEArD,MAAM,GAAG4D,UAAU,CAACC,qBAAqB,CAAC;MAC1C,IAAI7D,MAAM,GAAG,CAAC,KAAK+D,SAAS,GAAG,CAAC,IAAI,CAAC,GAAGA,SAAS,CAAC,EAChD,MAAM,gBAAgB;MAExB,QAAQA,SAAS;QACf,KAAK,CAAC;UACJ,OAAO/D,MAAM;QACf,KAAK,CAAC;UACJ,OAAO,CAAC,CAAC,GAAGA,MAAM;QACpB,KAAK,CAAC;UACJ,IAAIA,MAAM,GAAG,CAAC,EAAE;YACd,IAAIoE,QAAQ,GAAG,EAAE;YACjB,IAAIC,eAAe,GAAG,CAAC;YACvB,OAAO,CAACrE,MAAM,GAAG8D,0BAA0B,CAACC,SAAS,CAAC,KAAK,CAAC,EAAE;cAC5DM,eAAe,IAAIrE,MAAM;cACzBoE,QAAQ,CAACxC,IAAI,CAACe,eAAe,CAAC3C,MAAM,CAAC,CAAC;YACxC;YACA,IAAIsE,SAAS,GAAG,IAAIvC,UAAU,CAACsC,eAAe,CAAC;YAC/C,IAAIE,eAAe,GAAG,CAAC;YACvB,KAAKjE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,QAAQ,CAACpE,MAAM,EAAE,EAAEM,CAAC,EAAE;cACpCgE,SAAS,CAACE,GAAG,CAACJ,QAAQ,CAAC9D,CAAC,CAAC,EAAEiE,eAAe,CAAC;cAC3CA,eAAe,IAAIH,QAAQ,CAAC9D,CAAC,CAAC,CAACN,MAAM;YACvC;YACA,OAAOsE,SAAS;UAClB;UACA,OAAO3B,eAAe,CAAC3C,MAAM,CAAC;QAChC,KAAK,CAAC;UACJ,IAAIkE,SAAS,GAAG,EAAE;UAClB,IAAIlE,MAAM,GAAG,CAAC,EAAE;YACd,OAAO,CAACA,MAAM,GAAG8D,0BAA0B,CAACC,SAAS,CAAC,KAAK,CAAC,EAC1DE,eAAe,CAACC,SAAS,EAAElE,MAAM,CAAC;UACtC,CAAC,MACCiE,eAAe,CAACC,SAAS,EAAElE,MAAM,CAAC;UACpC,OAAOyE,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAET,SAAS,CAAC;QACnD,KAAK,CAAC;UACJ,IAAIU,QAAQ;UACZ,IAAI5E,MAAM,GAAG,CAAC,EAAE;YACd4E,QAAQ,GAAG,EAAE;YACb,OAAO,CAACjB,SAAS,CAAC,CAAC,EACjBiB,QAAQ,CAAChD,IAAI,CAACuC,UAAU,CAAC,CAAC,CAAC;UAC/B,CAAC,MAAM;YACLS,QAAQ,GAAG,IAAI/C,KAAK,CAAC7B,MAAM,CAAC;YAC5B,KAAKM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,EAAE,EAAEM,CAAC,EACzBsE,QAAQ,CAACtE,CAAC,CAAC,GAAG6D,UAAU,CAAC,CAAC;UAC9B;UACA,OAAOS,QAAQ;QACjB,KAAK,CAAC;UACJ,IAAIC,SAAS,GAAG,CAAC,CAAC;UAClB,KAAKvE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,IAAIA,MAAM,GAAG,CAAC,IAAI,CAAC2D,SAAS,CAAC,CAAC,EAAE,EAAErD,CAAC,EAAE;YACzD,IAAI4B,GAAG,GAAGiC,UAAU,CAAC,CAAC;YACtBU,SAAS,CAAC3C,GAAG,CAAC,GAAGiC,UAAU,CAAC,CAAC;UAC/B;UACA,OAAOU,SAAS;QAClB,KAAK,CAAC;UACJ,OAAOrC,MAAM,CAAC2B,UAAU,CAAC,CAAC,EAAEnE,MAAM,CAAC;QACrC,KAAK,CAAC;UACJ,QAAQA,MAAM;YACZ,KAAK,EAAE;cACL,OAAO,KAAK;YACd,KAAK,EAAE;cACL,OAAO,IAAI;YACb,KAAK,EAAE;cACL,OAAO,IAAI;YACb,KAAK,EAAE;cACL,OAAOf,SAAS;YAClB;cACE,OAAOwD,WAAW,CAACzC,MAAM,CAAC;UAC9B;MACJ;IACF;IAEA,IAAIoC,GAAG,GAAG+B,UAAU,CAAC,CAAC;IACtB,IAAIrE,MAAM,KAAKL,IAAI,CAACS,UAAU,EAC5B,MAAM,iBAAiB;IACzB,OAAOkC,GAAG;EACZ;EAEA,IAAI0C,GAAG,GAAG;IAAEvF,MAAM,EAAEA,MAAM;IAAEgD,MAAM,EAAEA;EAAO,CAAC;EAE5C,IAAI,OAAOwC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAC5CD,MAAM,CAAC,WAAW,EAAED,GAAG,CAAC,CAAC,KACtB,IAAI,OAAOG,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,EACtDD,MAAM,CAACC,OAAO,GAAGJ,GAAG,CAAC,KAClB,IAAI,CAAC9F,MAAM,CAACmG,IAAI,EACnBnG,MAAM,CAACmG,IAAI,GAAGL,GAAG;AAEnB,CAAC,EAAE,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}