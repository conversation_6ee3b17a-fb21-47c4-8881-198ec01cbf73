{"ast": null, "code": "export default function (model, calc) {\n  const fn = (buf, previous) => calc(buf, previous) >>> 0;\n  fn.signed = calc;\n  fn.unsigned = fn;\n  fn.model = model;\n  return fn;\n}", "map": {"version": 3, "names": ["model", "calc", "fn", "buf", "previous", "signed", "unsigned"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/crc/define_crc.js"], "sourcesContent": ["export default function(model, calc) {\n  const fn = (buf, previous) => calc(buf, previous) >>> 0;\n  fn.signed = calc;\n  fn.unsigned = fn;\n  fn.model = model;\n\n  return fn;\n}\n"], "mappings": "AAAA,eAAe,UAASA,KAAK,EAAEC,IAAI,EAAE;EACnC,MAAMC,EAAE,GAAGA,CAACC,GAAG,EAAEC,QAAQ,KAAKH,IAAI,CAACE,GAAG,EAAEC,QAAQ,CAAC,KAAK,CAAC;EACvDF,EAAE,CAACG,MAAM,GAAGJ,IAAI;EAChBC,EAAE,CAACI,QAAQ,GAAGJ,EAAE;EAChBA,EAAE,CAACF,KAAK,GAAGA,KAAK;EAEhB,OAAOE,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module"}