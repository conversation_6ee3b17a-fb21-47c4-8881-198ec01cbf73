{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport startOfUTCWeekYear from \"../startOfUTCWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCWeek(dirtyDate, options) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfUTCWeek(date, options).getTime() - startOfUTCWeekYear(date, options).getTime(); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}", "map": {"version": 3, "names": ["toDate", "startOfUTCWeek", "startOfUTCWeekYear", "requiredArgs", "MILLISECONDS_IN_WEEK", "getUTCWeek", "dirtyDate", "options", "arguments", "date", "diff", "getTime", "Math", "round"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/_lib/getUTCWeek/index.js"], "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport startOfUTCWeekYear from \"../startOfUTCWeekYear/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCWeek(dirtyDate, options) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfUTCWeek(date, options).getTime() - startOfUTCWeekYear(date, options).getTime(); // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,YAAY,MAAM,0BAA0B;AACnD,IAAIC,oBAAoB,GAAG,SAAS,CAAC,CAAC;AACtC;;AAEA,eAAe,SAASC,UAAUA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACrDJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGT,MAAM,CAACM,SAAS,CAAC;EAC5B,IAAII,IAAI,GAAGT,cAAc,CAACQ,IAAI,EAAEF,OAAO,CAAC,CAACI,OAAO,CAAC,CAAC,GAAGT,kBAAkB,CAACO,IAAI,EAAEF,OAAO,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;EAClG;EACA;;EAEA,OAAOC,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGN,oBAAoB,CAAC,GAAG,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}