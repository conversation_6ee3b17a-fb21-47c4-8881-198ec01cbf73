{"ast": null, "code": "'use strict';\n\n/******************************************************************************\n * Created 2008-08-19.\n *\n * Dijkstra path-finding functions. Adapted from the Dijkstar Python project.\n *\n * Copyright (C) 2008\n *   <PERSON> <<EMAIL>>\n *   All rights reserved\n *\n * Licensed under the MIT license.\n *\n *   http://www.opensource.org/licenses/mit-license.php\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n *****************************************************************************/\nvar dijkstra = {\n  single_source_shortest_paths: function (graph, s, d) {\n    // Predecessor map for each node that has been encountered.\n    // node ID => predecessor node ID\n    var predecessors = {};\n\n    // Costs of shortest paths from s to all nodes encountered.\n    // node ID => cost\n    var costs = {};\n    costs[s] = 0;\n\n    // Costs of shortest paths from s to all nodes encountered; differs from\n    // `costs` in that it provides easy access to the node that currently has\n    // the known shortest path from s.\n    // XXX: Do we actually need both `costs` and `open`?\n    var open = dijkstra.PriorityQueue.make();\n    open.push(s, 0);\n    var closest, u, v, cost_of_s_to_u, adjacent_nodes, cost_of_e, cost_of_s_to_u_plus_cost_of_e, cost_of_s_to_v, first_visit;\n    while (!open.empty()) {\n      // In the nodes remaining in graph that have a known cost from s,\n      // find the node, u, that currently has the shortest path from s.\n      closest = open.pop();\n      u = closest.value;\n      cost_of_s_to_u = closest.cost;\n\n      // Get nodes adjacent to u...\n      adjacent_nodes = graph[u] || {};\n\n      // ...and explore the edges that connect u to those nodes, updating\n      // the cost of the shortest paths to any or all of those nodes as\n      // necessary. v is the node across the current edge from u.\n      for (v in adjacent_nodes) {\n        if (adjacent_nodes.hasOwnProperty(v)) {\n          // Get the cost of the edge running from u to v.\n          cost_of_e = adjacent_nodes[v];\n\n          // Cost of s to u plus the cost of u to v across e--this is *a*\n          // cost from s to v that may or may not be less than the current\n          // known cost to v.\n          cost_of_s_to_u_plus_cost_of_e = cost_of_s_to_u + cost_of_e;\n\n          // If we haven't visited v yet OR if the current known cost from s to\n          // v is greater than the new cost we just found (cost of s to u plus\n          // cost of u to v across e), update v's cost in the cost list and\n          // update v's predecessor in the predecessor list (it's now u).\n          cost_of_s_to_v = costs[v];\n          first_visit = typeof costs[v] === 'undefined';\n          if (first_visit || cost_of_s_to_v > cost_of_s_to_u_plus_cost_of_e) {\n            costs[v] = cost_of_s_to_u_plus_cost_of_e;\n            open.push(v, cost_of_s_to_u_plus_cost_of_e);\n            predecessors[v] = u;\n          }\n        }\n      }\n    }\n    if (typeof d !== 'undefined' && typeof costs[d] === 'undefined') {\n      var msg = ['Could not find a path from ', s, ' to ', d, '.'].join('');\n      throw new Error(msg);\n    }\n    return predecessors;\n  },\n  extract_shortest_path_from_predecessor_list: function (predecessors, d) {\n    var nodes = [];\n    var u = d;\n    var predecessor;\n    while (u) {\n      nodes.push(u);\n      predecessor = predecessors[u];\n      u = predecessors[u];\n    }\n    nodes.reverse();\n    return nodes;\n  },\n  find_path: function (graph, s, d) {\n    var predecessors = dijkstra.single_source_shortest_paths(graph, s, d);\n    return dijkstra.extract_shortest_path_from_predecessor_list(predecessors, d);\n  },\n  /**\n   * A very naive priority queue implementation.\n   */\n  PriorityQueue: {\n    make: function (opts) {\n      var T = dijkstra.PriorityQueue,\n        t = {},\n        key;\n      opts = opts || {};\n      for (key in T) {\n        if (T.hasOwnProperty(key)) {\n          t[key] = T[key];\n        }\n      }\n      t.queue = [];\n      t.sorter = opts.sorter || T.default_sorter;\n      return t;\n    },\n    default_sorter: function (a, b) {\n      return a.cost - b.cost;\n    },\n    /**\n     * Add a new item to the queue and ensure the highest priority element\n     * is at the front of the queue.\n     */\n    push: function (value, cost) {\n      var item = {\n        value: value,\n        cost: cost\n      };\n      this.queue.push(item);\n      this.queue.sort(this.sorter);\n    },\n    /**\n     * Return the highest priority element in the queue.\n     */\n    pop: function () {\n      return this.queue.shift();\n    },\n    empty: function () {\n      return this.queue.length === 0;\n    }\n  }\n};\n\n// node.js module exports\nif (typeof module !== 'undefined') {\n  module.exports = dijkstra;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "single_source_shortest_paths", "graph", "s", "d", "predecessors", "costs", "open", "PriorityQueue", "make", "push", "closest", "u", "v", "cost_of_s_to_u", "adjacent_nodes", "cost_of_e", "cost_of_s_to_u_plus_cost_of_e", "cost_of_s_to_v", "first_visit", "empty", "pop", "value", "cost", "hasOwnProperty", "msg", "join", "Error", "extract_shortest_path_from_predecessor_list", "nodes", "predecessor", "reverse", "find_path", "opts", "T", "t", "key", "queue", "sorter", "default_sorter", "a", "b", "item", "sort", "shift", "length", "module", "exports"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/dijkstrajs/dijkstra.js"], "sourcesContent": ["'use strict';\n\n/******************************************************************************\n * Created 2008-08-19.\n *\n * Dijkstra path-finding functions. Adapted from the Dijkstar Python project.\n *\n * Copyright (C) 2008\n *   <PERSON> <<EMAIL>>\n *   All rights reserved\n *\n * Licensed under the MIT license.\n *\n *   http://www.opensource.org/licenses/mit-license.php\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n *****************************************************************************/\nvar dijkstra = {\n  single_source_shortest_paths: function(graph, s, d) {\n    // Predecessor map for each node that has been encountered.\n    // node ID => predecessor node ID\n    var predecessors = {};\n\n    // Costs of shortest paths from s to all nodes encountered.\n    // node ID => cost\n    var costs = {};\n    costs[s] = 0;\n\n    // Costs of shortest paths from s to all nodes encountered; differs from\n    // `costs` in that it provides easy access to the node that currently has\n    // the known shortest path from s.\n    // XXX: Do we actually need both `costs` and `open`?\n    var open = dijkstra.PriorityQueue.make();\n    open.push(s, 0);\n\n    var closest,\n        u, v,\n        cost_of_s_to_u,\n        adjacent_nodes,\n        cost_of_e,\n        cost_of_s_to_u_plus_cost_of_e,\n        cost_of_s_to_v,\n        first_visit;\n    while (!open.empty()) {\n      // In the nodes remaining in graph that have a known cost from s,\n      // find the node, u, that currently has the shortest path from s.\n      closest = open.pop();\n      u = closest.value;\n      cost_of_s_to_u = closest.cost;\n\n      // Get nodes adjacent to u...\n      adjacent_nodes = graph[u] || {};\n\n      // ...and explore the edges that connect u to those nodes, updating\n      // the cost of the shortest paths to any or all of those nodes as\n      // necessary. v is the node across the current edge from u.\n      for (v in adjacent_nodes) {\n        if (adjacent_nodes.hasOwnProperty(v)) {\n          // Get the cost of the edge running from u to v.\n          cost_of_e = adjacent_nodes[v];\n\n          // Cost of s to u plus the cost of u to v across e--this is *a*\n          // cost from s to v that may or may not be less than the current\n          // known cost to v.\n          cost_of_s_to_u_plus_cost_of_e = cost_of_s_to_u + cost_of_e;\n\n          // If we haven't visited v yet OR if the current known cost from s to\n          // v is greater than the new cost we just found (cost of s to u plus\n          // cost of u to v across e), update v's cost in the cost list and\n          // update v's predecessor in the predecessor list (it's now u).\n          cost_of_s_to_v = costs[v];\n          first_visit = (typeof costs[v] === 'undefined');\n          if (first_visit || cost_of_s_to_v > cost_of_s_to_u_plus_cost_of_e) {\n            costs[v] = cost_of_s_to_u_plus_cost_of_e;\n            open.push(v, cost_of_s_to_u_plus_cost_of_e);\n            predecessors[v] = u;\n          }\n        }\n      }\n    }\n\n    if (typeof d !== 'undefined' && typeof costs[d] === 'undefined') {\n      var msg = ['Could not find a path from ', s, ' to ', d, '.'].join('');\n      throw new Error(msg);\n    }\n\n    return predecessors;\n  },\n\n  extract_shortest_path_from_predecessor_list: function(predecessors, d) {\n    var nodes = [];\n    var u = d;\n    var predecessor;\n    while (u) {\n      nodes.push(u);\n      predecessor = predecessors[u];\n      u = predecessors[u];\n    }\n    nodes.reverse();\n    return nodes;\n  },\n\n  find_path: function(graph, s, d) {\n    var predecessors = dijkstra.single_source_shortest_paths(graph, s, d);\n    return dijkstra.extract_shortest_path_from_predecessor_list(\n      predecessors, d);\n  },\n\n  /**\n   * A very naive priority queue implementation.\n   */\n  PriorityQueue: {\n    make: function (opts) {\n      var T = dijkstra.PriorityQueue,\n          t = {},\n          key;\n      opts = opts || {};\n      for (key in T) {\n        if (T.hasOwnProperty(key)) {\n          t[key] = T[key];\n        }\n      }\n      t.queue = [];\n      t.sorter = opts.sorter || T.default_sorter;\n      return t;\n    },\n\n    default_sorter: function (a, b) {\n      return a.cost - b.cost;\n    },\n\n    /**\n     * Add a new item to the queue and ensure the highest priority element\n     * is at the front of the queue.\n     */\n    push: function (value, cost) {\n      var item = {value: value, cost: cost};\n      this.queue.push(item);\n      this.queue.sort(this.sorter);\n    },\n\n    /**\n     * Return the highest priority element in the queue.\n     */\n    pop: function () {\n      return this.queue.shift();\n    },\n\n    empty: function () {\n      return this.queue.length === 0;\n    }\n  }\n};\n\n\n// node.js module exports\nif (typeof module !== 'undefined') {\n  module.exports = dijkstra;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAG;EACbC,4BAA4B,EAAE,SAAAA,CAASC,KAAK,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAClD;IACA;IACA,IAAIC,YAAY,GAAG,CAAC,CAAC;;IAErB;IACA;IACA,IAAIC,KAAK,GAAG,CAAC,CAAC;IACdA,KAAK,CAACH,CAAC,CAAC,GAAG,CAAC;;IAEZ;IACA;IACA;IACA;IACA,IAAII,IAAI,GAAGP,QAAQ,CAACQ,aAAa,CAACC,IAAI,CAAC,CAAC;IACxCF,IAAI,CAACG,IAAI,CAACP,CAAC,EAAE,CAAC,CAAC;IAEf,IAAIQ,OAAO,EACPC,CAAC,EAAEC,CAAC,EACJC,cAAc,EACdC,cAAc,EACdC,SAAS,EACTC,6BAA6B,EAC7BC,cAAc,EACdC,WAAW;IACf,OAAO,CAACZ,IAAI,CAACa,KAAK,CAAC,CAAC,EAAE;MACpB;MACA;MACAT,OAAO,GAAGJ,IAAI,CAACc,GAAG,CAAC,CAAC;MACpBT,CAAC,GAAGD,OAAO,CAACW,KAAK;MACjBR,cAAc,GAAGH,OAAO,CAACY,IAAI;;MAE7B;MACAR,cAAc,GAAGb,KAAK,CAACU,CAAC,CAAC,IAAI,CAAC,CAAC;;MAE/B;MACA;MACA;MACA,KAAKC,CAAC,IAAIE,cAAc,EAAE;QACxB,IAAIA,cAAc,CAACS,cAAc,CAACX,CAAC,CAAC,EAAE;UACpC;UACAG,SAAS,GAAGD,cAAc,CAACF,CAAC,CAAC;;UAE7B;UACA;UACA;UACAI,6BAA6B,GAAGH,cAAc,GAAGE,SAAS;;UAE1D;UACA;UACA;UACA;UACAE,cAAc,GAAGZ,KAAK,CAACO,CAAC,CAAC;UACzBM,WAAW,GAAI,OAAOb,KAAK,CAACO,CAAC,CAAC,KAAK,WAAY;UAC/C,IAAIM,WAAW,IAAID,cAAc,GAAGD,6BAA6B,EAAE;YACjEX,KAAK,CAACO,CAAC,CAAC,GAAGI,6BAA6B;YACxCV,IAAI,CAACG,IAAI,CAACG,CAAC,EAAEI,6BAA6B,CAAC;YAC3CZ,YAAY,CAACQ,CAAC,CAAC,GAAGD,CAAC;UACrB;QACF;MACF;IACF;IAEA,IAAI,OAAOR,CAAC,KAAK,WAAW,IAAI,OAAOE,KAAK,CAACF,CAAC,CAAC,KAAK,WAAW,EAAE;MAC/D,IAAIqB,GAAG,GAAG,CAAC,6BAA6B,EAAEtB,CAAC,EAAE,MAAM,EAAEC,CAAC,EAAE,GAAG,CAAC,CAACsB,IAAI,CAAC,EAAE,CAAC;MACrE,MAAM,IAAIC,KAAK,CAACF,GAAG,CAAC;IACtB;IAEA,OAAOpB,YAAY;EACrB,CAAC;EAEDuB,2CAA2C,EAAE,SAAAA,CAASvB,YAAY,EAAED,CAAC,EAAE;IACrE,IAAIyB,KAAK,GAAG,EAAE;IACd,IAAIjB,CAAC,GAAGR,CAAC;IACT,IAAI0B,WAAW;IACf,OAAOlB,CAAC,EAAE;MACRiB,KAAK,CAACnB,IAAI,CAACE,CAAC,CAAC;MACbkB,WAAW,GAAGzB,YAAY,CAACO,CAAC,CAAC;MAC7BA,CAAC,GAAGP,YAAY,CAACO,CAAC,CAAC;IACrB;IACAiB,KAAK,CAACE,OAAO,CAAC,CAAC;IACf,OAAOF,KAAK;EACd,CAAC;EAEDG,SAAS,EAAE,SAAAA,CAAS9B,KAAK,EAAEC,CAAC,EAAEC,CAAC,EAAE;IAC/B,IAAIC,YAAY,GAAGL,QAAQ,CAACC,4BAA4B,CAACC,KAAK,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACrE,OAAOJ,QAAQ,CAAC4B,2CAA2C,CACzDvB,YAAY,EAAED,CAAC,CAAC;EACpB,CAAC;EAED;AACF;AACA;EACEI,aAAa,EAAE;IACbC,IAAI,EAAE,SAAAA,CAAUwB,IAAI,EAAE;MACpB,IAAIC,CAAC,GAAGlC,QAAQ,CAACQ,aAAa;QAC1B2B,CAAC,GAAG,CAAC,CAAC;QACNC,GAAG;MACPH,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;MACjB,KAAKG,GAAG,IAAIF,CAAC,EAAE;QACb,IAAIA,CAAC,CAACV,cAAc,CAACY,GAAG,CAAC,EAAE;UACzBD,CAAC,CAACC,GAAG,CAAC,GAAGF,CAAC,CAACE,GAAG,CAAC;QACjB;MACF;MACAD,CAAC,CAACE,KAAK,GAAG,EAAE;MACZF,CAAC,CAACG,MAAM,GAAGL,IAAI,CAACK,MAAM,IAAIJ,CAAC,CAACK,cAAc;MAC1C,OAAOJ,CAAC;IACV,CAAC;IAEDI,cAAc,EAAE,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;MAC9B,OAAOD,CAAC,CAACjB,IAAI,GAAGkB,CAAC,CAAClB,IAAI;IACxB,CAAC;IAED;AACJ;AACA;AACA;IACIb,IAAI,EAAE,SAAAA,CAAUY,KAAK,EAAEC,IAAI,EAAE;MAC3B,IAAImB,IAAI,GAAG;QAACpB,KAAK,EAAEA,KAAK;QAAEC,IAAI,EAAEA;MAAI,CAAC;MACrC,IAAI,CAACc,KAAK,CAAC3B,IAAI,CAACgC,IAAI,CAAC;MACrB,IAAI,CAACL,KAAK,CAACM,IAAI,CAAC,IAAI,CAACL,MAAM,CAAC;IAC9B,CAAC;IAED;AACJ;AACA;IACIjB,GAAG,EAAE,SAAAA,CAAA,EAAY;MACf,OAAO,IAAI,CAACgB,KAAK,CAACO,KAAK,CAAC,CAAC;IAC3B,CAAC;IAEDxB,KAAK,EAAE,SAAAA,CAAA,EAAY;MACjB,OAAO,IAAI,CAACiB,KAAK,CAACQ,MAAM,KAAK,CAAC;IAChC;EACF;AACF,CAAC;;AAGD;AACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACjCA,MAAM,CAACC,OAAO,GAAG/C,QAAQ;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "script"}