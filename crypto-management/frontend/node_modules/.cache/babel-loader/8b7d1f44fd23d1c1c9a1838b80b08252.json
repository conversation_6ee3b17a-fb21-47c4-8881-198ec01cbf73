{"ast": null, "code": "import { Buffer } from 'buffer';\nimport createBuffer from './create_buffer';\nimport defineCrc from './define_crc';\nconst crc1 = defineCrc('crc1', function (buf, previous) {\n  if (!Buffer.isBuffer(buf)) buf = createBuffer(buf);\n  let crc = ~~previous;\n  let accum = 0;\n  for (let index = 0; index < buf.length; index++) {\n    const byte = buf[index];\n    accum += byte;\n  }\n  crc += accum % 256;\n  return crc % 256;\n});\nexport default crc1;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "createBuffer", "defineCrc", "crc1", "buf", "previous", "<PERSON><PERSON><PERSON><PERSON>", "crc", "accum", "index", "length", "byte"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/crc/crc1.js"], "sourcesContent": ["import { Buffer } from 'buffer';\nimport createBuffer from './create_buffer';\nimport defineCrc from './define_crc';\n\nconst crc1 = defineCrc('crc1', function(buf, previous) {\n  if (!Buffer.isBuffer(buf)) buf = createBuffer(buf);\n\n  let crc = ~~previous;\n  let accum = 0;\n\n  for (let index = 0; index < buf.length; index++) {\n    const byte = buf[index];\n    accum += byte;\n  }\n\n  crc += accum % 256;\n  return crc % 256;\n});\n\nexport default crc1;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,SAAS,MAAM,cAAc;AAEpC,MAAMC,IAAI,GAAGD,SAAS,CAAC,MAAM,EAAE,UAASE,GAAG,EAAEC,QAAQ,EAAE;EACrD,IAAI,CAACL,MAAM,CAACM,QAAQ,CAACF,GAAG,CAAC,EAAEA,GAAG,GAAGH,YAAY,CAACG,GAAG,CAAC;EAElD,IAAIG,GAAG,GAAG,CAAC,CAACF,QAAQ;EACpB,IAAIG,KAAK,GAAG,CAAC;EAEb,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGL,GAAG,CAACM,MAAM,EAAED,KAAK,EAAE,EAAE;IAC/C,MAAME,IAAI,GAAGP,GAAG,CAACK,KAAK,CAAC;IACvBD,KAAK,IAAIG,IAAI;EACf;EAEAJ,GAAG,IAAIC,KAAK,GAAG,GAAG;EAClB,OAAOD,GAAG,GAAG,GAAG;AAClB,CAAC,CAAC;AAEF,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module"}