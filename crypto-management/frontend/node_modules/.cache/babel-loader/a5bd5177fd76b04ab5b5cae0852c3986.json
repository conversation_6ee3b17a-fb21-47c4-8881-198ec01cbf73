{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfQuarter\n * @category Quarter Helpers\n * @summary Return the end of a year quarter for the given date.\n *\n * @description\n * Return the end of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of a quarter\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of a quarter for 2 September 2014 11:55:00:\n * const result = endOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\n\nexport default function endOfQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var currentMonth = date.getMonth();\n  var month = currentMonth - currentMonth % 3 + 3;\n  date.setMonth(month, 0);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "endOfQuarter", "dirtyDate", "arguments", "date", "currentMonth", "getMonth", "month", "setMonth", "setHours"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/endOfQuarter/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfQuarter\n * @category Quarter Helpers\n * @summary Return the end of a year quarter for the given date.\n *\n * @description\n * Return the end of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of a quarter\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of a quarter for 2 September 2014 11:55:00:\n * const result = endOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\n\nexport default function endOfQuarter(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var currentMonth = date.getMonth();\n  var month = currentMonth - currentMonth % 3 + 3;\n  date.setMonth(month, 0);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC9CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGL,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIG,YAAY,GAAGD,IAAI,CAACE,QAAQ,CAAC,CAAC;EAClC,IAAIC,KAAK,GAAGF,YAAY,GAAGA,YAAY,GAAG,CAAC,GAAG,CAAC;EAC/CD,IAAI,CAACI,QAAQ,CAACD,KAAK,EAAE,CAAC,CAAC;EACvBH,IAAI,CAACK,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC9B,OAAOL,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}