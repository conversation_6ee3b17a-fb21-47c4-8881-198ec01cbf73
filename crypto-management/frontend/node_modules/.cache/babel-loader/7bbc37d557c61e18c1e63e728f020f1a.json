{"ast": null, "code": "import assign from \"../assign/index.js\";\nexport default function cloneObject(dirtyObject) {\n  return assign({}, dirtyObject);\n}", "map": {"version": 3, "names": ["assign", "cloneObject", "dirtyObject"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/_lib/cloneObject/index.js"], "sourcesContent": ["import assign from \"../assign/index.js\";\nexport default function cloneObject(dirtyObject) {\n  return assign({}, dirtyObject);\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,eAAe,SAASC,WAAWA,CAACC,WAAW,EAAE;EAC/C,OAAOF,MAAM,CAAC,CAAC,CAAC,EAAEE,WAAW,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}