{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport addISOWeekYears from \"../addISOWeekYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Subtract the specified number of ISO week-numbering years from the given date.\n *\n * @description\n * Subtract the specified number of ISO week-numbering years from the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - The function was renamed from `subISOYears` to `subISOWeekYears`.\n *   \"ISO week year\" is short for [ISO week-numbering year](https://en.wikipedia.org/wiki/ISO_week_date).\n *   This change makes the name consistent with\n *   locale-dependent week-numbering year helpers, e.g., `setWeekYear`.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of ISO week-numbering years to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the ISO week-numbering years subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 5 ISO week-numbering years from 1 September 2014:\n * const result = subISOWeekYears(new Date(2014, 8, 1), 5)\n * //=> Mon Aug 31 2009 00:00:00\n */\n\nexport default function subISOWeekYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addISOWeekYears(dirtyDate, -amount);\n}", "map": {"version": 3, "names": ["toInteger", "addISOWeekYears", "requiredArgs", "subISOWeekYears", "dirtyDate", "dirtyAmount", "arguments", "amount"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/subISOWeekYears/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport addISOWeekYears from \"../addISOWeekYears/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name subISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Subtract the specified number of ISO week-numbering years from the given date.\n *\n * @description\n * Subtract the specified number of ISO week-numbering years from the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - The function was renamed from `subISOYears` to `subISOWeekYears`.\n *   \"ISO week year\" is short for [ISO week-numbering year](https://en.wikipedia.org/wiki/ISO_week_date).\n *   This change makes the name consistent with\n *   locale-dependent week-numbering year helpers, e.g., `setWeekYear`.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} amount - the amount of ISO week-numbering years to be subtracted. Positive decimals will be rounded using `Math.floor`, decimals less than zero will be rounded using `Math.ceil`.\n * @returns {Date} the new date with the ISO week-numbering years subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract 5 ISO week-numbering years from 1 September 2014:\n * const result = subISOWeekYears(new Date(2014, 8, 1), 5)\n * //=> Mon Aug 31 2009 00:00:00\n */\n\nexport default function subISOWeekYears(dirtyDate, dirtyAmount) {\n  requiredArgs(2, arguments);\n  var amount = toInteger(dirtyAmount);\n  return addISOWeekYears(dirtyDate, -amount);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,eAAeA,CAACC,SAAS,EAAEC,WAAW,EAAE;EAC9DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,MAAM,GAAGP,SAAS,CAACK,WAAW,CAAC;EACnC,OAAOJ,eAAe,CAACG,SAAS,EAAE,CAACG,MAAM,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}