{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { noCase } from \"no-case\";\nexport function dotCase(input, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return noCase(input, __assign({\n    delimiter: \".\"\n  }, options));\n}", "map": {"version": 3, "names": ["noCase", "dotCase", "input", "options", "__assign", "delimiter"], "sources": ["../src/index.ts"], "sourcesContent": ["import { noCase, Options } from \"no-case\";\n\nexport { Options };\n\nexport function dotCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \".\",\n    ...options,\n  });\n}\n"], "mappings": ";AAAA,SAASA,MAAM,QAAiB,SAAS;AAIzC,OAAM,SAAUC,OAAOA,CAACC,KAAa,EAAEC,OAAqB;EAArB,IAAAA,OAAA;IAAAA,OAAA,KAAqB;EAAA;EAC1D,OAAOH,MAAM,CAACE,KAAK,EAAAE,QAAA;IACjBC,SAAS,EAAE;EAAG,GACXF,OAAO,EACV;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}