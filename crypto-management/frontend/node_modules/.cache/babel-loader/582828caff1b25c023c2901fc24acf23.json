{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInHour } from \"../constants/index.js\";\n/**\n * @name hoursToSeconds\n * @category Conversion Helpers\n * @summary Convert hours to seconds.\n *\n * @description\n * Convert a number of hours to a full number of seconds.\n *\n * @param {number} hours - number of hours to be converted\n *\n * @returns {number} the number of hours converted in seconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 hours to seconds:\n * const result = hoursToSeconds(2)\n * //=> 7200\n */\n\nexport default function hoursToSeconds(hours) {\n  requiredArgs(1, arguments);\n  return Math.floor(hours * secondsInHour);\n}", "map": {"version": 3, "names": ["requiredArgs", "secondsInHour", "hoursToSeconds", "hours", "arguments", "Math", "floor"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/hoursToSeconds/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInHour } from \"../constants/index.js\";\n/**\n * @name hoursToSeconds\n * @category Conversion Helpers\n * @summary Convert hours to seconds.\n *\n * @description\n * Convert a number of hours to a full number of seconds.\n *\n * @param {number} hours - number of hours to be converted\n *\n * @returns {number} the number of hours converted in seconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 hours to seconds:\n * const result = hoursToSeconds(2)\n * //=> 7200\n */\n\nexport default function hoursToSeconds(hours) {\n  requiredArgs(1, arguments);\n  return Math.floor(hours * secondsInHour);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC5CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAGF,aAAa,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}