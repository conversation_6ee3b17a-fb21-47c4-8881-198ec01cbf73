{"ast": null, "code": "import differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport format from \"../format/index.js\";\nimport defaultLocale from \"../locale/en-US/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name formatRelative\n * @category Common Helpers\n * @summary Represent the date in words relative to the given base date.\n *\n * @description\n * Represent the date in words relative to the given base date.\n *\n * | Distance to the base date | Result                    |\n * |---------------------------|---------------------------|\n * | Previous 6 days           | last Sunday at 04:30 AM   |\n * | Last day                  | yesterday at 04:30 AM     |\n * | Same day                  | today at 04:30 AM         |\n * | Next day                  | tomorrow at 04:30 AM      |\n * | Next 6 days               | Sunday at 04:30 AM        |\n * | Other                     | 12/31/2017                |\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to format\n * @param {Date|Number} baseDate - the date to compare with\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {String} the date in words\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.locale` must contain `localize` property\n * @throws {RangeError} `options.locale` must contain `formatLong` property\n * @throws {RangeError} `options.locale` must contain `formatRelative` property\n *\n * @example\n * // Represent the date of 6 days ago in words relative to the given base date. In this example, today is Wednesday\n * const result = formatRelative(addDays(new Date(), -6), new Date())\n * //=> \"last Thursday at 12:45 AM\"\n */\nexport default function formatRelative(dirtyDate, dirtyBaseDate, dirtyOptions) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var baseDate = toDate(dirtyBaseDate);\n  var _ref = dirtyOptions || {},\n    _ref$locale = _ref.locale,\n    locale = _ref$locale === void 0 ? defaultLocale : _ref$locale,\n    _ref$weekStartsOn = _ref.weekStartsOn,\n    weekStartsOn = _ref$weekStartsOn === void 0 ? 0 : _ref$weekStartsOn;\n  if (!locale.localize) {\n    throw new RangeError('locale must contain localize property');\n  }\n  if (!locale.formatLong) {\n    throw new RangeError('locale must contain formatLong property');\n  }\n  if (!locale.formatRelative) {\n    throw new RangeError('locale must contain formatRelative property');\n  }\n  var diff = differenceInCalendarDays(date, baseDate);\n  if (isNaN(diff)) {\n    throw new RangeError('Invalid time value');\n  }\n  var token;\n  if (diff < -6) {\n    token = 'other';\n  } else if (diff < -1) {\n    token = 'lastWeek';\n  } else if (diff < 0) {\n    token = 'yesterday';\n  } else if (diff < 1) {\n    token = 'today';\n  } else if (diff < 2) {\n    token = 'tomorrow';\n  } else if (diff < 7) {\n    token = 'nextWeek';\n  } else {\n    token = 'other';\n  }\n  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));\n  var utcBaseDate = subMilliseconds(baseDate, getTimezoneOffsetInMilliseconds(baseDate));\n  var formatStr = locale.formatRelative(token, utcDate, utcBaseDate, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n  return format(date, formatStr, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n}", "map": {"version": 3, "names": ["differenceInCalendarDays", "format", "defaultLocale", "subMilliseconds", "toDate", "getTimezoneOffsetInMilliseconds", "requiredArgs", "formatRelative", "dirtyDate", "dirtyBaseDate", "dirtyOptions", "arguments", "date", "baseDate", "_ref", "_ref$locale", "locale", "_ref$weekStartsOn", "weekStartsOn", "localize", "RangeError", "formatLong", "diff", "isNaN", "token", "utcDate", "utcBaseDate", "formatStr"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/formatRelative/index.js"], "sourcesContent": ["import differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport format from \"../format/index.js\";\nimport defaultLocale from \"../locale/en-US/index.js\";\nimport subMilliseconds from \"../subMilliseconds/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getTimezoneOffsetInMilliseconds from \"../_lib/getTimezoneOffsetInMilliseconds/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name formatRelative\n * @category Common Helpers\n * @summary Represent the date in words relative to the given base date.\n *\n * @description\n * Represent the date in words relative to the given base date.\n *\n * | Distance to the base date | Result                    |\n * |---------------------------|---------------------------|\n * | Previous 6 days           | last Sunday at 04:30 AM   |\n * | Last day                  | yesterday at 04:30 AM     |\n * | Same day                  | today at 04:30 AM         |\n * | Next day                  | tomorrow at 04:30 AM      |\n * | Next 6 days               | Sunday at 04:30 AM        |\n * | Other                     | 12/31/2017                |\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to format\n * @param {Date|Number} baseDate - the date to compare with\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {String} the date in words\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `baseDate` must not be Invalid Date\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.locale` must contain `localize` property\n * @throws {RangeError} `options.locale` must contain `formatLong` property\n * @throws {RangeError} `options.locale` must contain `formatRelative` property\n *\n * @example\n * // Represent the date of 6 days ago in words relative to the given base date. In this example, today is Wednesday\n * const result = formatRelative(addDays(new Date(), -6), new Date())\n * //=> \"last Thursday at 12:45 AM\"\n */\nexport default function formatRelative(dirtyDate, dirtyBaseDate, dirtyOptions) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var baseDate = toDate(dirtyBaseDate);\n\n  var _ref = dirtyOptions || {},\n      _ref$locale = _ref.locale,\n      locale = _ref$locale === void 0 ? defaultLocale : _ref$locale,\n      _ref$weekStartsOn = _ref.weekStartsOn,\n      weekStartsOn = _ref$weekStartsOn === void 0 ? 0 : _ref$weekStartsOn;\n\n  if (!locale.localize) {\n    throw new RangeError('locale must contain localize property');\n  }\n\n  if (!locale.formatLong) {\n    throw new RangeError('locale must contain formatLong property');\n  }\n\n  if (!locale.formatRelative) {\n    throw new RangeError('locale must contain formatRelative property');\n  }\n\n  var diff = differenceInCalendarDays(date, baseDate);\n\n  if (isNaN(diff)) {\n    throw new RangeError('Invalid time value');\n  }\n\n  var token;\n\n  if (diff < -6) {\n    token = 'other';\n  } else if (diff < -1) {\n    token = 'lastWeek';\n  } else if (diff < 0) {\n    token = 'yesterday';\n  } else if (diff < 1) {\n    token = 'today';\n  } else if (diff < 2) {\n    token = 'tomorrow';\n  } else if (diff < 7) {\n    token = 'nextWeek';\n  } else {\n    token = 'other';\n  }\n\n  var utcDate = subMilliseconds(date, getTimezoneOffsetInMilliseconds(date));\n  var utcBaseDate = subMilliseconds(baseDate, getTimezoneOffsetInMilliseconds(baseDate));\n  var formatStr = locale.formatRelative(token, utcDate, utcBaseDate, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n  return format(date, formatStr, {\n    locale: locale,\n    weekStartsOn: weekStartsOn\n  });\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,+BAA+B,MAAM,kDAAkD;AAC9F,OAAOC,YAAY,MAAM,+BAA+B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAEC,aAAa,EAAEC,YAAY,EAAE;EAC7EJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGR,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAIK,QAAQ,GAAGT,MAAM,CAACK,aAAa,CAAC;EAEpC,IAAIK,IAAI,GAAGJ,YAAY,IAAI,CAAC,CAAC;IACzBK,WAAW,GAAGD,IAAI,CAACE,MAAM;IACzBA,MAAM,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAGb,aAAa,GAAGa,WAAW;IAC7DE,iBAAiB,GAAGH,IAAI,CAACI,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;EAEvE,IAAI,CAACD,MAAM,CAACG,QAAQ,EAAE;IACpB,MAAM,IAAIC,UAAU,CAAC,uCAAuC,CAAC;EAC/D;EAEA,IAAI,CAACJ,MAAM,CAACK,UAAU,EAAE;IACtB,MAAM,IAAID,UAAU,CAAC,yCAAyC,CAAC;EACjE;EAEA,IAAI,CAACJ,MAAM,CAACT,cAAc,EAAE;IAC1B,MAAM,IAAIa,UAAU,CAAC,6CAA6C,CAAC;EACrE;EAEA,IAAIE,IAAI,GAAGtB,wBAAwB,CAACY,IAAI,EAAEC,QAAQ,CAAC;EAEnD,IAAIU,KAAK,CAACD,IAAI,CAAC,EAAE;IACf,MAAM,IAAIF,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,IAAII,KAAK;EAET,IAAIF,IAAI,GAAG,CAAC,CAAC,EAAE;IACbE,KAAK,GAAG,OAAO;EACjB,CAAC,MAAM,IAAIF,IAAI,GAAG,CAAC,CAAC,EAAE;IACpBE,KAAK,GAAG,UAAU;EACpB,CAAC,MAAM,IAAIF,IAAI,GAAG,CAAC,EAAE;IACnBE,KAAK,GAAG,WAAW;EACrB,CAAC,MAAM,IAAIF,IAAI,GAAG,CAAC,EAAE;IACnBE,KAAK,GAAG,OAAO;EACjB,CAAC,MAAM,IAAIF,IAAI,GAAG,CAAC,EAAE;IACnBE,KAAK,GAAG,UAAU;EACpB,CAAC,MAAM,IAAIF,IAAI,GAAG,CAAC,EAAE;IACnBE,KAAK,GAAG,UAAU;EACpB,CAAC,MAAM;IACLA,KAAK,GAAG,OAAO;EACjB;EAEA,IAAIC,OAAO,GAAGtB,eAAe,CAACS,IAAI,EAAEP,+BAA+B,CAACO,IAAI,CAAC,CAAC;EAC1E,IAAIc,WAAW,GAAGvB,eAAe,CAACU,QAAQ,EAAER,+BAA+B,CAACQ,QAAQ,CAAC,CAAC;EACtF,IAAIc,SAAS,GAAGX,MAAM,CAACT,cAAc,CAACiB,KAAK,EAAEC,OAAO,EAAEC,WAAW,EAAE;IACjEV,MAAM,EAAEA,MAAM;IACdE,YAAY,EAAEA;EAChB,CAAC,CAAC;EACF,OAAOjB,MAAM,CAACW,IAAI,EAAEe,SAAS,EAAE;IAC7BX,MAAM,EAAEA,MAAM;IACdE,YAAY,EAAEA;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}