{"ast": null, "code": "/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nvar SUPPORTED_LOCALE = {\n  tr: {\n    regexp: /[\\u0069]/g,\n    map: {\n      i: \"\\u0130\"\n    }\n  },\n  az: {\n    regexp: /[\\u0069]/g,\n    map: {\n      i: \"\\u0130\"\n    }\n  },\n  lt: {\n    regexp: /[\\u0069\\u006A\\u012F]\\u0307|\\u0069\\u0307[\\u0300\\u0301\\u0303]/g,\n    map: {\n      i̇: \"\\u0049\",\n      j̇: \"\\u004A\",\n      į̇: \"\\u012E\",\n      i̇̀: \"\\u00CC\",\n      i̇́: \"\\u00CD\",\n      i̇̃: \"\\u0128\"\n    }\n  }\n};\n/**\n * Localized upper case.\n */\nexport function localeUpperCase(str, locale) {\n  var lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n  if (lang) return upperCase(str.replace(lang.regexp, function (m) {\n    return lang.map[m];\n  }));\n  return upperCase(str);\n}\n/**\n * Upper case as a function.\n */\nexport function upperCase(str) {\n  return str.toUpperCase();\n}", "map": {"version": 3, "names": ["SUPPORTED_LOCALE", "tr", "regexp", "map", "i", "az", "lt", "i̇", "j̇", "į̇", "i̇̀", "i̇́", "i̇̃", "localeUpperCase", "str", "locale", "lang", "toLowerCase", "upperCase", "replace", "m", "toUpperCase"], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * Locale character mapping rules.\n */\ninterface Locale {\n  regexp: RegExp;\n  map: Record<string, string>;\n}\n\n/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nconst SUPPORTED_LOCALE: Record<string, Locale> = {\n  tr: {\n    regexp: /[\\u0069]/g,\n    map: {\n      i: \"\\u0130\",\n    },\n  },\n  az: {\n    regexp: /[\\u0069]/g,\n    map: {\n      i: \"\\u0130\",\n    },\n  },\n  lt: {\n    regexp: /[\\u0069\\u006A\\u012F]\\u0307|\\u0069\\u0307[\\u0300\\u0301\\u0303]/g,\n    map: {\n      i̇: \"\\u0049\",\n      j̇: \"\\u004A\",\n      į̇: \"\\u012E\",\n      i̇̀: \"\\u00CC\",\n      i̇́: \"\\u00CD\",\n      i̇̃: \"\\u0128\",\n    },\n  },\n};\n\n/**\n * Localized upper case.\n */\nexport function localeUpperCase(str: string, locale: string) {\n  const lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n  if (lang) return upperCase(str.replace(lang.regexp, (m) => lang.map[m]));\n  return upperCase(str);\n}\n\n/**\n * Upper case as a function.\n */\nexport function upperCase(str: string) {\n  return str.toUpperCase();\n}\n"], "mappings": "AAQA;;;AAGA,IAAMA,gBAAgB,GAA2B;EAC/CC,EAAE,EAAE;IACFC,MAAM,EAAE,WAAW;IACnBC,GAAG,EAAE;MACHC,CAAC,EAAE;;GAEN;EACDC,EAAE,EAAE;IACFH,MAAM,EAAE,WAAW;IACnBC,GAAG,EAAE;MACHC,CAAC,EAAE;;GAEN;EACDE,EAAE,EAAE;IACFJ,MAAM,EAAE,8DAA8D;IACtEC,GAAG,EAAE;MACHI,EAAE,EAAE,QAAQ;MACZC,EAAE,EAAE,QAAQ;MACZC,EAAE,EAAE,QAAQ;MACZC,GAAG,EAAE,QAAQ;MACbC,GAAG,EAAE,QAAQ;MACbC,GAAG,EAAE;;;CAGV;AAED;;;AAGA,OAAM,SAAUC,eAAeA,CAACC,GAAW,EAAEC,MAAc;EACzD,IAAMC,IAAI,GAAGhB,gBAAgB,CAACe,MAAM,CAACE,WAAW,EAAE,CAAC;EACnD,IAAID,IAAI,EAAE,OAAOE,SAAS,CAACJ,GAAG,CAACK,OAAO,CAACH,IAAI,CAACd,MAAM,EAAE,UAACkB,CAAC;IAAK,OAAAJ,IAAI,CAACb,GAAG,CAACiB,CAAC,CAAC;EAAX,CAAW,CAAC,CAAC;EACxE,OAAOF,SAAS,CAACJ,GAAG,CAAC;AACvB;AAEA;;;AAGA,OAAM,SAAUI,SAASA,CAACJ,GAAW;EACnC,OAAOA,GAAG,CAACO,WAAW,EAAE;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}