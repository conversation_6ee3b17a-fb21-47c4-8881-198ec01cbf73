{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { minutesInHour } from \"../constants/index.js\";\n/**\n * @name hoursToMinutes\n * @category Conversion Helpers\n * @summary Convert hours to minutes.\n *\n * @description\n * Convert a number of hours to a full number of minutes.\n *\n * @param {number} hours - number of hours to be converted\n *\n * @returns {number} the number of hours converted in minutes\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 hours to minutes:\n * const result = hoursToMinutes(2)\n * //=> 120\n */\n\nexport default function hoursToMinutes(hours) {\n  requiredArgs(1, arguments);\n  return Math.floor(hours * minutesInHour);\n}", "map": {"version": 3, "names": ["requiredArgs", "minutesInHour", "hoursToMinutes", "hours", "arguments", "Math", "floor"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/hoursToMinutes/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { minutesInHour } from \"../constants/index.js\";\n/**\n * @name hoursToMinutes\n * @category Conversion Helpers\n * @summary Convert hours to minutes.\n *\n * @description\n * Convert a number of hours to a full number of minutes.\n *\n * @param {number} hours - number of hours to be converted\n *\n * @returns {number} the number of hours converted in minutes\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 hours to minutes:\n * const result = hoursToMinutes(2)\n * //=> 120\n */\n\nexport default function hoursToMinutes(hours) {\n  requiredArgs(1, arguments);\n  return Math.floor(hours * minutesInHour);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC5CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOC,IAAI,CAACC,KAAK,CAACH,KAAK,GAAGF,aAAa,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}