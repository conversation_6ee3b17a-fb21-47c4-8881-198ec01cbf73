{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport MarkerModel from './MarkerModel.js';\nvar MarkPointModel = /** @class */\nfunction (_super) {\n  __extends(MarkPointModel, _super);\n  function MarkPointModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkPointModel.type;\n    return _this;\n  }\n  MarkPointModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {\n    return new MarkPointModel(markerOpt, masterMarkerModel, ecModel);\n  };\n  MarkPointModel.type = 'markPoint';\n  MarkPointModel.defaultOption = {\n    // zlevel: 0,\n    z: 5,\n    symbol: 'pin',\n    symbolSize: 50,\n    //symbolRotate: 0,\n    //symbolOffset: [0, 0]\n    tooltip: {\n      trigger: 'item'\n    },\n    label: {\n      show: true,\n      position: 'inside'\n    },\n    itemStyle: {\n      borderWidth: 2\n    },\n    emphasis: {\n      label: {\n        show: true\n      }\n    }\n  };\n  return MarkPointModel;\n}(MarkerModel);\nexport default MarkPointModel;", "map": {"version": 3, "names": ["__extends", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MarkPointModel", "_super", "_this", "apply", "arguments", "type", "prototype", "createMarkerModelFromSeries", "markerOpt", "masterMarkerModel", "ecModel", "defaultOption", "z", "symbol", "symbolSize", "tooltip", "trigger", "label", "show", "position", "itemStyle", "borderWidth", "emphasis"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/echarts/lib/component/marker/MarkPointModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport MarkerModel from './MarkerModel.js';\n\nvar MarkPointModel =\n/** @class */\nfunction (_super) {\n  __extends(MarkPointModel, _super);\n\n  function MarkPointModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = MarkPointModel.type;\n    return _this;\n  }\n\n  MarkPointModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {\n    return new MarkPointModel(markerOpt, masterMarkerModel, ecModel);\n  };\n\n  MarkPointModel.type = 'markPoint';\n  MarkPointModel.defaultOption = {\n    // zlevel: 0,\n    z: 5,\n    symbol: 'pin',\n    symbolSize: 50,\n    //symbolRotate: 0,\n    //symbolOffset: [0, 0]\n    tooltip: {\n      trigger: 'item'\n    },\n    label: {\n      show: true,\n      position: 'inside'\n    },\n    itemStyle: {\n      borderWidth: 2\n    },\n    emphasis: {\n      label: {\n        show: true\n      }\n    }\n  };\n  return MarkPointModel;\n}(MarkerModel);\n\nexport default MarkPointModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,kBAAkB;AAE1C,IAAIC,cAAc,GAClB;AACA,UAAUC,MAAM,EAAE;EAChBH,SAAS,CAACE,cAAc,EAAEC,MAAM,CAAC;EAEjC,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,cAAc,CAACK,IAAI;IAChC,OAAOH,KAAK;EACd;EAEAF,cAAc,CAACM,SAAS,CAACC,2BAA2B,GAAG,UAAUC,SAAS,EAAEC,iBAAiB,EAAEC,OAAO,EAAE;IACtG,OAAO,IAAIV,cAAc,CAACQ,SAAS,EAAEC,iBAAiB,EAAEC,OAAO,CAAC;EAClE,CAAC;EAEDV,cAAc,CAACK,IAAI,GAAG,WAAW;EACjCL,cAAc,CAACW,aAAa,GAAG;IAC7B;IACAC,CAAC,EAAE,CAAC;IACJC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,EAAE;IACd;IACA;IACAC,OAAO,EAAE;MACPC,OAAO,EAAE;IACX,CAAC;IACDC,KAAK,EAAE;MACLC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACTC,WAAW,EAAE;IACf,CAAC;IACDC,QAAQ,EAAE;MACRL,KAAK,EAAE;QACLC,IAAI,EAAE;MACR;IACF;EACF,CAAC;EACD,OAAOlB,cAAc;AACvB,CAAC,CAACD,WAAW,CAAC;AAEd,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}