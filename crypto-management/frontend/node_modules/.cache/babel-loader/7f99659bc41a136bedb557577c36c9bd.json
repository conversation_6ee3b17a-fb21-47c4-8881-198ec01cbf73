{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isTuesday\n * @category Weekday Helpers\n * @summary Is the given date Tuesday?\n *\n * @description\n * Is the given date Tuesday?\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Tuesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 23 September 2014 Tuesday?\n * var result = isTuesday(new Date(2014, 8, 23))\n * //=> true\n */\n\nexport default function isTuesday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 2;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "isTuesday", "dirtyDate", "arguments", "getDay"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/isTuesday/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isTuesday\n * @category Weekday Helpers\n * @summary Is the given date Tuesday?\n *\n * @description\n * Is the given date Tuesday?\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Tuesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 23 September 2014 Tuesday?\n * var result = isTuesday(new Date(2014, 8, 23))\n * //=> true\n */\n\nexport default function isTuesday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 2;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,SAASA,CAACC,SAAS,EAAE;EAC3CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,MAAM,CAACG,SAAS,CAAC,CAACE,MAAM,CAAC,CAAC,KAAK,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}