{"ast": null, "code": "import getISOWeekYear from \"../getISOWeekYear/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the end of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the end of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - The function was renamed from `endOfISOYear` to `endOfISOWeekYear`.\n *   \"ISO week year\" is short for [ISO week-numbering year](https://en.wikipedia.org/wiki/ISO_week_date).\n *   This change makes the name consistent with\n *   locale-dependent week-numbering year helpers, e.g., `addWeekYears`.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of an ISO week-numbering year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of an ISO week-numbering year for 2 July 2005:\n * const result = endOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Sun Jan 01 2006 23:59:59.999\n */\n\nexport default function endOfISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getISOWeekYear(dirtyDate);\n  var fourthOfJanuaryOfNextYear = new Date(0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  var date = startOfISOWeek(fourthOfJanuaryOfNextYear);\n  date.setMilliseconds(date.getMilliseconds() - 1);\n  return date;\n}", "map": {"version": 3, "names": ["getISOWeekYear", "startOfISOWeek", "requiredArgs", "endOfISOWeekYear", "dirtyDate", "arguments", "year", "fourthOfJanuaryOfNextYear", "Date", "setFullYear", "setHours", "date", "setMilliseconds", "getMilliseconds"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/endOfISOWeekYear/index.js"], "sourcesContent": ["import getISOWeekYear from \"../getISOWeekYear/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name endOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the end of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the end of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - The function was renamed from `endOfISOYear` to `endOfISOWeekYear`.\n *   \"ISO week year\" is short for [ISO week-numbering year](https://en.wikipedia.org/wiki/ISO_week_date).\n *   This change makes the name consistent with\n *   locale-dependent week-numbering year helpers, e.g., `addWeekYears`.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the end of an ISO week-numbering year\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The end of an ISO week-numbering year for 2 July 2005:\n * const result = endOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Sun Jan 01 2006 23:59:59.999\n */\n\nexport default function endOfISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getISOWeekYear(dirtyDate);\n  var fourthOfJanuaryOfNextYear = new Date(0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  var date = startOfISOWeek(fourthOfJanuaryOfNextYear);\n  date.setMilliseconds(date.getMilliseconds() - 1);\n  return date;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,4BAA4B;AACvD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EAClDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,cAAc,CAACI,SAAS,CAAC;EACpC,IAAIG,yBAAyB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC3CD,yBAAyB,CAACE,WAAW,CAACH,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACrDC,yBAAyB,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9C,IAAIC,IAAI,GAAGV,cAAc,CAACM,yBAAyB,CAAC;EACpDI,IAAI,CAACC,eAAe,CAACD,IAAI,CAACE,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;EAChD,OAAOF,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}