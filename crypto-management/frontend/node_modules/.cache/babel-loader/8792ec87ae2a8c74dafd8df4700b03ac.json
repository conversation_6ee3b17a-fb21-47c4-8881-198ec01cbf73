{"ast": null, "code": "import { Buffer } from 'buffer';\nimport createBuffer from './create_buffer';\nimport defineCrc from './define_crc';\nconst crc16xmodem = defineCrc('xmodem', function (buf, previous) {\n  if (!Buffer.isBuffer(buf)) buf = createBuffer(buf);\n  let crc = typeof previous !== 'undefined' ? ~~previous : 0x0;\n  for (let index = 0; index < buf.length; index++) {\n    const byte = buf[index];\n    let code = crc >>> 8 & 0xff;\n    code ^= byte & 0xff;\n    code ^= code >>> 4;\n    crc = crc << 8 & 0xffff;\n    crc ^= code;\n    code = code << 5 & 0xffff;\n    crc ^= code;\n    code = code << 7 & 0xffff;\n    crc ^= code;\n  }\n  return crc;\n});\nexport default crc16xmodem;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "createBuffer", "defineCrc", "crc16xmodem", "buf", "previous", "<PERSON><PERSON><PERSON><PERSON>", "crc", "index", "length", "byte", "code"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/crc/crc16xmodem.js"], "sourcesContent": ["import { Buffer } from 'buffer';\nimport createBuffer from './create_buffer';\nimport defineCrc from './define_crc';\n\nconst crc16xmodem = defineCrc('xmodem', function(buf, previous) {\n  if (!Buffer.isBuffer(buf)) buf = createBuffer(buf);\n\n  let crc = typeof previous !== 'undefined' ? ~~previous : 0x0;\n\n  for (let index = 0; index < buf.length; index++) {\n    const byte = buf[index];\n    let code = (crc >>> 8) & 0xff;\n\n    code ^= byte & 0xff;\n    code ^= code >>> 4;\n    crc = (crc << 8) & 0xffff;\n    crc ^= code;\n    code = (code << 5) & 0xffff;\n    crc ^= code;\n    code = (code << 7) & 0xffff;\n    crc ^= code;\n  }\n\n  return crc;\n});\n\nexport default crc16xmodem;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,SAAS,MAAM,cAAc;AAEpC,MAAMC,WAAW,GAAGD,SAAS,CAAC,QAAQ,EAAE,UAASE,GAAG,EAAEC,QAAQ,EAAE;EAC9D,IAAI,CAACL,MAAM,CAACM,QAAQ,CAACF,GAAG,CAAC,EAAEA,GAAG,GAAGH,YAAY,CAACG,GAAG,CAAC;EAElD,IAAIG,GAAG,GAAG,OAAOF,QAAQ,KAAK,WAAW,GAAG,CAAC,CAACA,QAAQ,GAAG,GAAG;EAE5D,KAAK,IAAIG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGJ,GAAG,CAACK,MAAM,EAAED,KAAK,EAAE,EAAE;IAC/C,MAAME,IAAI,GAAGN,GAAG,CAACI,KAAK,CAAC;IACvB,IAAIG,IAAI,GAAIJ,GAAG,KAAK,CAAC,GAAI,IAAI;IAE7BI,IAAI,IAAID,IAAI,GAAG,IAAI;IACnBC,IAAI,IAAIA,IAAI,KAAK,CAAC;IAClBJ,GAAG,GAAIA,GAAG,IAAI,CAAC,GAAI,MAAM;IACzBA,GAAG,IAAII,IAAI;IACXA,IAAI,GAAIA,IAAI,IAAI,CAAC,GAAI,MAAM;IAC3BJ,GAAG,IAAII,IAAI;IACXA,IAAI,GAAIA,IAAI,IAAI,CAAC,GAAI,MAAM;IAC3BJ,GAAG,IAAII,IAAI;EACb;EAEA,OAAOJ,GAAG;AACZ,CAAC,CAAC;AAEF,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}