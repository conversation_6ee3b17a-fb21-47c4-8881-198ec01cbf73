{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name closestIndexTo\n * @category Common Helpers\n * @summary Return an index of the closest date from the array comparing to the given date.\n *\n * @description\n * Return an index of the closest date from the array comparing to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - Now, `closestIndexTo` doesn't throw an exception\n *   when the second argument is not an array, and returns Invalid Date instead.\n *\n * @param {Date|Number} dateToCompare - the date to compare with\n * @param {Date[]|Number[]} datesArray - the array to search\n * @returns {Number} an index of the date closest to the given date\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Which date is closer to 6 September 2015?\n * var dateToCompare = new Date(2015, 8, 6)\n * var datesArray = [\n *   new Date(2015, 0, 1),\n *   new Date(2016, 0, 1),\n *   new Date(2017, 0, 1)\n * ]\n * var result = closestIndexTo(dateToCompare, datesArray)\n * //=> 1\n */\n\nexport default function closestIndexTo(dirtyDateToCompare, dirtyDatesArray) {\n  requiredArgs(2, arguments);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  if (isNaN(dateToCompare)) {\n    return NaN;\n  }\n  var timeToCompare = dateToCompare.getTime();\n  var datesArray; // `dirtyDatesArray` is undefined or null\n\n  if (dirtyDatesArray == null) {\n    datesArray = []; // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  } else if (typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray; // If `dirtyDatesArray` is Array-like Object, convert to Array. Otherwise, make it empty Array\n  } else {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  }\n  var result;\n  var minDistance;\n  datesArray.forEach(function (dirtyDate, index) {\n    var currentDate = toDate(dirtyDate);\n    if (isNaN(currentDate)) {\n      result = NaN;\n      minDistance = NaN;\n      return;\n    }\n    var distance = Math.abs(timeToCompare - currentDate.getTime());\n    if (result == null || distance < minDistance) {\n      result = index;\n      minDistance = distance;\n    }\n  });\n  return result;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "closestIndexTo", "dirtyDateToCompare", "dirtyDatesArray", "arguments", "dateToCompare", "isNaN", "NaN", "timeToCompare", "getTime", "datesArray", "for<PERSON>ach", "Array", "prototype", "slice", "call", "result", "minDistance", "dirtyDate", "index", "currentDate", "distance", "Math", "abs"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/closestIndexTo/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name closestIndexTo\n * @category Common Helpers\n * @summary Return an index of the closest date from the array comparing to the given date.\n *\n * @description\n * Return an index of the closest date from the array comparing to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - Now, `closestIndexTo` doesn't throw an exception\n *   when the second argument is not an array, and returns Invalid Date instead.\n *\n * @param {Date|Number} dateToCompare - the date to compare with\n * @param {Date[]|Number[]} datesArray - the array to search\n * @returns {Number} an index of the date closest to the given date\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Which date is closer to 6 September 2015?\n * var dateToCompare = new Date(2015, 8, 6)\n * var datesArray = [\n *   new Date(2015, 0, 1),\n *   new Date(2016, 0, 1),\n *   new Date(2017, 0, 1)\n * ]\n * var result = closestIndexTo(dateToCompare, datesArray)\n * //=> 1\n */\n\nexport default function closestIndexTo(dirtyDateToCompare, dirtyDatesArray) {\n  requiredArgs(2, arguments);\n  var dateToCompare = toDate(dirtyDateToCompare);\n\n  if (isNaN(dateToCompare)) {\n    return NaN;\n  }\n\n  var timeToCompare = dateToCompare.getTime();\n  var datesArray; // `dirtyDatesArray` is undefined or null\n\n  if (dirtyDatesArray == null) {\n    datesArray = []; // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  } else if (typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray; // If `dirtyDatesArray` is Array-like Object, convert to Array. Otherwise, make it empty Array\n  } else {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  }\n\n  var result;\n  var minDistance;\n  datesArray.forEach(function (dirtyDate, index) {\n    var currentDate = toDate(dirtyDate);\n\n    if (isNaN(currentDate)) {\n      result = NaN;\n      minDistance = NaN;\n      return;\n    }\n\n    var distance = Math.abs(timeToCompare - currentDate.getTime());\n\n    if (result == null || distance < minDistance) {\n      result = index;\n      minDistance = distance;\n    }\n  });\n  return result;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,cAAcA,CAACC,kBAAkB,EAAEC,eAAe,EAAE;EAC1EH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,aAAa,GAAGN,MAAM,CAACG,kBAAkB,CAAC;EAE9C,IAAII,KAAK,CAACD,aAAa,CAAC,EAAE;IACxB,OAAOE,GAAG;EACZ;EAEA,IAAIC,aAAa,GAAGH,aAAa,CAACI,OAAO,CAAC,CAAC;EAC3C,IAAIC,UAAU,CAAC,CAAC;;EAEhB,IAAIP,eAAe,IAAI,IAAI,EAAE;IAC3BO,UAAU,GAAG,EAAE,CAAC,CAAC;EACnB,CAAC,MAAM,IAAI,OAAOP,eAAe,CAACQ,OAAO,KAAK,UAAU,EAAE;IACxDD,UAAU,GAAGP,eAAe,CAAC,CAAC;EAChC,CAAC,MAAM;IACLO,UAAU,GAAGE,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACZ,eAAe,CAAC;EAC1D;EAEA,IAAIa,MAAM;EACV,IAAIC,WAAW;EACfP,UAAU,CAACC,OAAO,CAAC,UAAUO,SAAS,EAAEC,KAAK,EAAE;IAC7C,IAAIC,WAAW,GAAGrB,MAAM,CAACmB,SAAS,CAAC;IAEnC,IAAIZ,KAAK,CAACc,WAAW,CAAC,EAAE;MACtBJ,MAAM,GAAGT,GAAG;MACZU,WAAW,GAAGV,GAAG;MACjB;IACF;IAEA,IAAIc,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACf,aAAa,GAAGY,WAAW,CAACX,OAAO,CAAC,CAAC,CAAC;IAE9D,IAAIO,MAAM,IAAI,IAAI,IAAIK,QAAQ,GAAGJ,WAAW,EAAE;MAC5CD,MAAM,GAAGG,KAAK;MACdF,WAAW,GAAGI,QAAQ;IACxB;EACF,CAAC,CAAC;EACF,OAAOL,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}