{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isWeekend\n * @category Weekday Helpers\n * @summary Does the given date fall on a weekend?\n *\n * @description\n * Does the given date fall on a weekend?\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date falls on a weekend\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Does 5 October 2014 fall on a weekend?\n * const result = isWeekend(new Date(2014, 9, 5))\n * //=> true\n */\n\nexport default function isWeekend(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var day = date.getDay();\n  return day === 0 || day === 6;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "isWeekend", "dirtyDate", "arguments", "date", "day", "getDay"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/isWeekend/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isWeekend\n * @category Weekday Helpers\n * @summary Does the given date fall on a weekend?\n *\n * @description\n * Does the given date fall on a weekend?\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date falls on a weekend\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Does 5 October 2014 fall on a weekend?\n * const result = isWeekend(new Date(2014, 9, 5))\n * //=> true\n */\n\nexport default function isWeekend(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var day = date.getDay();\n  return day === 0 || day === 6;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,SAASA,CAACC,SAAS,EAAE;EAC3CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGL,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIG,GAAG,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC;EACvB,OAAOD,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}