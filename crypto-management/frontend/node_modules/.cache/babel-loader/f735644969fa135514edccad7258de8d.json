{"ast": null, "code": "import { Buffer } from 'buffer';\nconst createBuffer = Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow ? Buffer.from :\n// support for Node < 5.10\nval => new Buffer(val);\nexport default createBuffer;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "createBuffer", "from", "alloc", "allocUnsafe", "allocUnsafeSlow", "val"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/crc/create_buffer.js"], "sourcesContent": ["import { Buffer } from 'buffer';\n\nconst createBuffer =\n  Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow\n    ? Buffer.from\n    : // support for Node < 5.10\n      val => new Buffer(val);\n\nexport default createBuffer;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,QAAQ;AAE/B,MAAMC,YAAY,GAChBD,MAAM,CAACE,IAAI,IAAIF,MAAM,CAACG,KAAK,IAAIH,MAAM,CAACI,WAAW,IAAIJ,MAAM,CAACK,eAAe,GACvEL,MAAM,CAACE,IAAI;AACX;AACAI,GAAG,IAAI,IAAIN,MAAM,CAACM,GAAG,CAAC;AAE5B,eAAeL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}