{"ast": null, "code": "import getUTCISOWeekYear from \"../getUTCISOWeekYear/index.js\";\nimport startOfUTCISOWeek from \"../startOfUTCISOWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getUTCISOWeekYear(dirtyDate);\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setUTCFullYear(year, 0, 4);\n  fourthOfJanuary.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCISOWeek(fourthOfJanuary);\n  return date;\n}", "map": {"version": 3, "names": ["getUTCISOWeekYear", "startOfUTCISOWeek", "requiredArgs", "startOfUTCISOWeekYear", "dirtyDate", "arguments", "year", "fourthOfJanuary", "Date", "setUTCFullYear", "setUTCHours", "date"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/_lib/startOfUTCISOWeekYear/index.js"], "sourcesContent": ["import getUTCISOWeekYear from \"../getUTCISOWeekYear/index.js\";\nimport startOfUTCISOWeek from \"../startOfUTCISOWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getUTCISOWeekYear(dirtyDate);\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setUTCFullYear(year, 0, 4);\n  fourthOfJanuary.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCISOWeek(fourthOfJanuary);\n  return date;\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,YAAY,MAAM,0BAA0B,CAAC,CAAC;AACrD;;AAEA,eAAe,SAASC,qBAAqBA,CAACC,SAAS,EAAE;EACvDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,iBAAiB,CAACI,SAAS,CAAC;EACvC,IAAIG,eAAe,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EACjCD,eAAe,CAACE,cAAc,CAACH,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1CC,eAAe,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACvC,IAAIC,IAAI,GAAGV,iBAAiB,CAACM,eAAe,CAAC;EAC7C,OAAOI,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}