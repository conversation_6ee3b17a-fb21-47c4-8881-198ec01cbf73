{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\n\n/**\n * Created by hustcc on 18/7/5.\n * Contract: <EMAIL>\n */\nvar createSensor = function createSensor(element) {\n  var sensor = undefined; // callback\n\n  var listeners = [];\n  /**\n   * trigger listeners\n   */\n\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n  /**\n   * create ResizeObserver sensor\n   * @returns\n   */\n\n  var newSensor = function newSensor() {\n    var s = new ResizeObserver(resizeListener); // listen element\n\n    s.observe(element); // trigger once\n\n    resizeListener();\n    return s;\n  };\n  /**\n   * listen with callback\n   * @param cb\n   */\n\n  var bind = function bind(cb) {\n    if (!sensor) {\n      sensor = newSensor();\n    }\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n  /**\n   * destroy\n   */\n\n  var destroy = function destroy() {\n    sensor.disconnect();\n    listeners = [];\n    sensor = undefined;\n  };\n  /**\n   * cancel bind\n   * @param cb\n   */\n\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    } // no listener, and sensor is exist\n    // then destroy the sensor\n\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\nexports.createSensor = createSensor;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createSensor", "_debounce", "_interopRequireDefault", "require", "obj", "__esModule", "element", "sensor", "undefined", "listeners", "resizeListener", "for<PERSON>ach", "listener", "newSensor", "s", "ResizeObserver", "observe", "bind", "cb", "indexOf", "push", "destroy", "disconnect", "unbind", "idx", "splice", "length"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/size-sensor/lib/sensors/resizeObserver.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\n\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\n/**\n * Created by hustcc on 18/7/5.\n * Contract: <EMAIL>\n */\nvar createSensor = function createSensor(element) {\n  var sensor = undefined; // callback\n\n  var listeners = [];\n  /**\n   * trigger listeners\n   */\n\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n  /**\n   * create ResizeObserver sensor\n   * @returns\n   */\n\n  var newSensor = function newSensor() {\n    var s = new ResizeObserver(resizeListener); // listen element\n\n    s.observe(element); // trigger once\n\n    resizeListener();\n    return s;\n  };\n  /**\n   * listen with callback\n   * @param cb\n   */\n\n\n  var bind = function bind(cb) {\n    if (!sensor) {\n      sensor = newSensor();\n    }\n\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n  /**\n   * destroy\n   */\n\n\n  var destroy = function destroy() {\n    sensor.disconnect();\n    listeners = [];\n    sensor = undefined;\n  };\n  /**\n   * cancel bind\n   * @param cb\n   */\n\n\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    } // no listener, and sensor is exist\n    // then destroy the sensor\n\n\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\n\nexports.createSensor = createSensor;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAE7B,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE9D,SAASD,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;;AAEhG;AACA;AACA;AACA;AACA,IAAIJ,YAAY,GAAG,SAASA,YAAYA,CAACM,OAAO,EAAE;EAChD,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC;;EAExB,IAAIC,SAAS,GAAG,EAAE;EAClB;AACF;AACA;;EAEE,IAAIC,cAAc,GAAG,CAAC,CAAC,EAAET,SAAS,CAAC,SAAS,CAAC,EAAE,YAAY;IACzD;IACAQ,SAAS,CAACE,OAAO,CAAC,UAAUC,QAAQ,EAAE;MACpCA,QAAQ,CAACN,OAAO,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;AACF;AACA;AACA;;EAEE,IAAIO,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIC,CAAC,GAAG,IAAIC,cAAc,CAACL,cAAc,CAAC,CAAC,CAAC;;IAE5CI,CAAC,CAACE,OAAO,CAACV,OAAO,CAAC,CAAC,CAAC;;IAEpBI,cAAc,CAAC,CAAC;IAChB,OAAOI,CAAC;EACV,CAAC;EACD;AACF;AACA;AACA;;EAGE,IAAIG,IAAI,GAAG,SAASA,IAAIA,CAACC,EAAE,EAAE;IAC3B,IAAI,CAACX,MAAM,EAAE;MACXA,MAAM,GAAGM,SAAS,CAAC,CAAC;IACtB;IAEA,IAAIJ,SAAS,CAACU,OAAO,CAACD,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;MAChCT,SAAS,CAACW,IAAI,CAACF,EAAE,CAAC;IACpB;EACF,CAAC;EACD;AACF;AACA;;EAGE,IAAIG,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/Bd,MAAM,CAACe,UAAU,CAAC,CAAC;IACnBb,SAAS,GAAG,EAAE;IACdF,MAAM,GAAGC,SAAS;EACpB,CAAC;EACD;AACF;AACA;AACA;;EAGE,IAAIe,MAAM,GAAG,SAASA,MAAMA,CAACL,EAAE,EAAE;IAC/B,IAAIM,GAAG,GAAGf,SAAS,CAACU,OAAO,CAACD,EAAE,CAAC;IAE/B,IAAIM,GAAG,KAAK,CAAC,CAAC,EAAE;MACdf,SAAS,CAACgB,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF;;IAGA,IAAIf,SAAS,CAACiB,MAAM,KAAK,CAAC,IAAInB,MAAM,EAAE;MACpCc,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,OAAO;IACLf,OAAO,EAAEA,OAAO;IAChBW,IAAI,EAAEA,IAAI;IACVI,OAAO,EAAEA,OAAO;IAChBE,MAAM,EAAEA;EACV,CAAC;AACH,CAAC;AAEDzB,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}