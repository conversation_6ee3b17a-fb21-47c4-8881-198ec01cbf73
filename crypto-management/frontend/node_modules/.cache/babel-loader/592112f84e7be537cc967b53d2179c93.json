{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js'; // id may be function name of Object, add a prefix to avoid this problem.\n\nfunction generateNodeKey(id) {\n  return '_EC_' + id;\n}\nvar Graph = /** @class */\nfunction () {\n  function Graph(directed) {\n    this.type = 'graph';\n    this.nodes = [];\n    this.edges = [];\n    this._nodesMap = {};\n    /**\n     * @type {Object.<string, module:echarts/data/Graph.Edge>}\n     * @private\n     */\n\n    this._edgesMap = {};\n    this._directed = directed || false;\n  }\n  /**\n   * If is directed graph\n   */\n\n  Graph.prototype.isDirected = function () {\n    return this._directed;\n  };\n  ;\n  /**\n   * Add a new node\n   */\n\n  Graph.prototype.addNode = function (id, dataIndex) {\n    id = id == null ? '' + dataIndex : '' + id;\n    var nodesMap = this._nodesMap;\n    if (nodesMap[generateNodeKey(id)]) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Graph nodes have duplicate name or id');\n      }\n      return;\n    }\n    var node = new GraphNode(id, dataIndex);\n    node.hostGraph = this;\n    this.nodes.push(node);\n    nodesMap[generateNodeKey(id)] = node;\n    return node;\n  };\n  ;\n  /**\n   * Get node by data index\n   */\n\n  Graph.prototype.getNodeByIndex = function (dataIndex) {\n    var rawIdx = this.data.getRawIndex(dataIndex);\n    return this.nodes[rawIdx];\n  };\n  ;\n  /**\n   * Get node by id\n   */\n\n  Graph.prototype.getNodeById = function (id) {\n    return this._nodesMap[generateNodeKey(id)];\n  };\n  ;\n  /**\n   * Add a new edge\n   */\n\n  Graph.prototype.addEdge = function (n1, n2, dataIndex) {\n    var nodesMap = this._nodesMap;\n    var edgesMap = this._edgesMap; // PNEDING\n\n    if (zrUtil.isNumber(n1)) {\n      n1 = this.nodes[n1];\n    }\n    if (zrUtil.isNumber(n2)) {\n      n2 = this.nodes[n2];\n    }\n    if (!(n1 instanceof GraphNode)) {\n      n1 = nodesMap[generateNodeKey(n1)];\n    }\n    if (!(n2 instanceof GraphNode)) {\n      n2 = nodesMap[generateNodeKey(n2)];\n    }\n    if (!n1 || !n2) {\n      return;\n    }\n    var key = n1.id + '-' + n2.id;\n    var edge = new GraphEdge(n1, n2, dataIndex);\n    edge.hostGraph = this;\n    if (this._directed) {\n      n1.outEdges.push(edge);\n      n2.inEdges.push(edge);\n    }\n    n1.edges.push(edge);\n    if (n1 !== n2) {\n      n2.edges.push(edge);\n    }\n    this.edges.push(edge);\n    edgesMap[key] = edge;\n    return edge;\n  };\n  ;\n  /**\n   * Get edge by data index\n   */\n\n  Graph.prototype.getEdgeByIndex = function (dataIndex) {\n    var rawIdx = this.edgeData.getRawIndex(dataIndex);\n    return this.edges[rawIdx];\n  };\n  ;\n  /**\n   * Get edge by two linked nodes\n   */\n\n  Graph.prototype.getEdge = function (n1, n2) {\n    if (n1 instanceof GraphNode) {\n      n1 = n1.id;\n    }\n    if (n2 instanceof GraphNode) {\n      n2 = n2.id;\n    }\n    var edgesMap = this._edgesMap;\n    if (this._directed) {\n      return edgesMap[n1 + '-' + n2];\n    } else {\n      return edgesMap[n1 + '-' + n2] || edgesMap[n2 + '-' + n1];\n    }\n  };\n  ;\n  /**\n   * Iterate all nodes\n   */\n\n  Graph.prototype.eachNode = function (cb, context) {\n    var nodes = this.nodes;\n    var len = nodes.length;\n    for (var i = 0; i < len; i++) {\n      if (nodes[i].dataIndex >= 0) {\n        cb.call(context, nodes[i], i);\n      }\n    }\n  };\n  ;\n  /**\n   * Iterate all edges\n   */\n\n  Graph.prototype.eachEdge = function (cb, context) {\n    var edges = this.edges;\n    var len = edges.length;\n    for (var i = 0; i < len; i++) {\n      if (edges[i].dataIndex >= 0 && edges[i].node1.dataIndex >= 0 && edges[i].node2.dataIndex >= 0) {\n        cb.call(context, edges[i], i);\n      }\n    }\n  };\n  ;\n  /**\n   * Breadth first traverse\n   * Return true to stop traversing\n   */\n\n  Graph.prototype.breadthFirstTraverse = function (cb, startNode, direction, context) {\n    if (!(startNode instanceof GraphNode)) {\n      startNode = this._nodesMap[generateNodeKey(startNode)];\n    }\n    if (!startNode) {\n      return;\n    }\n    var edgeType = direction === 'out' ? 'outEdges' : direction === 'in' ? 'inEdges' : 'edges';\n    for (var i = 0; i < this.nodes.length; i++) {\n      this.nodes[i].__visited = false;\n    }\n    if (cb.call(context, startNode, null)) {\n      return;\n    }\n    var queue = [startNode];\n    while (queue.length) {\n      var currentNode = queue.shift();\n      var edges = currentNode[edgeType];\n      for (var i = 0; i < edges.length; i++) {\n        var e = edges[i];\n        var otherNode = e.node1 === currentNode ? e.node2 : e.node1;\n        if (!otherNode.__visited) {\n          if (cb.call(context, otherNode, currentNode)) {\n            // Stop traversing\n            return;\n          }\n          queue.push(otherNode);\n          otherNode.__visited = true;\n        }\n      }\n    }\n  };\n  ; // TODO\n  // depthFirstTraverse(\n  //     cb, startNode, direction, context\n  // ) {\n  // };\n  // Filter update\n\n  Graph.prototype.update = function () {\n    var data = this.data;\n    var edgeData = this.edgeData;\n    var nodes = this.nodes;\n    var edges = this.edges;\n    for (var i = 0, len = nodes.length; i < len; i++) {\n      nodes[i].dataIndex = -1;\n    }\n    for (var i = 0, len = data.count(); i < len; i++) {\n      nodes[data.getRawIndex(i)].dataIndex = i;\n    }\n    edgeData.filterSelf(function (idx) {\n      var edge = edges[edgeData.getRawIndex(idx)];\n      return edge.node1.dataIndex >= 0 && edge.node2.dataIndex >= 0;\n    }); // Update edge\n\n    for (var i = 0, len = edges.length; i < len; i++) {\n      edges[i].dataIndex = -1;\n    }\n    for (var i = 0, len = edgeData.count(); i < len; i++) {\n      edges[edgeData.getRawIndex(i)].dataIndex = i;\n    }\n  };\n  ;\n  /**\n   * @return {module:echarts/data/Graph}\n   */\n\n  Graph.prototype.clone = function () {\n    var graph = new Graph(this._directed);\n    var nodes = this.nodes;\n    var edges = this.edges;\n    for (var i = 0; i < nodes.length; i++) {\n      graph.addNode(nodes[i].id, nodes[i].dataIndex);\n    }\n    for (var i = 0; i < edges.length; i++) {\n      var e = edges[i];\n      graph.addEdge(e.node1.id, e.node2.id, e.dataIndex);\n    }\n    return graph;\n  };\n  ;\n  return Graph;\n}();\nvar GraphNode = /** @class */\nfunction () {\n  function GraphNode(id, dataIndex) {\n    this.inEdges = [];\n    this.outEdges = [];\n    this.edges = [];\n    this.dataIndex = -1;\n    this.id = id == null ? '' : id;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  }\n  /**\n   * @return {number}\n   */\n\n  GraphNode.prototype.degree = function () {\n    return this.edges.length;\n  };\n  /**\n   * @return {number}\n   */\n\n  GraphNode.prototype.inDegree = function () {\n    return this.inEdges.length;\n  };\n  /**\n  * @return {number}\n  */\n\n  GraphNode.prototype.outDegree = function () {\n    return this.outEdges.length;\n  };\n  GraphNode.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var graph = this.hostGraph;\n    var itemModel = graph.data.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  GraphNode.prototype.getAdjacentDataIndices = function () {\n    var dataIndices = {\n      edge: [],\n      node: []\n    };\n    for (var i = 0; i < this.edges.length; i++) {\n      var adjacentEdge = this.edges[i];\n      if (adjacentEdge.dataIndex < 0) {\n        continue;\n      }\n      dataIndices.edge.push(adjacentEdge.dataIndex);\n      dataIndices.node.push(adjacentEdge.node1.dataIndex, adjacentEdge.node2.dataIndex);\n    }\n    return dataIndices;\n  };\n  return GraphNode;\n}();\nvar GraphEdge = /** @class */\nfunction () {\n  function GraphEdge(n1, n2, dataIndex) {\n    this.dataIndex = -1;\n    this.node1 = n1;\n    this.node2 = n2;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  } // eslint-disable-next-line @typescript-eslint/no-unused-vars\n\n  GraphEdge.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n    var graph = this.hostGraph;\n    var itemModel = graph.edgeData.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n  GraphEdge.prototype.getAdjacentDataIndices = function () {\n    return {\n      edge: [this.dataIndex],\n      node: [this.node1.dataIndex, this.node2.dataIndex]\n    };\n  };\n  return GraphEdge;\n}();\nfunction createGraphDataProxyMixin(hostName, dataName) {\n  return {\n    /**\n     * @param Default 'value'. can be 'a', 'b', 'c', 'd', 'e'.\n     */\n    getValue: function (dimension) {\n      var data = this[hostName][dataName];\n      return data.getStore().get(data.getDimensionIndex(dimension || 'value'), this.dataIndex);\n    },\n    // TODO: TYPE stricter type.\n    setVisual: function (key, value) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemVisual(this.dataIndex, key, value);\n    },\n    getVisual: function (key) {\n      return this[hostName][dataName].getItemVisual(this.dataIndex, key);\n    },\n    setLayout: function (layout, merge) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemLayout(this.dataIndex, layout, merge);\n    },\n    getLayout: function () {\n      return this[hostName][dataName].getItemLayout(this.dataIndex);\n    },\n    getGraphicEl: function () {\n      return this[hostName][dataName].getItemGraphicEl(this.dataIndex);\n    },\n    getRawIndex: function () {\n      return this[hostName][dataName].getRawIndex(this.dataIndex);\n    }\n  };\n}\n;\n;\n;\nzrUtil.mixin(GraphNode, createGraphDataProxyMixin('hostGraph', 'data'));\nzrUtil.mixin(GraphEdge, createGraphDataProxyMixin('hostGraph', 'edgeData'));\nexport default Graph;\nexport { GraphNode, GraphEdge };", "map": {"version": 3, "names": ["zrUtil", "generateNodeKey", "id", "Graph", "directed", "type", "nodes", "edges", "_nodesMap", "_edgesMap", "_directed", "prototype", "isDirected", "addNode", "dataIndex", "nodesMap", "process", "env", "NODE_ENV", "console", "error", "node", "GraphNode", "hostGraph", "push", "getNodeByIndex", "rawIdx", "data", "getRawIndex", "getNodeById", "addEdge", "n1", "n2", "edgesMap", "isNumber", "key", "edge", "GraphEdge", "outEdges", "inEdges", "getEdgeByIndex", "edgeData", "getEdge", "eachNode", "cb", "context", "len", "length", "i", "call", "eachEdge", "node1", "node2", "breadthFirstTraverse", "startNode", "direction", "edgeType", "__visited", "queue", "currentNode", "shift", "e", "otherNode", "update", "count", "filterSelf", "idx", "clone", "graph", "degree", "inDegree", "outDegree", "getModel", "path", "itemModel", "getItemModel", "getAdjacentDataIndices", "dataIndices", "adjacentEdge", "createGraphDataProxyMixin", "hostName", "dataName", "getValue", "dimension", "getStore", "get", "getDimensionIndex", "setVisual", "value", "setItemVisual", "getVisual", "getItemVisual", "setLayout", "layout", "merge", "setItemLayout", "getLayout", "getItemLayout", "getGraphicEl", "getItemGraphicEl", "mixin"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/echarts/lib/data/Graph.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js'; // id may be function name of Object, add a prefix to avoid this problem.\n\nfunction generateNodeKey(id) {\n  return '_EC_' + id;\n}\n\nvar Graph =\n/** @class */\nfunction () {\n  function Graph(directed) {\n    this.type = 'graph';\n    this.nodes = [];\n    this.edges = [];\n    this._nodesMap = {};\n    /**\n     * @type {Object.<string, module:echarts/data/Graph.Edge>}\n     * @private\n     */\n\n    this._edgesMap = {};\n    this._directed = directed || false;\n  }\n  /**\n   * If is directed graph\n   */\n\n\n  Graph.prototype.isDirected = function () {\n    return this._directed;\n  };\n\n  ;\n  /**\n   * Add a new node\n   */\n\n  Graph.prototype.addNode = function (id, dataIndex) {\n    id = id == null ? '' + dataIndex : '' + id;\n    var nodesMap = this._nodesMap;\n\n    if (nodesMap[generateNodeKey(id)]) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('Graph nodes have duplicate name or id');\n      }\n\n      return;\n    }\n\n    var node = new GraphNode(id, dataIndex);\n    node.hostGraph = this;\n    this.nodes.push(node);\n    nodesMap[generateNodeKey(id)] = node;\n    return node;\n  };\n\n  ;\n  /**\n   * Get node by data index\n   */\n\n  Graph.prototype.getNodeByIndex = function (dataIndex) {\n    var rawIdx = this.data.getRawIndex(dataIndex);\n    return this.nodes[rawIdx];\n  };\n\n  ;\n  /**\n   * Get node by id\n   */\n\n  Graph.prototype.getNodeById = function (id) {\n    return this._nodesMap[generateNodeKey(id)];\n  };\n\n  ;\n  /**\n   * Add a new edge\n   */\n\n  Graph.prototype.addEdge = function (n1, n2, dataIndex) {\n    var nodesMap = this._nodesMap;\n    var edgesMap = this._edgesMap; // PNEDING\n\n    if (zrUtil.isNumber(n1)) {\n      n1 = this.nodes[n1];\n    }\n\n    if (zrUtil.isNumber(n2)) {\n      n2 = this.nodes[n2];\n    }\n\n    if (!(n1 instanceof GraphNode)) {\n      n1 = nodesMap[generateNodeKey(n1)];\n    }\n\n    if (!(n2 instanceof GraphNode)) {\n      n2 = nodesMap[generateNodeKey(n2)];\n    }\n\n    if (!n1 || !n2) {\n      return;\n    }\n\n    var key = n1.id + '-' + n2.id;\n    var edge = new GraphEdge(n1, n2, dataIndex);\n    edge.hostGraph = this;\n\n    if (this._directed) {\n      n1.outEdges.push(edge);\n      n2.inEdges.push(edge);\n    }\n\n    n1.edges.push(edge);\n\n    if (n1 !== n2) {\n      n2.edges.push(edge);\n    }\n\n    this.edges.push(edge);\n    edgesMap[key] = edge;\n    return edge;\n  };\n\n  ;\n  /**\n   * Get edge by data index\n   */\n\n  Graph.prototype.getEdgeByIndex = function (dataIndex) {\n    var rawIdx = this.edgeData.getRawIndex(dataIndex);\n    return this.edges[rawIdx];\n  };\n\n  ;\n  /**\n   * Get edge by two linked nodes\n   */\n\n  Graph.prototype.getEdge = function (n1, n2) {\n    if (n1 instanceof GraphNode) {\n      n1 = n1.id;\n    }\n\n    if (n2 instanceof GraphNode) {\n      n2 = n2.id;\n    }\n\n    var edgesMap = this._edgesMap;\n\n    if (this._directed) {\n      return edgesMap[n1 + '-' + n2];\n    } else {\n      return edgesMap[n1 + '-' + n2] || edgesMap[n2 + '-' + n1];\n    }\n  };\n\n  ;\n  /**\n   * Iterate all nodes\n   */\n\n  Graph.prototype.eachNode = function (cb, context) {\n    var nodes = this.nodes;\n    var len = nodes.length;\n\n    for (var i = 0; i < len; i++) {\n      if (nodes[i].dataIndex >= 0) {\n        cb.call(context, nodes[i], i);\n      }\n    }\n  };\n\n  ;\n  /**\n   * Iterate all edges\n   */\n\n  Graph.prototype.eachEdge = function (cb, context) {\n    var edges = this.edges;\n    var len = edges.length;\n\n    for (var i = 0; i < len; i++) {\n      if (edges[i].dataIndex >= 0 && edges[i].node1.dataIndex >= 0 && edges[i].node2.dataIndex >= 0) {\n        cb.call(context, edges[i], i);\n      }\n    }\n  };\n\n  ;\n  /**\n   * Breadth first traverse\n   * Return true to stop traversing\n   */\n\n  Graph.prototype.breadthFirstTraverse = function (cb, startNode, direction, context) {\n    if (!(startNode instanceof GraphNode)) {\n      startNode = this._nodesMap[generateNodeKey(startNode)];\n    }\n\n    if (!startNode) {\n      return;\n    }\n\n    var edgeType = direction === 'out' ? 'outEdges' : direction === 'in' ? 'inEdges' : 'edges';\n\n    for (var i = 0; i < this.nodes.length; i++) {\n      this.nodes[i].__visited = false;\n    }\n\n    if (cb.call(context, startNode, null)) {\n      return;\n    }\n\n    var queue = [startNode];\n\n    while (queue.length) {\n      var currentNode = queue.shift();\n      var edges = currentNode[edgeType];\n\n      for (var i = 0; i < edges.length; i++) {\n        var e = edges[i];\n        var otherNode = e.node1 === currentNode ? e.node2 : e.node1;\n\n        if (!otherNode.__visited) {\n          if (cb.call(context, otherNode, currentNode)) {\n            // Stop traversing\n            return;\n          }\n\n          queue.push(otherNode);\n          otherNode.__visited = true;\n        }\n      }\n    }\n  };\n\n  ; // TODO\n  // depthFirstTraverse(\n  //     cb, startNode, direction, context\n  // ) {\n  // };\n  // Filter update\n\n  Graph.prototype.update = function () {\n    var data = this.data;\n    var edgeData = this.edgeData;\n    var nodes = this.nodes;\n    var edges = this.edges;\n\n    for (var i = 0, len = nodes.length; i < len; i++) {\n      nodes[i].dataIndex = -1;\n    }\n\n    for (var i = 0, len = data.count(); i < len; i++) {\n      nodes[data.getRawIndex(i)].dataIndex = i;\n    }\n\n    edgeData.filterSelf(function (idx) {\n      var edge = edges[edgeData.getRawIndex(idx)];\n      return edge.node1.dataIndex >= 0 && edge.node2.dataIndex >= 0;\n    }); // Update edge\n\n    for (var i = 0, len = edges.length; i < len; i++) {\n      edges[i].dataIndex = -1;\n    }\n\n    for (var i = 0, len = edgeData.count(); i < len; i++) {\n      edges[edgeData.getRawIndex(i)].dataIndex = i;\n    }\n  };\n\n  ;\n  /**\n   * @return {module:echarts/data/Graph}\n   */\n\n  Graph.prototype.clone = function () {\n    var graph = new Graph(this._directed);\n    var nodes = this.nodes;\n    var edges = this.edges;\n\n    for (var i = 0; i < nodes.length; i++) {\n      graph.addNode(nodes[i].id, nodes[i].dataIndex);\n    }\n\n    for (var i = 0; i < edges.length; i++) {\n      var e = edges[i];\n      graph.addEdge(e.node1.id, e.node2.id, e.dataIndex);\n    }\n\n    return graph;\n  };\n\n  ;\n  return Graph;\n}();\n\nvar GraphNode =\n/** @class */\nfunction () {\n  function GraphNode(id, dataIndex) {\n    this.inEdges = [];\n    this.outEdges = [];\n    this.edges = [];\n    this.dataIndex = -1;\n    this.id = id == null ? '' : id;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  }\n  /**\n   * @return {number}\n   */\n\n\n  GraphNode.prototype.degree = function () {\n    return this.edges.length;\n  };\n  /**\n   * @return {number}\n   */\n\n\n  GraphNode.prototype.inDegree = function () {\n    return this.inEdges.length;\n  };\n  /**\n  * @return {number}\n  */\n\n\n  GraphNode.prototype.outDegree = function () {\n    return this.outEdges.length;\n  };\n\n  GraphNode.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n\n    var graph = this.hostGraph;\n    var itemModel = graph.data.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n\n  GraphNode.prototype.getAdjacentDataIndices = function () {\n    var dataIndices = {\n      edge: [],\n      node: []\n    };\n\n    for (var i = 0; i < this.edges.length; i++) {\n      var adjacentEdge = this.edges[i];\n\n      if (adjacentEdge.dataIndex < 0) {\n        continue;\n      }\n\n      dataIndices.edge.push(adjacentEdge.dataIndex);\n      dataIndices.node.push(adjacentEdge.node1.dataIndex, adjacentEdge.node2.dataIndex);\n    }\n\n    return dataIndices;\n  };\n\n  return GraphNode;\n}();\n\nvar GraphEdge =\n/** @class */\nfunction () {\n  function GraphEdge(n1, n2, dataIndex) {\n    this.dataIndex = -1;\n    this.node1 = n1;\n    this.node2 = n2;\n    this.dataIndex = dataIndex == null ? -1 : dataIndex;\n  } // eslint-disable-next-line @typescript-eslint/no-unused-vars\n\n\n  GraphEdge.prototype.getModel = function (path) {\n    if (this.dataIndex < 0) {\n      return;\n    }\n\n    var graph = this.hostGraph;\n    var itemModel = graph.edgeData.getItemModel(this.dataIndex);\n    return itemModel.getModel(path);\n  };\n\n  GraphEdge.prototype.getAdjacentDataIndices = function () {\n    return {\n      edge: [this.dataIndex],\n      node: [this.node1.dataIndex, this.node2.dataIndex]\n    };\n  };\n\n  return GraphEdge;\n}();\n\nfunction createGraphDataProxyMixin(hostName, dataName) {\n  return {\n    /**\n     * @param Default 'value'. can be 'a', 'b', 'c', 'd', 'e'.\n     */\n    getValue: function (dimension) {\n      var data = this[hostName][dataName];\n      return data.getStore().get(data.getDimensionIndex(dimension || 'value'), this.dataIndex);\n    },\n    // TODO: TYPE stricter type.\n    setVisual: function (key, value) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemVisual(this.dataIndex, key, value);\n    },\n    getVisual: function (key) {\n      return this[hostName][dataName].getItemVisual(this.dataIndex, key);\n    },\n    setLayout: function (layout, merge) {\n      this.dataIndex >= 0 && this[hostName][dataName].setItemLayout(this.dataIndex, layout, merge);\n    },\n    getLayout: function () {\n      return this[hostName][dataName].getItemLayout(this.dataIndex);\n    },\n    getGraphicEl: function () {\n      return this[hostName][dataName].getItemGraphicEl(this.dataIndex);\n    },\n    getRawIndex: function () {\n      return this[hostName][dataName].getRawIndex(this.dataIndex);\n    }\n  };\n}\n\n;\n;\n;\nzrUtil.mixin(GraphNode, createGraphDataProxyMixin('hostGraph', 'data'));\nzrUtil.mixin(GraphEdge, createGraphDataProxyMixin('hostGraph', 'edgeData'));\nexport default Graph;\nexport { GraphNode, GraphEdge };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B,CAAC,CAAC;;AAEpD,SAASC,eAAeA,CAACC,EAAE,EAAE;EAC3B,OAAO,MAAM,GAAGA,EAAE;AACpB;AAEA,IAAIC,KAAK,GACT;AACA,YAAY;EACV,SAASA,KAAKA,CAACC,QAAQ,EAAE;IACvB,IAAI,CAACC,IAAI,GAAG,OAAO;IACnB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB;AACJ;AACA;AACA;;IAEI,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,SAAS,GAAGN,QAAQ,IAAI,KAAK;EACpC;EACA;AACF;AACA;;EAGED,KAAK,CAACQ,SAAS,CAACC,UAAU,GAAG,YAAY;IACvC,OAAO,IAAI,CAACF,SAAS;EACvB,CAAC;EAED;EACA;AACF;AACA;;EAEEP,KAAK,CAACQ,SAAS,CAACE,OAAO,GAAG,UAAUX,EAAE,EAAEY,SAAS,EAAE;IACjDZ,EAAE,GAAGA,EAAE,IAAI,IAAI,GAAG,EAAE,GAAGY,SAAS,GAAG,EAAE,GAAGZ,EAAE;IAC1C,IAAIa,QAAQ,GAAG,IAAI,CAACP,SAAS;IAE7B,IAAIO,QAAQ,CAACd,eAAe,CAACC,EAAE,CAAC,CAAC,EAAE;MACjC,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCC,OAAO,CAACC,KAAK,CAAC,uCAAuC,CAAC;MACxD;MAEA;IACF;IAEA,IAAIC,IAAI,GAAG,IAAIC,SAAS,CAACpB,EAAE,EAAEY,SAAS,CAAC;IACvCO,IAAI,CAACE,SAAS,GAAG,IAAI;IACrB,IAAI,CAACjB,KAAK,CAACkB,IAAI,CAACH,IAAI,CAAC;IACrBN,QAAQ,CAACd,eAAe,CAACC,EAAE,CAAC,CAAC,GAAGmB,IAAI;IACpC,OAAOA,IAAI;EACb,CAAC;EAED;EACA;AACF;AACA;;EAEElB,KAAK,CAACQ,SAAS,CAACc,cAAc,GAAG,UAAUX,SAAS,EAAE;IACpD,IAAIY,MAAM,GAAG,IAAI,CAACC,IAAI,CAACC,WAAW,CAACd,SAAS,CAAC;IAC7C,OAAO,IAAI,CAACR,KAAK,CAACoB,MAAM,CAAC;EAC3B,CAAC;EAED;EACA;AACF;AACA;;EAEEvB,KAAK,CAACQ,SAAS,CAACkB,WAAW,GAAG,UAAU3B,EAAE,EAAE;IAC1C,OAAO,IAAI,CAACM,SAAS,CAACP,eAAe,CAACC,EAAE,CAAC,CAAC;EAC5C,CAAC;EAED;EACA;AACF;AACA;;EAEEC,KAAK,CAACQ,SAAS,CAACmB,OAAO,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAElB,SAAS,EAAE;IACrD,IAAIC,QAAQ,GAAG,IAAI,CAACP,SAAS;IAC7B,IAAIyB,QAAQ,GAAG,IAAI,CAACxB,SAAS,CAAC,CAAC;;IAE/B,IAAIT,MAAM,CAACkC,QAAQ,CAACH,EAAE,CAAC,EAAE;MACvBA,EAAE,GAAG,IAAI,CAACzB,KAAK,CAACyB,EAAE,CAAC;IACrB;IAEA,IAAI/B,MAAM,CAACkC,QAAQ,CAACF,EAAE,CAAC,EAAE;MACvBA,EAAE,GAAG,IAAI,CAAC1B,KAAK,CAAC0B,EAAE,CAAC;IACrB;IAEA,IAAI,EAAED,EAAE,YAAYT,SAAS,CAAC,EAAE;MAC9BS,EAAE,GAAGhB,QAAQ,CAACd,eAAe,CAAC8B,EAAE,CAAC,CAAC;IACpC;IAEA,IAAI,EAAEC,EAAE,YAAYV,SAAS,CAAC,EAAE;MAC9BU,EAAE,GAAGjB,QAAQ,CAACd,eAAe,CAAC+B,EAAE,CAAC,CAAC;IACpC;IAEA,IAAI,CAACD,EAAE,IAAI,CAACC,EAAE,EAAE;MACd;IACF;IAEA,IAAIG,GAAG,GAAGJ,EAAE,CAAC7B,EAAE,GAAG,GAAG,GAAG8B,EAAE,CAAC9B,EAAE;IAC7B,IAAIkC,IAAI,GAAG,IAAIC,SAAS,CAACN,EAAE,EAAEC,EAAE,EAAElB,SAAS,CAAC;IAC3CsB,IAAI,CAACb,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACb,SAAS,EAAE;MAClBqB,EAAE,CAACO,QAAQ,CAACd,IAAI,CAACY,IAAI,CAAC;MACtBJ,EAAE,CAACO,OAAO,CAACf,IAAI,CAACY,IAAI,CAAC;IACvB;IAEAL,EAAE,CAACxB,KAAK,CAACiB,IAAI,CAACY,IAAI,CAAC;IAEnB,IAAIL,EAAE,KAAKC,EAAE,EAAE;MACbA,EAAE,CAACzB,KAAK,CAACiB,IAAI,CAACY,IAAI,CAAC;IACrB;IAEA,IAAI,CAAC7B,KAAK,CAACiB,IAAI,CAACY,IAAI,CAAC;IACrBH,QAAQ,CAACE,GAAG,CAAC,GAAGC,IAAI;IACpB,OAAOA,IAAI;EACb,CAAC;EAED;EACA;AACF;AACA;;EAEEjC,KAAK,CAACQ,SAAS,CAAC6B,cAAc,GAAG,UAAU1B,SAAS,EAAE;IACpD,IAAIY,MAAM,GAAG,IAAI,CAACe,QAAQ,CAACb,WAAW,CAACd,SAAS,CAAC;IACjD,OAAO,IAAI,CAACP,KAAK,CAACmB,MAAM,CAAC;EAC3B,CAAC;EAED;EACA;AACF;AACA;;EAEEvB,KAAK,CAACQ,SAAS,CAAC+B,OAAO,GAAG,UAAUX,EAAE,EAAEC,EAAE,EAAE;IAC1C,IAAID,EAAE,YAAYT,SAAS,EAAE;MAC3BS,EAAE,GAAGA,EAAE,CAAC7B,EAAE;IACZ;IAEA,IAAI8B,EAAE,YAAYV,SAAS,EAAE;MAC3BU,EAAE,GAAGA,EAAE,CAAC9B,EAAE;IACZ;IAEA,IAAI+B,QAAQ,GAAG,IAAI,CAACxB,SAAS;IAE7B,IAAI,IAAI,CAACC,SAAS,EAAE;MAClB,OAAOuB,QAAQ,CAACF,EAAE,GAAG,GAAG,GAAGC,EAAE,CAAC;IAChC,CAAC,MAAM;MACL,OAAOC,QAAQ,CAACF,EAAE,GAAG,GAAG,GAAGC,EAAE,CAAC,IAAIC,QAAQ,CAACD,EAAE,GAAG,GAAG,GAAGD,EAAE,CAAC;IAC3D;EACF,CAAC;EAED;EACA;AACF;AACA;;EAEE5B,KAAK,CAACQ,SAAS,CAACgC,QAAQ,GAAG,UAAUC,EAAE,EAAEC,OAAO,EAAE;IAChD,IAAIvC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIwC,GAAG,GAAGxC,KAAK,CAACyC,MAAM;IAEtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC5B,IAAI1C,KAAK,CAAC0C,CAAC,CAAC,CAAClC,SAAS,IAAI,CAAC,EAAE;QAC3B8B,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEvC,KAAK,CAAC0C,CAAC,CAAC,EAAEA,CAAC,CAAC;MAC/B;IACF;EACF,CAAC;EAED;EACA;AACF;AACA;;EAEE7C,KAAK,CAACQ,SAAS,CAACuC,QAAQ,GAAG,UAAUN,EAAE,EAAEC,OAAO,EAAE;IAChD,IAAItC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIuC,GAAG,GAAGvC,KAAK,CAACwC,MAAM;IAEtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC5B,IAAIzC,KAAK,CAACyC,CAAC,CAAC,CAAClC,SAAS,IAAI,CAAC,IAAIP,KAAK,CAACyC,CAAC,CAAC,CAACG,KAAK,CAACrC,SAAS,IAAI,CAAC,IAAIP,KAAK,CAACyC,CAAC,CAAC,CAACI,KAAK,CAACtC,SAAS,IAAI,CAAC,EAAE;QAC7F8B,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEtC,KAAK,CAACyC,CAAC,CAAC,EAAEA,CAAC,CAAC;MAC/B;IACF;EACF,CAAC;EAED;EACA;AACF;AACA;AACA;;EAEE7C,KAAK,CAACQ,SAAS,CAAC0C,oBAAoB,GAAG,UAAUT,EAAE,EAAEU,SAAS,EAAEC,SAAS,EAAEV,OAAO,EAAE;IAClF,IAAI,EAAES,SAAS,YAAYhC,SAAS,CAAC,EAAE;MACrCgC,SAAS,GAAG,IAAI,CAAC9C,SAAS,CAACP,eAAe,CAACqD,SAAS,CAAC,CAAC;IACxD;IAEA,IAAI,CAACA,SAAS,EAAE;MACd;IACF;IAEA,IAAIE,QAAQ,GAAGD,SAAS,KAAK,KAAK,GAAG,UAAU,GAAGA,SAAS,KAAK,IAAI,GAAG,SAAS,GAAG,OAAO;IAE1F,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC1C,KAAK,CAACyC,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC1C,IAAI,CAAC1C,KAAK,CAAC0C,CAAC,CAAC,CAACS,SAAS,GAAG,KAAK;IACjC;IAEA,IAAIb,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAES,SAAS,EAAE,IAAI,CAAC,EAAE;MACrC;IACF;IAEA,IAAII,KAAK,GAAG,CAACJ,SAAS,CAAC;IAEvB,OAAOI,KAAK,CAACX,MAAM,EAAE;MACnB,IAAIY,WAAW,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC;MAC/B,IAAIrD,KAAK,GAAGoD,WAAW,CAACH,QAAQ,CAAC;MAEjC,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,KAAK,CAACwC,MAAM,EAAEC,CAAC,EAAE,EAAE;QACrC,IAAIa,CAAC,GAAGtD,KAAK,CAACyC,CAAC,CAAC;QAChB,IAAIc,SAAS,GAAGD,CAAC,CAACV,KAAK,KAAKQ,WAAW,GAAGE,CAAC,CAACT,KAAK,GAAGS,CAAC,CAACV,KAAK;QAE3D,IAAI,CAACW,SAAS,CAACL,SAAS,EAAE;UACxB,IAAIb,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEiB,SAAS,EAAEH,WAAW,CAAC,EAAE;YAC5C;YACA;UACF;UAEAD,KAAK,CAAClC,IAAI,CAACsC,SAAS,CAAC;UACrBA,SAAS,CAACL,SAAS,GAAG,IAAI;QAC5B;MACF;IACF;EACF,CAAC;EAED,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;;EAEAtD,KAAK,CAACQ,SAAS,CAACoD,MAAM,GAAG,YAAY;IACnC,IAAIpC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIc,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAInC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IAEtB,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGxC,KAAK,CAACyC,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAChD1C,KAAK,CAAC0C,CAAC,CAAC,CAAClC,SAAS,GAAG,CAAC,CAAC;IACzB;IAEA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGnB,IAAI,CAACqC,KAAK,CAAC,CAAC,EAAEhB,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAChD1C,KAAK,CAACqB,IAAI,CAACC,WAAW,CAACoB,CAAC,CAAC,CAAC,CAAClC,SAAS,GAAGkC,CAAC;IAC1C;IAEAP,QAAQ,CAACwB,UAAU,CAAC,UAAUC,GAAG,EAAE;MACjC,IAAI9B,IAAI,GAAG7B,KAAK,CAACkC,QAAQ,CAACb,WAAW,CAACsC,GAAG,CAAC,CAAC;MAC3C,OAAO9B,IAAI,CAACe,KAAK,CAACrC,SAAS,IAAI,CAAC,IAAIsB,IAAI,CAACgB,KAAK,CAACtC,SAAS,IAAI,CAAC;IAC/D,CAAC,CAAC,CAAC,CAAC;;IAEJ,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGvC,KAAK,CAACwC,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAChDzC,KAAK,CAACyC,CAAC,CAAC,CAAClC,SAAS,GAAG,CAAC,CAAC;IACzB;IAEA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGL,QAAQ,CAACuB,KAAK,CAAC,CAAC,EAAEhB,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MACpDzC,KAAK,CAACkC,QAAQ,CAACb,WAAW,CAACoB,CAAC,CAAC,CAAC,CAAClC,SAAS,GAAGkC,CAAC;IAC9C;EACF,CAAC;EAED;EACA;AACF;AACA;;EAEE7C,KAAK,CAACQ,SAAS,CAACwD,KAAK,GAAG,YAAY;IAClC,IAAIC,KAAK,GAAG,IAAIjE,KAAK,CAAC,IAAI,CAACO,SAAS,CAAC;IACrC,IAAIJ,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IAEtB,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,KAAK,CAACyC,MAAM,EAAEC,CAAC,EAAE,EAAE;MACrCoB,KAAK,CAACvD,OAAO,CAACP,KAAK,CAAC0C,CAAC,CAAC,CAAC9C,EAAE,EAAEI,KAAK,CAAC0C,CAAC,CAAC,CAAClC,SAAS,CAAC;IAChD;IAEA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,KAAK,CAACwC,MAAM,EAAEC,CAAC,EAAE,EAAE;MACrC,IAAIa,CAAC,GAAGtD,KAAK,CAACyC,CAAC,CAAC;MAChBoB,KAAK,CAACtC,OAAO,CAAC+B,CAAC,CAACV,KAAK,CAACjD,EAAE,EAAE2D,CAAC,CAACT,KAAK,CAAClD,EAAE,EAAE2D,CAAC,CAAC/C,SAAS,CAAC;IACpD;IAEA,OAAOsD,KAAK;EACd,CAAC;EAED;EACA,OAAOjE,KAAK;AACd,CAAC,CAAC,CAAC;AAEH,IAAImB,SAAS,GACb;AACA,YAAY;EACV,SAASA,SAASA,CAACpB,EAAE,EAAEY,SAAS,EAAE;IAChC,IAAI,CAACyB,OAAO,GAAG,EAAE;IACjB,IAAI,CAACD,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC/B,KAAK,GAAG,EAAE;IACf,IAAI,CAACO,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACZ,EAAE,GAAGA,EAAE,IAAI,IAAI,GAAG,EAAE,GAAGA,EAAE;IAC9B,IAAI,CAACY,SAAS,GAAGA,SAAS,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGA,SAAS;EACrD;EACA;AACF;AACA;;EAGEQ,SAAS,CAACX,SAAS,CAAC0D,MAAM,GAAG,YAAY;IACvC,OAAO,IAAI,CAAC9D,KAAK,CAACwC,MAAM;EAC1B,CAAC;EACD;AACF;AACA;;EAGEzB,SAAS,CAACX,SAAS,CAAC2D,QAAQ,GAAG,YAAY;IACzC,OAAO,IAAI,CAAC/B,OAAO,CAACQ,MAAM;EAC5B,CAAC;EACD;AACF;AACA;;EAGEzB,SAAS,CAACX,SAAS,CAAC4D,SAAS,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACjC,QAAQ,CAACS,MAAM;EAC7B,CAAC;EAEDzB,SAAS,CAACX,SAAS,CAAC6D,QAAQ,GAAG,UAAUC,IAAI,EAAE;IAC7C,IAAI,IAAI,CAAC3D,SAAS,GAAG,CAAC,EAAE;MACtB;IACF;IAEA,IAAIsD,KAAK,GAAG,IAAI,CAAC7C,SAAS;IAC1B,IAAImD,SAAS,GAAGN,KAAK,CAACzC,IAAI,CAACgD,YAAY,CAAC,IAAI,CAAC7D,SAAS,CAAC;IACvD,OAAO4D,SAAS,CAACF,QAAQ,CAACC,IAAI,CAAC;EACjC,CAAC;EAEDnD,SAAS,CAACX,SAAS,CAACiE,sBAAsB,GAAG,YAAY;IACvD,IAAIC,WAAW,GAAG;MAChBzC,IAAI,EAAE,EAAE;MACRf,IAAI,EAAE;IACR,CAAC;IAED,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACzC,KAAK,CAACwC,MAAM,EAAEC,CAAC,EAAE,EAAE;MAC1C,IAAI8B,YAAY,GAAG,IAAI,CAACvE,KAAK,CAACyC,CAAC,CAAC;MAEhC,IAAI8B,YAAY,CAAChE,SAAS,GAAG,CAAC,EAAE;QAC9B;MACF;MAEA+D,WAAW,CAACzC,IAAI,CAACZ,IAAI,CAACsD,YAAY,CAAChE,SAAS,CAAC;MAC7C+D,WAAW,CAACxD,IAAI,CAACG,IAAI,CAACsD,YAAY,CAAC3B,KAAK,CAACrC,SAAS,EAAEgE,YAAY,CAAC1B,KAAK,CAACtC,SAAS,CAAC;IACnF;IAEA,OAAO+D,WAAW;EACpB,CAAC;EAED,OAAOvD,SAAS;AAClB,CAAC,CAAC,CAAC;AAEH,IAAIe,SAAS,GACb;AACA,YAAY;EACV,SAASA,SAASA,CAACN,EAAE,EAAEC,EAAE,EAAElB,SAAS,EAAE;IACpC,IAAI,CAACA,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACqC,KAAK,GAAGpB,EAAE;IACf,IAAI,CAACqB,KAAK,GAAGpB,EAAE;IACf,IAAI,CAAClB,SAAS,GAAGA,SAAS,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGA,SAAS;EACrD,CAAC,CAAC;;EAGFuB,SAAS,CAAC1B,SAAS,CAAC6D,QAAQ,GAAG,UAAUC,IAAI,EAAE;IAC7C,IAAI,IAAI,CAAC3D,SAAS,GAAG,CAAC,EAAE;MACtB;IACF;IAEA,IAAIsD,KAAK,GAAG,IAAI,CAAC7C,SAAS;IAC1B,IAAImD,SAAS,GAAGN,KAAK,CAAC3B,QAAQ,CAACkC,YAAY,CAAC,IAAI,CAAC7D,SAAS,CAAC;IAC3D,OAAO4D,SAAS,CAACF,QAAQ,CAACC,IAAI,CAAC;EACjC,CAAC;EAEDpC,SAAS,CAAC1B,SAAS,CAACiE,sBAAsB,GAAG,YAAY;IACvD,OAAO;MACLxC,IAAI,EAAE,CAAC,IAAI,CAACtB,SAAS,CAAC;MACtBO,IAAI,EAAE,CAAC,IAAI,CAAC8B,KAAK,CAACrC,SAAS,EAAE,IAAI,CAACsC,KAAK,CAACtC,SAAS;IACnD,CAAC;EACH,CAAC;EAED,OAAOuB,SAAS;AAClB,CAAC,CAAC,CAAC;AAEH,SAAS0C,yBAAyBA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EACrD,OAAO;IACL;AACJ;AACA;IACIC,QAAQ,EAAE,SAAAA,CAAUC,SAAS,EAAE;MAC7B,IAAIxD,IAAI,GAAG,IAAI,CAACqD,QAAQ,CAAC,CAACC,QAAQ,CAAC;MACnC,OAAOtD,IAAI,CAACyD,QAAQ,CAAC,CAAC,CAACC,GAAG,CAAC1D,IAAI,CAAC2D,iBAAiB,CAACH,SAAS,IAAI,OAAO,CAAC,EAAE,IAAI,CAACrE,SAAS,CAAC;IAC1F,CAAC;IACD;IACAyE,SAAS,EAAE,SAAAA,CAAUpD,GAAG,EAAEqD,KAAK,EAAE;MAC/B,IAAI,CAAC1E,SAAS,IAAI,CAAC,IAAI,IAAI,CAACkE,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACQ,aAAa,CAAC,IAAI,CAAC3E,SAAS,EAAEqB,GAAG,EAAEqD,KAAK,CAAC;IAC3F,CAAC;IACDE,SAAS,EAAE,SAAAA,CAAUvD,GAAG,EAAE;MACxB,OAAO,IAAI,CAAC6C,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACU,aAAa,CAAC,IAAI,CAAC7E,SAAS,EAAEqB,GAAG,CAAC;IACpE,CAAC;IACDyD,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAEC,KAAK,EAAE;MAClC,IAAI,CAAChF,SAAS,IAAI,CAAC,IAAI,IAAI,CAACkE,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACc,aAAa,CAAC,IAAI,CAACjF,SAAS,EAAE+E,MAAM,EAAEC,KAAK,CAAC;IAC9F,CAAC;IACDE,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrB,OAAO,IAAI,CAAChB,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACgB,aAAa,CAAC,IAAI,CAACnF,SAAS,CAAC;IAC/D,CAAC;IACDoF,YAAY,EAAE,SAAAA,CAAA,EAAY;MACxB,OAAO,IAAI,CAAClB,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACkB,gBAAgB,CAAC,IAAI,CAACrF,SAAS,CAAC;IAClE,CAAC;IACDc,WAAW,EAAE,SAAAA,CAAA,EAAY;MACvB,OAAO,IAAI,CAACoD,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACrD,WAAW,CAAC,IAAI,CAACd,SAAS,CAAC;IAC7D;EACF,CAAC;AACH;AAEA;AACA;AACA;AACAd,MAAM,CAACoG,KAAK,CAAC9E,SAAS,EAAEyD,yBAAyB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AACvE/E,MAAM,CAACoG,KAAK,CAAC/D,SAAS,EAAE0C,yBAAyB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AAC3E,eAAe5E,KAAK;AACpB,SAASmB,SAAS,EAAEe,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}