{"ast": null, "code": "export default function assign(target, dirtyObject) {\n  if (target == null) {\n    throw new TypeError('assign requires that input parameter not be null or undefined');\n  }\n  dirtyObject = dirtyObject || {};\n  for (var property in dirtyObject) {\n    if (Object.prototype.hasOwnProperty.call(dirtyObject, property)) {\n      target[property] = dirtyObject[property];\n    }\n  }\n  return target;\n}", "map": {"version": 3, "names": ["assign", "target", "dirtyObject", "TypeError", "property", "Object", "prototype", "hasOwnProperty", "call"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/_lib/assign/index.js"], "sourcesContent": ["export default function assign(target, dirtyObject) {\n  if (target == null) {\n    throw new TypeError('assign requires that input parameter not be null or undefined');\n  }\n\n  dirtyObject = dirtyObject || {};\n\n  for (var property in dirtyObject) {\n    if (Object.prototype.hasOwnProperty.call(dirtyObject, property)) {\n      target[property] = dirtyObject[property];\n    }\n  }\n\n  return target;\n}"], "mappings": "AAAA,eAAe,SAASA,MAAMA,CAACC,MAAM,EAAEC,WAAW,EAAE;EAClD,IAAID,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAIE,SAAS,CAAC,+DAA+D,CAAC;EACtF;EAEAD,WAAW,GAAGA,WAAW,IAAI,CAAC,CAAC;EAE/B,KAAK,IAAIE,QAAQ,IAAIF,WAAW,EAAE;IAChC,IAAIG,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACN,WAAW,EAAEE,QAAQ,CAAC,EAAE;MAC/DH,MAAM,CAACG,QAAQ,CAAC,GAAGF,WAAW,CAACE,QAAQ,CAAC;IAC1C;EACF;EAEA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}