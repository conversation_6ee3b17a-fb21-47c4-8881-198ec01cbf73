{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport LinearGradient from 'zrender/lib/graphic/LinearGradient.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport VisualMapView from './VisualMapView.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as numberUtil from '../../util/number.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport * as helper from './helper.js';\nimport * as modelUtil from '../../util/model.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nimport { setAsHighDownDispatcher } from '../../util/states.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nvar linearMap = numberUtil.linearMap;\nvar each = zrUtil.each;\nvar mathMin = Math.min;\nvar mathMax = Math.max; // Arbitrary value\n\nvar HOVER_LINK_SIZE = 12;\nvar HOVER_LINK_OUT = 6; // Notice:\n// Any \"interval\" should be by the order of [low, high].\n// \"handle0\" (handleIndex === 0) maps to\n// low data value: this._dataInterval[0] and has low coord.\n// \"handle1\" (handleIndex === 1) maps to\n// high data value: this._dataInterval[1] and has high coord.\n// The logic of transform is implemented in this._createBarGroup.\n\nvar ContinuousView = /** @class */\nfunction (_super) {\n  __extends(ContinuousView, _super);\n  function ContinuousView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ContinuousView.type;\n    _this._shapes = {};\n    _this._dataInterval = [];\n    _this._handleEnds = [];\n    _this._hoverLinkDataIndices = [];\n    return _this;\n  }\n  ContinuousView.prototype.doRender = function (visualMapModel, ecModel, api, payload) {\n    this._api = api;\n    if (!payload || payload.type !== 'selectDataRange' || payload.from !== this.uid) {\n      this._buildView();\n    }\n  };\n  ContinuousView.prototype._buildView = function () {\n    this.group.removeAll();\n    var visualMapModel = this.visualMapModel;\n    var thisGroup = this.group;\n    this._orient = visualMapModel.get('orient');\n    this._useHandle = visualMapModel.get('calculable');\n    this._resetInterval();\n    this._renderBar(thisGroup);\n    var dataRangeText = visualMapModel.get('text');\n    this._renderEndsText(thisGroup, dataRangeText, 0);\n    this._renderEndsText(thisGroup, dataRangeText, 1); // Do this for background size calculation.\n\n    this._updateView(true); // After updating view, inner shapes is built completely,\n    // and then background can be rendered.\n\n    this.renderBackground(thisGroup); // Real update view\n\n    this._updateView();\n    this._enableHoverLinkToSeries();\n    this._enableHoverLinkFromSeries();\n    this.positionGroup(thisGroup);\n  };\n  ContinuousView.prototype._renderEndsText = function (group, dataRangeText, endsIndex) {\n    if (!dataRangeText) {\n      return;\n    } // Compatible with ec2, text[0] map to high value, text[1] map low value.\n\n    var text = dataRangeText[1 - endsIndex];\n    text = text != null ? text + '' : '';\n    var visualMapModel = this.visualMapModel;\n    var textGap = visualMapModel.get('textGap');\n    var itemSize = visualMapModel.itemSize;\n    var barGroup = this._shapes.mainGroup;\n    var position = this._applyTransform([itemSize[0] / 2, endsIndex === 0 ? -textGap : itemSize[1] + textGap], barGroup);\n    var align = this._applyTransform(endsIndex === 0 ? 'bottom' : 'top', barGroup);\n    var orient = this._orient;\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    this.group.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        x: position[0],\n        y: position[1],\n        verticalAlign: orient === 'horizontal' ? 'middle' : align,\n        align: orient === 'horizontal' ? align : 'center',\n        text: text\n      })\n    }));\n  };\n  ContinuousView.prototype._renderBar = function (targetGroup) {\n    var visualMapModel = this.visualMapModel;\n    var shapes = this._shapes;\n    var itemSize = visualMapModel.itemSize;\n    var orient = this._orient;\n    var useHandle = this._useHandle;\n    var itemAlign = helper.getItemAlign(visualMapModel, this.api, itemSize);\n    var mainGroup = shapes.mainGroup = this._createBarGroup(itemAlign);\n    var gradientBarGroup = new graphic.Group();\n    mainGroup.add(gradientBarGroup); // Bar\n\n    gradientBarGroup.add(shapes.outOfRange = createPolygon());\n    gradientBarGroup.add(shapes.inRange = createPolygon(null, useHandle ? getCursor(this._orient) : null, zrUtil.bind(this._dragHandle, this, 'all', false), zrUtil.bind(this._dragHandle, this, 'all', true))); // A border radius clip.\n\n    gradientBarGroup.setClipPath(new graphic.Rect({\n      shape: {\n        x: 0,\n        y: 0,\n        width: itemSize[0],\n        height: itemSize[1],\n        r: 3\n      }\n    }));\n    var textRect = visualMapModel.textStyleModel.getTextRect('国');\n    var textSize = mathMax(textRect.width, textRect.height); // Handle\n\n    if (useHandle) {\n      shapes.handleThumbs = [];\n      shapes.handleLabels = [];\n      shapes.handleLabelPoints = [];\n      this._createHandle(visualMapModel, mainGroup, 0, itemSize, textSize, orient);\n      this._createHandle(visualMapModel, mainGroup, 1, itemSize, textSize, orient);\n    }\n    this._createIndicator(visualMapModel, mainGroup, itemSize, textSize, orient);\n    targetGroup.add(mainGroup);\n  };\n  ContinuousView.prototype._createHandle = function (visualMapModel, mainGroup, handleIndex, itemSize, textSize, orient) {\n    var onDrift = zrUtil.bind(this._dragHandle, this, handleIndex, false);\n    var onDragEnd = zrUtil.bind(this._dragHandle, this, handleIndex, true);\n    var handleSize = parsePercent(visualMapModel.get('handleSize'), itemSize[0]);\n    var handleThumb = createSymbol(visualMapModel.get('handleIcon'), -handleSize / 2, -handleSize / 2, handleSize, handleSize, null, true);\n    var cursor = getCursor(this._orient);\n    handleThumb.attr({\n      cursor: cursor,\n      draggable: true,\n      drift: onDrift,\n      ondragend: onDragEnd,\n      onmousemove: function (e) {\n        eventTool.stop(e.event);\n      }\n    });\n    handleThumb.x = itemSize[0] / 2;\n    handleThumb.useStyle(visualMapModel.getModel('handleStyle').getItemStyle());\n    handleThumb.setStyle({\n      strokeNoScale: true,\n      strokeFirst: true\n    });\n    handleThumb.style.lineWidth *= 2;\n    handleThumb.ensureState('emphasis').style = visualMapModel.getModel(['emphasis', 'handleStyle']).getItemStyle();\n    setAsHighDownDispatcher(handleThumb, true);\n    mainGroup.add(handleThumb); // Text is always horizontal layout but should not be effected by\n    // transform (orient/inverse). So label is built separately but not\n    // use zrender/graphic/helper/RectText, and is located based on view\n    // group (according to handleLabelPoint) but not barGroup.\n\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    var handleLabel = new graphic.Text({\n      cursor: cursor,\n      draggable: true,\n      drift: onDrift,\n      onmousemove: function (e) {\n        // Fot mobile devicem, prevent screen slider on the button.\n        eventTool.stop(e.event);\n      },\n      ondragend: onDragEnd,\n      style: createTextStyle(textStyleModel, {\n        x: 0,\n        y: 0,\n        text: ''\n      })\n    });\n    handleLabel.ensureState('blur').style = {\n      opacity: 0.1\n    };\n    handleLabel.stateTransition = {\n      duration: 200\n    };\n    this.group.add(handleLabel);\n    var handleLabelPoint = [handleSize, 0];\n    var shapes = this._shapes;\n    shapes.handleThumbs[handleIndex] = handleThumb;\n    shapes.handleLabelPoints[handleIndex] = handleLabelPoint;\n    shapes.handleLabels[handleIndex] = handleLabel;\n  };\n  ContinuousView.prototype._createIndicator = function (visualMapModel, mainGroup, itemSize, textSize, orient) {\n    var scale = parsePercent(visualMapModel.get('indicatorSize'), itemSize[0]);\n    var indicator = createSymbol(visualMapModel.get('indicatorIcon'), -scale / 2, -scale / 2, scale, scale, null, true);\n    indicator.attr({\n      cursor: 'move',\n      invisible: true,\n      silent: true,\n      x: itemSize[0] / 2\n    });\n    var indicatorStyle = visualMapModel.getModel('indicatorStyle').getItemStyle();\n    if (indicator instanceof ZRImage) {\n      var pathStyle = indicator.style;\n      indicator.useStyle(zrUtil.extend({\n        // TODO other properties like x, y ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, indicatorStyle));\n    } else {\n      indicator.useStyle(indicatorStyle);\n    }\n    mainGroup.add(indicator);\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    var indicatorLabel = new graphic.Text({\n      silent: true,\n      invisible: true,\n      style: createTextStyle(textStyleModel, {\n        x: 0,\n        y: 0,\n        text: ''\n      })\n    });\n    this.group.add(indicatorLabel);\n    var indicatorLabelPoint = [(orient === 'horizontal' ? textSize / 2 : HOVER_LINK_OUT) + itemSize[0] / 2, 0];\n    var shapes = this._shapes;\n    shapes.indicator = indicator;\n    shapes.indicatorLabel = indicatorLabel;\n    shapes.indicatorLabelPoint = indicatorLabelPoint;\n    this._firstShowIndicator = true;\n  };\n  ContinuousView.prototype._dragHandle = function (handleIndex, isEnd,\n  // dx is event from ondragend if isEnd is true. It's not used\n  dx, dy) {\n    if (!this._useHandle) {\n      return;\n    }\n    this._dragging = !isEnd;\n    if (!isEnd) {\n      // Transform dx, dy to bar coordination.\n      var vertex = this._applyTransform([dx, dy], this._shapes.mainGroup, true);\n      this._updateInterval(handleIndex, vertex[1]);\n      this._hideIndicator(); // Considering realtime, update view should be executed\n      // before dispatch action.\n\n      this._updateView();\n    } // dragEnd do not dispatch action when realtime.\n\n    if (isEnd === !this.visualMapModel.get('realtime')) {\n      // jshint ignore:line\n      this.api.dispatchAction({\n        type: 'selectDataRange',\n        from: this.uid,\n        visualMapId: this.visualMapModel.id,\n        selected: this._dataInterval.slice()\n      });\n    }\n    if (isEnd) {\n      !this._hovering && this._clearHoverLinkToSeries();\n    } else if (useHoverLinkOnHandle(this.visualMapModel)) {\n      this._doHoverLinkToSeries(this._handleEnds[handleIndex], false);\n    }\n  };\n  ContinuousView.prototype._resetInterval = function () {\n    var visualMapModel = this.visualMapModel;\n    var dataInterval = this._dataInterval = visualMapModel.getSelected();\n    var dataExtent = visualMapModel.getExtent();\n    var sizeExtent = [0, visualMapModel.itemSize[1]];\n    this._handleEnds = [linearMap(dataInterval[0], dataExtent, sizeExtent, true), linearMap(dataInterval[1], dataExtent, sizeExtent, true)];\n  };\n  /**\n   * @private\n   * @param {(number|string)} handleIndex 0 or 1 or 'all'\n   * @param {number} dx\n   * @param {number} dy\n   */\n\n  ContinuousView.prototype._updateInterval = function (handleIndex, delta) {\n    delta = delta || 0;\n    var visualMapModel = this.visualMapModel;\n    var handleEnds = this._handleEnds;\n    var sizeExtent = [0, visualMapModel.itemSize[1]];\n    sliderMove(delta, handleEnds, sizeExtent, handleIndex,\n    // cross is forbiden\n    0);\n    var dataExtent = visualMapModel.getExtent(); // Update data interval.\n\n    this._dataInterval = [linearMap(handleEnds[0], sizeExtent, dataExtent, true), linearMap(handleEnds[1], sizeExtent, dataExtent, true)];\n  };\n  ContinuousView.prototype._updateView = function (forSketch) {\n    var visualMapModel = this.visualMapModel;\n    var dataExtent = visualMapModel.getExtent();\n    var shapes = this._shapes;\n    var outOfRangeHandleEnds = [0, visualMapModel.itemSize[1]];\n    var inRangeHandleEnds = forSketch ? outOfRangeHandleEnds : this._handleEnds;\n    var visualInRange = this._createBarVisual(this._dataInterval, dataExtent, inRangeHandleEnds, 'inRange');\n    var visualOutOfRange = this._createBarVisual(dataExtent, dataExtent, outOfRangeHandleEnds, 'outOfRange');\n    shapes.inRange.setStyle({\n      fill: visualInRange.barColor // opacity: visualInRange.opacity\n    }).setShape('points', visualInRange.barPoints);\n    shapes.outOfRange.setStyle({\n      fill: visualOutOfRange.barColor // opacity: visualOutOfRange.opacity\n    }).setShape('points', visualOutOfRange.barPoints);\n    this._updateHandle(inRangeHandleEnds, visualInRange);\n  };\n  ContinuousView.prototype._createBarVisual = function (dataInterval, dataExtent, handleEnds, forceState) {\n    var opts = {\n      forceState: forceState,\n      convertOpacityToAlpha: true\n    };\n    var colorStops = this._makeColorGradient(dataInterval, opts);\n    var symbolSizes = [this.getControllerVisual(dataInterval[0], 'symbolSize', opts), this.getControllerVisual(dataInterval[1], 'symbolSize', opts)];\n    var barPoints = this._createBarPoints(handleEnds, symbolSizes);\n    return {\n      barColor: new LinearGradient(0, 0, 0, 1, colorStops),\n      barPoints: barPoints,\n      handlesColor: [colorStops[0].color, colorStops[colorStops.length - 1].color]\n    };\n  };\n  ContinuousView.prototype._makeColorGradient = function (dataInterval, opts) {\n    // Considering colorHue, which is not linear, so we have to sample\n    // to calculate gradient color stops, but not only caculate head\n    // and tail.\n    var sampleNumber = 100; // Arbitrary value.\n\n    var colorStops = [];\n    var step = (dataInterval[1] - dataInterval[0]) / sampleNumber;\n    colorStops.push({\n      color: this.getControllerVisual(dataInterval[0], 'color', opts),\n      offset: 0\n    });\n    for (var i = 1; i < sampleNumber; i++) {\n      var currValue = dataInterval[0] + step * i;\n      if (currValue > dataInterval[1]) {\n        break;\n      }\n      colorStops.push({\n        color: this.getControllerVisual(currValue, 'color', opts),\n        offset: i / sampleNumber\n      });\n    }\n    colorStops.push({\n      color: this.getControllerVisual(dataInterval[1], 'color', opts),\n      offset: 1\n    });\n    return colorStops;\n  };\n  ContinuousView.prototype._createBarPoints = function (handleEnds, symbolSizes) {\n    var itemSize = this.visualMapModel.itemSize;\n    return [[itemSize[0] - symbolSizes[0], handleEnds[0]], [itemSize[0], handleEnds[0]], [itemSize[0], handleEnds[1]], [itemSize[0] - symbolSizes[1], handleEnds[1]]];\n  };\n  ContinuousView.prototype._createBarGroup = function (itemAlign) {\n    var orient = this._orient;\n    var inverse = this.visualMapModel.get('inverse');\n    return new graphic.Group(orient === 'horizontal' && !inverse ? {\n      scaleX: itemAlign === 'bottom' ? 1 : -1,\n      rotation: Math.PI / 2\n    } : orient === 'horizontal' && inverse ? {\n      scaleX: itemAlign === 'bottom' ? -1 : 1,\n      rotation: -Math.PI / 2\n    } : orient === 'vertical' && !inverse ? {\n      scaleX: itemAlign === 'left' ? 1 : -1,\n      scaleY: -1\n    } : {\n      scaleX: itemAlign === 'left' ? 1 : -1\n    });\n  };\n  ContinuousView.prototype._updateHandle = function (handleEnds, visualInRange) {\n    if (!this._useHandle) {\n      return;\n    }\n    var shapes = this._shapes;\n    var visualMapModel = this.visualMapModel;\n    var handleThumbs = shapes.handleThumbs;\n    var handleLabels = shapes.handleLabels;\n    var itemSize = visualMapModel.itemSize;\n    var dataExtent = visualMapModel.getExtent();\n    each([0, 1], function (handleIndex) {\n      var handleThumb = handleThumbs[handleIndex];\n      handleThumb.setStyle('fill', visualInRange.handlesColor[handleIndex]);\n      handleThumb.y = handleEnds[handleIndex];\n      var val = linearMap(handleEnds[handleIndex], [0, itemSize[1]], dataExtent, true);\n      var symbolSize = this.getControllerVisual(val, 'symbolSize');\n      handleThumb.scaleX = handleThumb.scaleY = symbolSize / itemSize[0];\n      handleThumb.x = itemSize[0] - symbolSize / 2; // Update handle label position.\n\n      var textPoint = graphic.applyTransform(shapes.handleLabelPoints[handleIndex], graphic.getTransform(handleThumb, this.group));\n      handleLabels[handleIndex].setStyle({\n        x: textPoint[0],\n        y: textPoint[1],\n        text: visualMapModel.formatValueText(this._dataInterval[handleIndex]),\n        verticalAlign: 'middle',\n        align: this._orient === 'vertical' ? this._applyTransform('left', shapes.mainGroup) : 'center'\n      });\n    }, this);\n  };\n  ContinuousView.prototype._showIndicator = function (cursorValue, textValue, rangeSymbol, halfHoverLinkSize) {\n    var visualMapModel = this.visualMapModel;\n    var dataExtent = visualMapModel.getExtent();\n    var itemSize = visualMapModel.itemSize;\n    var sizeExtent = [0, itemSize[1]];\n    var shapes = this._shapes;\n    var indicator = shapes.indicator;\n    if (!indicator) {\n      return;\n    }\n    indicator.attr('invisible', false);\n    var opts = {\n      convertOpacityToAlpha: true\n    };\n    var color = this.getControllerVisual(cursorValue, 'color', opts);\n    var symbolSize = this.getControllerVisual(cursorValue, 'symbolSize');\n    var y = linearMap(cursorValue, dataExtent, sizeExtent, true);\n    var x = itemSize[0] - symbolSize / 2;\n    var oldIndicatorPos = {\n      x: indicator.x,\n      y: indicator.y\n    }; // Update handle label position.\n\n    indicator.y = y;\n    indicator.x = x;\n    var textPoint = graphic.applyTransform(shapes.indicatorLabelPoint, graphic.getTransform(indicator, this.group));\n    var indicatorLabel = shapes.indicatorLabel;\n    indicatorLabel.attr('invisible', false);\n    var align = this._applyTransform('left', shapes.mainGroup);\n    var orient = this._orient;\n    var isHorizontal = orient === 'horizontal';\n    indicatorLabel.setStyle({\n      text: (rangeSymbol ? rangeSymbol : '') + visualMapModel.formatValueText(textValue),\n      verticalAlign: isHorizontal ? align : 'middle',\n      align: isHorizontal ? 'center' : align\n    });\n    var indicatorNewProps = {\n      x: x,\n      y: y,\n      style: {\n        fill: color\n      }\n    };\n    var labelNewProps = {\n      style: {\n        x: textPoint[0],\n        y: textPoint[1]\n      }\n    };\n    if (visualMapModel.ecModel.isAnimationEnabled() && !this._firstShowIndicator) {\n      var animationCfg = {\n        duration: 100,\n        easing: 'cubicInOut',\n        additive: true\n      };\n      indicator.x = oldIndicatorPos.x;\n      indicator.y = oldIndicatorPos.y;\n      indicator.animateTo(indicatorNewProps, animationCfg);\n      indicatorLabel.animateTo(labelNewProps, animationCfg);\n    } else {\n      indicator.attr(indicatorNewProps);\n      indicatorLabel.attr(labelNewProps);\n    }\n    this._firstShowIndicator = false;\n    var handleLabels = this._shapes.handleLabels;\n    if (handleLabels) {\n      for (var i = 0; i < handleLabels.length; i++) {\n        // Fade out handle labels.\n        // NOTE: Must use api enter/leave on emphasis/blur/select state. Or the global states manager will change it.\n        this._api.enterBlur(handleLabels[i]);\n      }\n    }\n  };\n  ContinuousView.prototype._enableHoverLinkToSeries = function () {\n    var self = this;\n    this._shapes.mainGroup.on('mousemove', function (e) {\n      self._hovering = true;\n      if (!self._dragging) {\n        var itemSize = self.visualMapModel.itemSize;\n        var pos = self._applyTransform([e.offsetX, e.offsetY], self._shapes.mainGroup, true, true); // For hover link show when hover handle, which might be\n        // below or upper than sizeExtent.\n\n        pos[1] = mathMin(mathMax(0, pos[1]), itemSize[1]);\n        self._doHoverLinkToSeries(pos[1], 0 <= pos[0] && pos[0] <= itemSize[0]);\n      }\n    }).on('mouseout', function () {\n      // When mouse is out of handle, hoverLink still need\n      // to be displayed when realtime is set as false.\n      self._hovering = false;\n      !self._dragging && self._clearHoverLinkToSeries();\n    });\n  };\n  ContinuousView.prototype._enableHoverLinkFromSeries = function () {\n    var zr = this.api.getZr();\n    if (this.visualMapModel.option.hoverLink) {\n      zr.on('mouseover', this._hoverLinkFromSeriesMouseOver, this);\n      zr.on('mouseout', this._hideIndicator, this);\n    } else {\n      this._clearHoverLinkFromSeries();\n    }\n  };\n  ContinuousView.prototype._doHoverLinkToSeries = function (cursorPos, hoverOnBar) {\n    var visualMapModel = this.visualMapModel;\n    var itemSize = visualMapModel.itemSize;\n    if (!visualMapModel.option.hoverLink) {\n      return;\n    }\n    var sizeExtent = [0, itemSize[1]];\n    var dataExtent = visualMapModel.getExtent(); // For hover link show when hover handle, which might be below or upper than sizeExtent.\n\n    cursorPos = mathMin(mathMax(sizeExtent[0], cursorPos), sizeExtent[1]);\n    var halfHoverLinkSize = getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent);\n    var hoverRange = [cursorPos - halfHoverLinkSize, cursorPos + halfHoverLinkSize];\n    var cursorValue = linearMap(cursorPos, sizeExtent, dataExtent, true);\n    var valueRange = [linearMap(hoverRange[0], sizeExtent, dataExtent, true), linearMap(hoverRange[1], sizeExtent, dataExtent, true)]; // Consider data range is out of visualMap range, see test/visualMap-continuous.html,\n    // where china and india has very large population.\n\n    hoverRange[0] < sizeExtent[0] && (valueRange[0] = -Infinity);\n    hoverRange[1] > sizeExtent[1] && (valueRange[1] = Infinity); // Do not show indicator when mouse is over handle,\n    // otherwise labels overlap, especially when dragging.\n\n    if (hoverOnBar) {\n      if (valueRange[0] === -Infinity) {\n        this._showIndicator(cursorValue, valueRange[1], '< ', halfHoverLinkSize);\n      } else if (valueRange[1] === Infinity) {\n        this._showIndicator(cursorValue, valueRange[0], '> ', halfHoverLinkSize);\n      } else {\n        this._showIndicator(cursorValue, cursorValue, '≈ ', halfHoverLinkSize);\n      }\n    } // When realtime is set as false, handles, which are in barGroup,\n    // also trigger hoverLink, which help user to realize where they\n    // focus on when dragging. (see test/heatmap-large.html)\n    // When realtime is set as true, highlight will not show when hover\n    // handle, because the label on handle, which displays a exact value\n    // but not range, might mislead users.\n\n    var oldBatch = this._hoverLinkDataIndices;\n    var newBatch = [];\n    if (hoverOnBar || useHoverLinkOnHandle(visualMapModel)) {\n      newBatch = this._hoverLinkDataIndices = visualMapModel.findTargetDataIndices(valueRange);\n    }\n    var resultBatches = modelUtil.compressBatches(oldBatch, newBatch);\n    this._dispatchHighDown('downplay', helper.makeHighDownBatch(resultBatches[0], visualMapModel));\n    this._dispatchHighDown('highlight', helper.makeHighDownBatch(resultBatches[1], visualMapModel));\n  };\n  ContinuousView.prototype._hoverLinkFromSeriesMouseOver = function (e) {\n    var el = e.target;\n    var visualMapModel = this.visualMapModel;\n    if (!el || getECData(el).dataIndex == null) {\n      return;\n    }\n    var ecData = getECData(el);\n    var dataModel = this.ecModel.getSeriesByIndex(ecData.seriesIndex);\n    if (!visualMapModel.isTargetSeries(dataModel)) {\n      return;\n    }\n    var data = dataModel.getData(ecData.dataType);\n    var value = data.getStore().get(visualMapModel.getDataDimensionIndex(data), ecData.dataIndex);\n    if (!isNaN(value)) {\n      this._showIndicator(value, value);\n    }\n  };\n  ContinuousView.prototype._hideIndicator = function () {\n    var shapes = this._shapes;\n    shapes.indicator && shapes.indicator.attr('invisible', true);\n    shapes.indicatorLabel && shapes.indicatorLabel.attr('invisible', true);\n    var handleLabels = this._shapes.handleLabels;\n    if (handleLabels) {\n      for (var i = 0; i < handleLabels.length; i++) {\n        // Fade out handle labels.\n        // NOTE: Must use api enter/leave on emphasis/blur/select state. Or the global states manager will change it.\n        this._api.leaveBlur(handleLabels[i]);\n      }\n    }\n  };\n  ContinuousView.prototype._clearHoverLinkToSeries = function () {\n    this._hideIndicator();\n    var indices = this._hoverLinkDataIndices;\n    this._dispatchHighDown('downplay', helper.makeHighDownBatch(indices, this.visualMapModel));\n    indices.length = 0;\n  };\n  ContinuousView.prototype._clearHoverLinkFromSeries = function () {\n    this._hideIndicator();\n    var zr = this.api.getZr();\n    zr.off('mouseover', this._hoverLinkFromSeriesMouseOver);\n    zr.off('mouseout', this._hideIndicator);\n  };\n  ContinuousView.prototype._applyTransform = function (vertex, element, inverse, global) {\n    var transform = graphic.getTransform(element, global ? null : this.group);\n    return zrUtil.isArray(vertex) ? graphic.applyTransform(vertex, transform, inverse) : graphic.transformDirection(vertex, transform, inverse);\n  }; // TODO: TYPE more specified payload types.\n\n  ContinuousView.prototype._dispatchHighDown = function (type, batch) {\n    batch && batch.length && this.api.dispatchAction({\n      type: type,\n      batch: batch\n    });\n  };\n  /**\n   * @override\n   */\n\n  ContinuousView.prototype.dispose = function () {\n    this._clearHoverLinkFromSeries();\n    this._clearHoverLinkToSeries();\n  };\n  /**\n   * @override\n   */\n\n  ContinuousView.prototype.remove = function () {\n    this._clearHoverLinkFromSeries();\n    this._clearHoverLinkToSeries();\n  };\n  ContinuousView.type = 'visualMap.continuous';\n  return ContinuousView;\n}(VisualMapView);\nfunction createPolygon(points, cursor, onDrift, onDragEnd) {\n  return new graphic.Polygon({\n    shape: {\n      points: points\n    },\n    draggable: !!onDrift,\n    cursor: cursor,\n    drift: onDrift,\n    onmousemove: function (e) {\n      // Fot mobile devicem, prevent screen slider on the button.\n      eventTool.stop(e.event);\n    },\n    ondragend: onDragEnd\n  });\n}\nfunction getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent) {\n  var halfHoverLinkSize = HOVER_LINK_SIZE / 2;\n  var hoverLinkDataSize = visualMapModel.get('hoverLinkDataSize');\n  if (hoverLinkDataSize) {\n    halfHoverLinkSize = linearMap(hoverLinkDataSize, dataExtent, sizeExtent, true) / 2;\n  }\n  return halfHoverLinkSize;\n}\nfunction useHoverLinkOnHandle(visualMapModel) {\n  var hoverLinkOnHandle = visualMapModel.get('hoverLinkOnHandle');\n  return !!(hoverLinkOnHandle == null ? visualMapModel.get('realtime') : hoverLinkOnHandle);\n}\nfunction getCursor(orient) {\n  return orient === 'vertical' ? 'ns-resize' : 'ew-resize';\n}\nexport default ContinuousView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "LinearGradient", "eventTool", "VisualMapView", "graphic", "numberUtil", "slider<PERSON><PERSON>", "helper", "modelUtil", "parsePercent", "setAs<PERSON>ighDownD<PERSON><PERSON><PERSON><PERSON>", "createSymbol", "ZRImage", "getECData", "createTextStyle", "linearMap", "each", "mathMin", "Math", "min", "mathMax", "max", "HOVER_LINK_SIZE", "HOVER_LINK_OUT", "Continuous<PERSON><PERSON>w", "_super", "_this", "apply", "arguments", "type", "_shapes", "_dataInterval", "_handleEnds", "_hoverLinkDataIndices", "prototype", "doR<PERSON>", "visualMapModel", "ecModel", "api", "payload", "_api", "from", "uid", "_buildView", "group", "removeAll", "thisGroup", "_orient", "get", "_useHandle", "_resetInterval", "_renderBar", "dataRangeText", "_renderEndsText", "_updateView", "renderBackground", "_enableHoverLinkToSeries", "_enableHoverLinkFromSeries", "positionGroup", "endsIndex", "text", "textGap", "itemSize", "barGroup", "mainGroup", "position", "_applyTransform", "align", "orient", "textStyleModel", "add", "Text", "style", "x", "y", "verticalAlign", "targetGroup", "shapes", "useHandle", "itemAlign", "getItemAlign", "_createBarGroup", "gradientBarGroup", "Group", "outOfRange", "createPolygon", "inRange", "getCursor", "bind", "_drag<PERSON><PERSON>le", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rect", "shape", "width", "height", "r", "textRect", "getTextRect", "textSize", "handleThumbs", "handleLabels", "handleLabelPoints", "_createHandle", "_createIndicator", "handleIndex", "onDrift", "onDragEnd", "handleSize", "handleThumb", "cursor", "attr", "draggable", "drift", "ondragend", "<PERSON><PERSON><PERSON><PERSON>", "e", "stop", "event", "useStyle", "getModel", "getItemStyle", "setStyle", "strokeNoScale", "<PERSON><PERSON><PERSON><PERSON>", "lineWidth", "ensureState", "handleLabel", "opacity", "stateTransition", "duration", "handleLabelPoint", "scale", "indicator", "invisible", "silent", "indicatorStyle", "pathStyle", "extend", "image", "indicatorLabel", "indicatorLabelPoint", "_firstShowIndicator", "isEnd", "dx", "dy", "_dragging", "vertex", "_updateInterval", "_hideIndicator", "dispatchAction", "visualMapId", "id", "selected", "slice", "_hovering", "_clearHoverLinkToSeries", "useHoverLinkOnHandle", "_doHoverLinkToSeries", "dataInterval", "getSelected", "dataExtent", "getExtent", "sizeExtent", "delta", "handleEnds", "forSketch", "outOfRangeHandleEnds", "inRangeHandleEnds", "visualInRange", "_createBarVisual", "visualOutOfRange", "fill", "barColor", "setShape", "barPoints", "_update<PERSON><PERSON>le", "forceState", "opts", "convertOpacityToAlpha", "colorStops", "_makeColorGradient", "symbolSizes", "getControllerVisual", "_createBarPoints", "handlesColor", "color", "length", "sampleNumber", "step", "push", "offset", "i", "currValue", "inverse", "scaleX", "rotation", "PI", "scaleY", "val", "symbolSize", "textPoint", "applyTransform", "getTransform", "formatValueText", "_showIndicator", "cursor<PERSON><PERSON>ue", "textValue", "rangeSymbol", "halfHoverLinkSize", "oldIndicatorPos", "isHorizontal", "indicatorNewProps", "labelNewProps", "isAnimationEnabled", "animationCfg", "easing", "additive", "animateTo", "enterBlur", "self", "on", "pos", "offsetX", "offsetY", "zr", "getZr", "option", "hoverLink", "_hoverLinkFromSeriesMouseOver", "_clearHoverLinkFromSeries", "cursorPos", "hoverOnBar", "getHalfHoverLinkSize", "hoverRange", "valueRange", "Infinity", "oldBatch", "newBatch", "findTargetDataIndices", "resultBatches", "compressBatches", "_dispatchHighDown", "makeHighDownBatch", "el", "target", "dataIndex", "ecData", "dataModel", "getSeriesByIndex", "seriesIndex", "isTargetSeries", "data", "getData", "dataType", "value", "getStore", "getDataDimensionIndex", "isNaN", "leaveBlur", "indices", "off", "element", "global", "transform", "isArray", "transformDirection", "batch", "dispose", "remove", "points", "Polygon", "hoverLinkDataSize", "hoverLinkOnHandle"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/echarts/lib/component/visualMap/ContinuousView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport LinearGradient from 'zrender/lib/graphic/LinearGradient.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport VisualMapView from './VisualMapView.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as numberUtil from '../../util/number.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport * as helper from './helper.js';\nimport * as modelUtil from '../../util/model.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nimport { setAsHighDownDispatcher } from '../../util/states.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nvar linearMap = numberUtil.linearMap;\nvar each = zrUtil.each;\nvar mathMin = Math.min;\nvar mathMax = Math.max; // Arbitrary value\n\nvar HOVER_LINK_SIZE = 12;\nvar HOVER_LINK_OUT = 6; // Notice:\n// Any \"interval\" should be by the order of [low, high].\n// \"handle0\" (handleIndex === 0) maps to\n// low data value: this._dataInterval[0] and has low coord.\n// \"handle1\" (handleIndex === 1) maps to\n// high data value: this._dataInterval[1] and has high coord.\n// The logic of transform is implemented in this._createBarGroup.\n\nvar ContinuousView =\n/** @class */\nfunction (_super) {\n  __extends(ContinuousView, _super);\n\n  function ContinuousView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = ContinuousView.type;\n    _this._shapes = {};\n    _this._dataInterval = [];\n    _this._handleEnds = [];\n    _this._hoverLinkDataIndices = [];\n    return _this;\n  }\n\n  ContinuousView.prototype.doRender = function (visualMapModel, ecModel, api, payload) {\n    this._api = api;\n\n    if (!payload || payload.type !== 'selectDataRange' || payload.from !== this.uid) {\n      this._buildView();\n    }\n  };\n\n  ContinuousView.prototype._buildView = function () {\n    this.group.removeAll();\n    var visualMapModel = this.visualMapModel;\n    var thisGroup = this.group;\n    this._orient = visualMapModel.get('orient');\n    this._useHandle = visualMapModel.get('calculable');\n\n    this._resetInterval();\n\n    this._renderBar(thisGroup);\n\n    var dataRangeText = visualMapModel.get('text');\n\n    this._renderEndsText(thisGroup, dataRangeText, 0);\n\n    this._renderEndsText(thisGroup, dataRangeText, 1); // Do this for background size calculation.\n\n\n    this._updateView(true); // After updating view, inner shapes is built completely,\n    // and then background can be rendered.\n\n\n    this.renderBackground(thisGroup); // Real update view\n\n    this._updateView();\n\n    this._enableHoverLinkToSeries();\n\n    this._enableHoverLinkFromSeries();\n\n    this.positionGroup(thisGroup);\n  };\n\n  ContinuousView.prototype._renderEndsText = function (group, dataRangeText, endsIndex) {\n    if (!dataRangeText) {\n      return;\n    } // Compatible with ec2, text[0] map to high value, text[1] map low value.\n\n\n    var text = dataRangeText[1 - endsIndex];\n    text = text != null ? text + '' : '';\n    var visualMapModel = this.visualMapModel;\n    var textGap = visualMapModel.get('textGap');\n    var itemSize = visualMapModel.itemSize;\n    var barGroup = this._shapes.mainGroup;\n\n    var position = this._applyTransform([itemSize[0] / 2, endsIndex === 0 ? -textGap : itemSize[1] + textGap], barGroup);\n\n    var align = this._applyTransform(endsIndex === 0 ? 'bottom' : 'top', barGroup);\n\n    var orient = this._orient;\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    this.group.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        x: position[0],\n        y: position[1],\n        verticalAlign: orient === 'horizontal' ? 'middle' : align,\n        align: orient === 'horizontal' ? align : 'center',\n        text: text\n      })\n    }));\n  };\n\n  ContinuousView.prototype._renderBar = function (targetGroup) {\n    var visualMapModel = this.visualMapModel;\n    var shapes = this._shapes;\n    var itemSize = visualMapModel.itemSize;\n    var orient = this._orient;\n    var useHandle = this._useHandle;\n    var itemAlign = helper.getItemAlign(visualMapModel, this.api, itemSize);\n\n    var mainGroup = shapes.mainGroup = this._createBarGroup(itemAlign);\n\n    var gradientBarGroup = new graphic.Group();\n    mainGroup.add(gradientBarGroup); // Bar\n\n    gradientBarGroup.add(shapes.outOfRange = createPolygon());\n    gradientBarGroup.add(shapes.inRange = createPolygon(null, useHandle ? getCursor(this._orient) : null, zrUtil.bind(this._dragHandle, this, 'all', false), zrUtil.bind(this._dragHandle, this, 'all', true))); // A border radius clip.\n\n    gradientBarGroup.setClipPath(new graphic.Rect({\n      shape: {\n        x: 0,\n        y: 0,\n        width: itemSize[0],\n        height: itemSize[1],\n        r: 3\n      }\n    }));\n    var textRect = visualMapModel.textStyleModel.getTextRect('国');\n    var textSize = mathMax(textRect.width, textRect.height); // Handle\n\n    if (useHandle) {\n      shapes.handleThumbs = [];\n      shapes.handleLabels = [];\n      shapes.handleLabelPoints = [];\n\n      this._createHandle(visualMapModel, mainGroup, 0, itemSize, textSize, orient);\n\n      this._createHandle(visualMapModel, mainGroup, 1, itemSize, textSize, orient);\n    }\n\n    this._createIndicator(visualMapModel, mainGroup, itemSize, textSize, orient);\n\n    targetGroup.add(mainGroup);\n  };\n\n  ContinuousView.prototype._createHandle = function (visualMapModel, mainGroup, handleIndex, itemSize, textSize, orient) {\n    var onDrift = zrUtil.bind(this._dragHandle, this, handleIndex, false);\n    var onDragEnd = zrUtil.bind(this._dragHandle, this, handleIndex, true);\n    var handleSize = parsePercent(visualMapModel.get('handleSize'), itemSize[0]);\n    var handleThumb = createSymbol(visualMapModel.get('handleIcon'), -handleSize / 2, -handleSize / 2, handleSize, handleSize, null, true);\n    var cursor = getCursor(this._orient);\n    handleThumb.attr({\n      cursor: cursor,\n      draggable: true,\n      drift: onDrift,\n      ondragend: onDragEnd,\n      onmousemove: function (e) {\n        eventTool.stop(e.event);\n      }\n    });\n    handleThumb.x = itemSize[0] / 2;\n    handleThumb.useStyle(visualMapModel.getModel('handleStyle').getItemStyle());\n    handleThumb.setStyle({\n      strokeNoScale: true,\n      strokeFirst: true\n    });\n    handleThumb.style.lineWidth *= 2;\n    handleThumb.ensureState('emphasis').style = visualMapModel.getModel(['emphasis', 'handleStyle']).getItemStyle();\n    setAsHighDownDispatcher(handleThumb, true);\n    mainGroup.add(handleThumb); // Text is always horizontal layout but should not be effected by\n    // transform (orient/inverse). So label is built separately but not\n    // use zrender/graphic/helper/RectText, and is located based on view\n    // group (according to handleLabelPoint) but not barGroup.\n\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    var handleLabel = new graphic.Text({\n      cursor: cursor,\n      draggable: true,\n      drift: onDrift,\n      onmousemove: function (e) {\n        // Fot mobile devicem, prevent screen slider on the button.\n        eventTool.stop(e.event);\n      },\n      ondragend: onDragEnd,\n      style: createTextStyle(textStyleModel, {\n        x: 0,\n        y: 0,\n        text: ''\n      })\n    });\n    handleLabel.ensureState('blur').style = {\n      opacity: 0.1\n    };\n    handleLabel.stateTransition = {\n      duration: 200\n    };\n    this.group.add(handleLabel);\n    var handleLabelPoint = [handleSize, 0];\n    var shapes = this._shapes;\n    shapes.handleThumbs[handleIndex] = handleThumb;\n    shapes.handleLabelPoints[handleIndex] = handleLabelPoint;\n    shapes.handleLabels[handleIndex] = handleLabel;\n  };\n\n  ContinuousView.prototype._createIndicator = function (visualMapModel, mainGroup, itemSize, textSize, orient) {\n    var scale = parsePercent(visualMapModel.get('indicatorSize'), itemSize[0]);\n    var indicator = createSymbol(visualMapModel.get('indicatorIcon'), -scale / 2, -scale / 2, scale, scale, null, true);\n    indicator.attr({\n      cursor: 'move',\n      invisible: true,\n      silent: true,\n      x: itemSize[0] / 2\n    });\n    var indicatorStyle = visualMapModel.getModel('indicatorStyle').getItemStyle();\n\n    if (indicator instanceof ZRImage) {\n      var pathStyle = indicator.style;\n      indicator.useStyle(zrUtil.extend({\n        // TODO other properties like x, y ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, indicatorStyle));\n    } else {\n      indicator.useStyle(indicatorStyle);\n    }\n\n    mainGroup.add(indicator);\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    var indicatorLabel = new graphic.Text({\n      silent: true,\n      invisible: true,\n      style: createTextStyle(textStyleModel, {\n        x: 0,\n        y: 0,\n        text: ''\n      })\n    });\n    this.group.add(indicatorLabel);\n    var indicatorLabelPoint = [(orient === 'horizontal' ? textSize / 2 : HOVER_LINK_OUT) + itemSize[0] / 2, 0];\n    var shapes = this._shapes;\n    shapes.indicator = indicator;\n    shapes.indicatorLabel = indicatorLabel;\n    shapes.indicatorLabelPoint = indicatorLabelPoint;\n    this._firstShowIndicator = true;\n  };\n\n  ContinuousView.prototype._dragHandle = function (handleIndex, isEnd, // dx is event from ondragend if isEnd is true. It's not used\n  dx, dy) {\n    if (!this._useHandle) {\n      return;\n    }\n\n    this._dragging = !isEnd;\n\n    if (!isEnd) {\n      // Transform dx, dy to bar coordination.\n      var vertex = this._applyTransform([dx, dy], this._shapes.mainGroup, true);\n\n      this._updateInterval(handleIndex, vertex[1]);\n\n      this._hideIndicator(); // Considering realtime, update view should be executed\n      // before dispatch action.\n\n\n      this._updateView();\n    } // dragEnd do not dispatch action when realtime.\n\n\n    if (isEnd === !this.visualMapModel.get('realtime')) {\n      // jshint ignore:line\n      this.api.dispatchAction({\n        type: 'selectDataRange',\n        from: this.uid,\n        visualMapId: this.visualMapModel.id,\n        selected: this._dataInterval.slice()\n      });\n    }\n\n    if (isEnd) {\n      !this._hovering && this._clearHoverLinkToSeries();\n    } else if (useHoverLinkOnHandle(this.visualMapModel)) {\n      this._doHoverLinkToSeries(this._handleEnds[handleIndex], false);\n    }\n  };\n\n  ContinuousView.prototype._resetInterval = function () {\n    var visualMapModel = this.visualMapModel;\n    var dataInterval = this._dataInterval = visualMapModel.getSelected();\n    var dataExtent = visualMapModel.getExtent();\n    var sizeExtent = [0, visualMapModel.itemSize[1]];\n    this._handleEnds = [linearMap(dataInterval[0], dataExtent, sizeExtent, true), linearMap(dataInterval[1], dataExtent, sizeExtent, true)];\n  };\n  /**\n   * @private\n   * @param {(number|string)} handleIndex 0 or 1 or 'all'\n   * @param {number} dx\n   * @param {number} dy\n   */\n\n\n  ContinuousView.prototype._updateInterval = function (handleIndex, delta) {\n    delta = delta || 0;\n    var visualMapModel = this.visualMapModel;\n    var handleEnds = this._handleEnds;\n    var sizeExtent = [0, visualMapModel.itemSize[1]];\n    sliderMove(delta, handleEnds, sizeExtent, handleIndex, // cross is forbiden\n    0);\n    var dataExtent = visualMapModel.getExtent(); // Update data interval.\n\n    this._dataInterval = [linearMap(handleEnds[0], sizeExtent, dataExtent, true), linearMap(handleEnds[1], sizeExtent, dataExtent, true)];\n  };\n\n  ContinuousView.prototype._updateView = function (forSketch) {\n    var visualMapModel = this.visualMapModel;\n    var dataExtent = visualMapModel.getExtent();\n    var shapes = this._shapes;\n    var outOfRangeHandleEnds = [0, visualMapModel.itemSize[1]];\n    var inRangeHandleEnds = forSketch ? outOfRangeHandleEnds : this._handleEnds;\n\n    var visualInRange = this._createBarVisual(this._dataInterval, dataExtent, inRangeHandleEnds, 'inRange');\n\n    var visualOutOfRange = this._createBarVisual(dataExtent, dataExtent, outOfRangeHandleEnds, 'outOfRange');\n\n    shapes.inRange.setStyle({\n      fill: visualInRange.barColor // opacity: visualInRange.opacity\n\n    }).setShape('points', visualInRange.barPoints);\n    shapes.outOfRange.setStyle({\n      fill: visualOutOfRange.barColor // opacity: visualOutOfRange.opacity\n\n    }).setShape('points', visualOutOfRange.barPoints);\n\n    this._updateHandle(inRangeHandleEnds, visualInRange);\n  };\n\n  ContinuousView.prototype._createBarVisual = function (dataInterval, dataExtent, handleEnds, forceState) {\n    var opts = {\n      forceState: forceState,\n      convertOpacityToAlpha: true\n    };\n\n    var colorStops = this._makeColorGradient(dataInterval, opts);\n\n    var symbolSizes = [this.getControllerVisual(dataInterval[0], 'symbolSize', opts), this.getControllerVisual(dataInterval[1], 'symbolSize', opts)];\n\n    var barPoints = this._createBarPoints(handleEnds, symbolSizes);\n\n    return {\n      barColor: new LinearGradient(0, 0, 0, 1, colorStops),\n      barPoints: barPoints,\n      handlesColor: [colorStops[0].color, colorStops[colorStops.length - 1].color]\n    };\n  };\n\n  ContinuousView.prototype._makeColorGradient = function (dataInterval, opts) {\n    // Considering colorHue, which is not linear, so we have to sample\n    // to calculate gradient color stops, but not only caculate head\n    // and tail.\n    var sampleNumber = 100; // Arbitrary value.\n\n    var colorStops = [];\n    var step = (dataInterval[1] - dataInterval[0]) / sampleNumber;\n    colorStops.push({\n      color: this.getControllerVisual(dataInterval[0], 'color', opts),\n      offset: 0\n    });\n\n    for (var i = 1; i < sampleNumber; i++) {\n      var currValue = dataInterval[0] + step * i;\n\n      if (currValue > dataInterval[1]) {\n        break;\n      }\n\n      colorStops.push({\n        color: this.getControllerVisual(currValue, 'color', opts),\n        offset: i / sampleNumber\n      });\n    }\n\n    colorStops.push({\n      color: this.getControllerVisual(dataInterval[1], 'color', opts),\n      offset: 1\n    });\n    return colorStops;\n  };\n\n  ContinuousView.prototype._createBarPoints = function (handleEnds, symbolSizes) {\n    var itemSize = this.visualMapModel.itemSize;\n    return [[itemSize[0] - symbolSizes[0], handleEnds[0]], [itemSize[0], handleEnds[0]], [itemSize[0], handleEnds[1]], [itemSize[0] - symbolSizes[1], handleEnds[1]]];\n  };\n\n  ContinuousView.prototype._createBarGroup = function (itemAlign) {\n    var orient = this._orient;\n    var inverse = this.visualMapModel.get('inverse');\n    return new graphic.Group(orient === 'horizontal' && !inverse ? {\n      scaleX: itemAlign === 'bottom' ? 1 : -1,\n      rotation: Math.PI / 2\n    } : orient === 'horizontal' && inverse ? {\n      scaleX: itemAlign === 'bottom' ? -1 : 1,\n      rotation: -Math.PI / 2\n    } : orient === 'vertical' && !inverse ? {\n      scaleX: itemAlign === 'left' ? 1 : -1,\n      scaleY: -1\n    } : {\n      scaleX: itemAlign === 'left' ? 1 : -1\n    });\n  };\n\n  ContinuousView.prototype._updateHandle = function (handleEnds, visualInRange) {\n    if (!this._useHandle) {\n      return;\n    }\n\n    var shapes = this._shapes;\n    var visualMapModel = this.visualMapModel;\n    var handleThumbs = shapes.handleThumbs;\n    var handleLabels = shapes.handleLabels;\n    var itemSize = visualMapModel.itemSize;\n    var dataExtent = visualMapModel.getExtent();\n    each([0, 1], function (handleIndex) {\n      var handleThumb = handleThumbs[handleIndex];\n      handleThumb.setStyle('fill', visualInRange.handlesColor[handleIndex]);\n      handleThumb.y = handleEnds[handleIndex];\n      var val = linearMap(handleEnds[handleIndex], [0, itemSize[1]], dataExtent, true);\n      var symbolSize = this.getControllerVisual(val, 'symbolSize');\n      handleThumb.scaleX = handleThumb.scaleY = symbolSize / itemSize[0];\n      handleThumb.x = itemSize[0] - symbolSize / 2; // Update handle label position.\n\n      var textPoint = graphic.applyTransform(shapes.handleLabelPoints[handleIndex], graphic.getTransform(handleThumb, this.group));\n      handleLabels[handleIndex].setStyle({\n        x: textPoint[0],\n        y: textPoint[1],\n        text: visualMapModel.formatValueText(this._dataInterval[handleIndex]),\n        verticalAlign: 'middle',\n        align: this._orient === 'vertical' ? this._applyTransform('left', shapes.mainGroup) : 'center'\n      });\n    }, this);\n  };\n\n  ContinuousView.prototype._showIndicator = function (cursorValue, textValue, rangeSymbol, halfHoverLinkSize) {\n    var visualMapModel = this.visualMapModel;\n    var dataExtent = visualMapModel.getExtent();\n    var itemSize = visualMapModel.itemSize;\n    var sizeExtent = [0, itemSize[1]];\n    var shapes = this._shapes;\n    var indicator = shapes.indicator;\n\n    if (!indicator) {\n      return;\n    }\n\n    indicator.attr('invisible', false);\n    var opts = {\n      convertOpacityToAlpha: true\n    };\n    var color = this.getControllerVisual(cursorValue, 'color', opts);\n    var symbolSize = this.getControllerVisual(cursorValue, 'symbolSize');\n    var y = linearMap(cursorValue, dataExtent, sizeExtent, true);\n    var x = itemSize[0] - symbolSize / 2;\n    var oldIndicatorPos = {\n      x: indicator.x,\n      y: indicator.y\n    }; // Update handle label position.\n\n    indicator.y = y;\n    indicator.x = x;\n    var textPoint = graphic.applyTransform(shapes.indicatorLabelPoint, graphic.getTransform(indicator, this.group));\n    var indicatorLabel = shapes.indicatorLabel;\n    indicatorLabel.attr('invisible', false);\n\n    var align = this._applyTransform('left', shapes.mainGroup);\n\n    var orient = this._orient;\n    var isHorizontal = orient === 'horizontal';\n    indicatorLabel.setStyle({\n      text: (rangeSymbol ? rangeSymbol : '') + visualMapModel.formatValueText(textValue),\n      verticalAlign: isHorizontal ? align : 'middle',\n      align: isHorizontal ? 'center' : align\n    });\n    var indicatorNewProps = {\n      x: x,\n      y: y,\n      style: {\n        fill: color\n      }\n    };\n    var labelNewProps = {\n      style: {\n        x: textPoint[0],\n        y: textPoint[1]\n      }\n    };\n\n    if (visualMapModel.ecModel.isAnimationEnabled() && !this._firstShowIndicator) {\n      var animationCfg = {\n        duration: 100,\n        easing: 'cubicInOut',\n        additive: true\n      };\n      indicator.x = oldIndicatorPos.x;\n      indicator.y = oldIndicatorPos.y;\n      indicator.animateTo(indicatorNewProps, animationCfg);\n      indicatorLabel.animateTo(labelNewProps, animationCfg);\n    } else {\n      indicator.attr(indicatorNewProps);\n      indicatorLabel.attr(labelNewProps);\n    }\n\n    this._firstShowIndicator = false;\n    var handleLabels = this._shapes.handleLabels;\n\n    if (handleLabels) {\n      for (var i = 0; i < handleLabels.length; i++) {\n        // Fade out handle labels.\n        // NOTE: Must use api enter/leave on emphasis/blur/select state. Or the global states manager will change it.\n        this._api.enterBlur(handleLabels[i]);\n      }\n    }\n  };\n\n  ContinuousView.prototype._enableHoverLinkToSeries = function () {\n    var self = this;\n\n    this._shapes.mainGroup.on('mousemove', function (e) {\n      self._hovering = true;\n\n      if (!self._dragging) {\n        var itemSize = self.visualMapModel.itemSize;\n\n        var pos = self._applyTransform([e.offsetX, e.offsetY], self._shapes.mainGroup, true, true); // For hover link show when hover handle, which might be\n        // below or upper than sizeExtent.\n\n\n        pos[1] = mathMin(mathMax(0, pos[1]), itemSize[1]);\n\n        self._doHoverLinkToSeries(pos[1], 0 <= pos[0] && pos[0] <= itemSize[0]);\n      }\n    }).on('mouseout', function () {\n      // When mouse is out of handle, hoverLink still need\n      // to be displayed when realtime is set as false.\n      self._hovering = false;\n      !self._dragging && self._clearHoverLinkToSeries();\n    });\n  };\n\n  ContinuousView.prototype._enableHoverLinkFromSeries = function () {\n    var zr = this.api.getZr();\n\n    if (this.visualMapModel.option.hoverLink) {\n      zr.on('mouseover', this._hoverLinkFromSeriesMouseOver, this);\n      zr.on('mouseout', this._hideIndicator, this);\n    } else {\n      this._clearHoverLinkFromSeries();\n    }\n  };\n\n  ContinuousView.prototype._doHoverLinkToSeries = function (cursorPos, hoverOnBar) {\n    var visualMapModel = this.visualMapModel;\n    var itemSize = visualMapModel.itemSize;\n\n    if (!visualMapModel.option.hoverLink) {\n      return;\n    }\n\n    var sizeExtent = [0, itemSize[1]];\n    var dataExtent = visualMapModel.getExtent(); // For hover link show when hover handle, which might be below or upper than sizeExtent.\n\n    cursorPos = mathMin(mathMax(sizeExtent[0], cursorPos), sizeExtent[1]);\n    var halfHoverLinkSize = getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent);\n    var hoverRange = [cursorPos - halfHoverLinkSize, cursorPos + halfHoverLinkSize];\n    var cursorValue = linearMap(cursorPos, sizeExtent, dataExtent, true);\n    var valueRange = [linearMap(hoverRange[0], sizeExtent, dataExtent, true), linearMap(hoverRange[1], sizeExtent, dataExtent, true)]; // Consider data range is out of visualMap range, see test/visualMap-continuous.html,\n    // where china and india has very large population.\n\n    hoverRange[0] < sizeExtent[0] && (valueRange[0] = -Infinity);\n    hoverRange[1] > sizeExtent[1] && (valueRange[1] = Infinity); // Do not show indicator when mouse is over handle,\n    // otherwise labels overlap, especially when dragging.\n\n    if (hoverOnBar) {\n      if (valueRange[0] === -Infinity) {\n        this._showIndicator(cursorValue, valueRange[1], '< ', halfHoverLinkSize);\n      } else if (valueRange[1] === Infinity) {\n        this._showIndicator(cursorValue, valueRange[0], '> ', halfHoverLinkSize);\n      } else {\n        this._showIndicator(cursorValue, cursorValue, '≈ ', halfHoverLinkSize);\n      }\n    } // When realtime is set as false, handles, which are in barGroup,\n    // also trigger hoverLink, which help user to realize where they\n    // focus on when dragging. (see test/heatmap-large.html)\n    // When realtime is set as true, highlight will not show when hover\n    // handle, because the label on handle, which displays a exact value\n    // but not range, might mislead users.\n\n\n    var oldBatch = this._hoverLinkDataIndices;\n    var newBatch = [];\n\n    if (hoverOnBar || useHoverLinkOnHandle(visualMapModel)) {\n      newBatch = this._hoverLinkDataIndices = visualMapModel.findTargetDataIndices(valueRange);\n    }\n\n    var resultBatches = modelUtil.compressBatches(oldBatch, newBatch);\n\n    this._dispatchHighDown('downplay', helper.makeHighDownBatch(resultBatches[0], visualMapModel));\n\n    this._dispatchHighDown('highlight', helper.makeHighDownBatch(resultBatches[1], visualMapModel));\n  };\n\n  ContinuousView.prototype._hoverLinkFromSeriesMouseOver = function (e) {\n    var el = e.target;\n    var visualMapModel = this.visualMapModel;\n\n    if (!el || getECData(el).dataIndex == null) {\n      return;\n    }\n\n    var ecData = getECData(el);\n    var dataModel = this.ecModel.getSeriesByIndex(ecData.seriesIndex);\n\n    if (!visualMapModel.isTargetSeries(dataModel)) {\n      return;\n    }\n\n    var data = dataModel.getData(ecData.dataType);\n    var value = data.getStore().get(visualMapModel.getDataDimensionIndex(data), ecData.dataIndex);\n\n    if (!isNaN(value)) {\n      this._showIndicator(value, value);\n    }\n  };\n\n  ContinuousView.prototype._hideIndicator = function () {\n    var shapes = this._shapes;\n    shapes.indicator && shapes.indicator.attr('invisible', true);\n    shapes.indicatorLabel && shapes.indicatorLabel.attr('invisible', true);\n    var handleLabels = this._shapes.handleLabels;\n\n    if (handleLabels) {\n      for (var i = 0; i < handleLabels.length; i++) {\n        // Fade out handle labels.\n        // NOTE: Must use api enter/leave on emphasis/blur/select state. Or the global states manager will change it.\n        this._api.leaveBlur(handleLabels[i]);\n      }\n    }\n  };\n\n  ContinuousView.prototype._clearHoverLinkToSeries = function () {\n    this._hideIndicator();\n\n    var indices = this._hoverLinkDataIndices;\n\n    this._dispatchHighDown('downplay', helper.makeHighDownBatch(indices, this.visualMapModel));\n\n    indices.length = 0;\n  };\n\n  ContinuousView.prototype._clearHoverLinkFromSeries = function () {\n    this._hideIndicator();\n\n    var zr = this.api.getZr();\n    zr.off('mouseover', this._hoverLinkFromSeriesMouseOver);\n    zr.off('mouseout', this._hideIndicator);\n  };\n\n  ContinuousView.prototype._applyTransform = function (vertex, element, inverse, global) {\n    var transform = graphic.getTransform(element, global ? null : this.group);\n    return zrUtil.isArray(vertex) ? graphic.applyTransform(vertex, transform, inverse) : graphic.transformDirection(vertex, transform, inverse);\n  }; // TODO: TYPE more specified payload types.\n\n\n  ContinuousView.prototype._dispatchHighDown = function (type, batch) {\n    batch && batch.length && this.api.dispatchAction({\n      type: type,\n      batch: batch\n    });\n  };\n  /**\n   * @override\n   */\n\n\n  ContinuousView.prototype.dispose = function () {\n    this._clearHoverLinkFromSeries();\n\n    this._clearHoverLinkToSeries();\n  };\n  /**\n   * @override\n   */\n\n\n  ContinuousView.prototype.remove = function () {\n    this._clearHoverLinkFromSeries();\n\n    this._clearHoverLinkToSeries();\n  };\n\n  ContinuousView.type = 'visualMap.continuous';\n  return ContinuousView;\n}(VisualMapView);\n\nfunction createPolygon(points, cursor, onDrift, onDragEnd) {\n  return new graphic.Polygon({\n    shape: {\n      points: points\n    },\n    draggable: !!onDrift,\n    cursor: cursor,\n    drift: onDrift,\n    onmousemove: function (e) {\n      // Fot mobile devicem, prevent screen slider on the button.\n      eventTool.stop(e.event);\n    },\n    ondragend: onDragEnd\n  });\n}\n\nfunction getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent) {\n  var halfHoverLinkSize = HOVER_LINK_SIZE / 2;\n  var hoverLinkDataSize = visualMapModel.get('hoverLinkDataSize');\n\n  if (hoverLinkDataSize) {\n    halfHoverLinkSize = linearMap(hoverLinkDataSize, dataExtent, sizeExtent, true) / 2;\n  }\n\n  return halfHoverLinkSize;\n}\n\nfunction useHoverLinkOnHandle(visualMapModel) {\n  var hoverLinkOnHandle = visualMapModel.get('hoverLinkOnHandle');\n  return !!(hoverLinkOnHandle == null ? visualMapModel.get('realtime') : hoverLinkOnHandle);\n}\n\nfunction getCursor(orient) {\n  return orient === 'vertical' ? 'ns-resize' : 'ew-resize';\n}\n\nexport default ContinuousView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAO,KAAKC,SAAS,MAAM,2BAA2B;AACtD,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAO,KAAKC,MAAM,MAAM,aAAa;AACrC,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,uBAAuB,QAAQ,sBAAsB;AAC9D,SAASC,YAAY,QAAQ,sBAAsB;AACnD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,IAAIC,SAAS,GAAGV,UAAU,CAACU,SAAS;AACpC,IAAIC,IAAI,GAAGhB,MAAM,CAACgB,IAAI;AACtB,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC;;AAExB,IAAIC,eAAe,GAAG,EAAE;AACxB,IAAIC,cAAc,GAAG,CAAC,CAAC,CAAC;AACxB;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,cAAc,GAClB;AACA,UAAUC,MAAM,EAAE;EAChB1B,SAAS,CAACyB,cAAc,EAAEC,MAAM,CAAC;EAEjC,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,cAAc,CAACK,IAAI;IAChCH,KAAK,CAACI,OAAO,GAAG,CAAC,CAAC;IAClBJ,KAAK,CAACK,aAAa,GAAG,EAAE;IACxBL,KAAK,CAACM,WAAW,GAAG,EAAE;IACtBN,KAAK,CAACO,qBAAqB,GAAG,EAAE;IAChC,OAAOP,KAAK;EACd;EAEAF,cAAc,CAACU,SAAS,CAACC,QAAQ,GAAG,UAAUC,cAAc,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IACnF,IAAI,CAACC,IAAI,GAAGF,GAAG;IAEf,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACV,IAAI,KAAK,iBAAiB,IAAIU,OAAO,CAACE,IAAI,KAAK,IAAI,CAACC,GAAG,EAAE;MAC/E,IAAI,CAACC,UAAU,CAAC,CAAC;IACnB;EACF,CAAC;EAEDnB,cAAc,CAACU,SAAS,CAACS,UAAU,GAAG,YAAY;IAChD,IAAI,CAACC,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAIT,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIU,SAAS,GAAG,IAAI,CAACF,KAAK;IAC1B,IAAI,CAACG,OAAO,GAAGX,cAAc,CAACY,GAAG,CAAC,QAAQ,CAAC;IAC3C,IAAI,CAACC,UAAU,GAAGb,cAAc,CAACY,GAAG,CAAC,YAAY,CAAC;IAElD,IAAI,CAACE,cAAc,CAAC,CAAC;IAErB,IAAI,CAACC,UAAU,CAACL,SAAS,CAAC;IAE1B,IAAIM,aAAa,GAAGhB,cAAc,CAACY,GAAG,CAAC,MAAM,CAAC;IAE9C,IAAI,CAACK,eAAe,CAACP,SAAS,EAAEM,aAAa,EAAE,CAAC,CAAC;IAEjD,IAAI,CAACC,eAAe,CAACP,SAAS,EAAEM,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;;IAGnD,IAAI,CAACE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;IACxB;;IAGA,IAAI,CAACC,gBAAgB,CAACT,SAAS,CAAC,CAAC,CAAC;;IAElC,IAAI,CAACQ,WAAW,CAAC,CAAC;IAElB,IAAI,CAACE,wBAAwB,CAAC,CAAC;IAE/B,IAAI,CAACC,0BAA0B,CAAC,CAAC;IAEjC,IAAI,CAACC,aAAa,CAACZ,SAAS,CAAC;EAC/B,CAAC;EAEDtB,cAAc,CAACU,SAAS,CAACmB,eAAe,GAAG,UAAUT,KAAK,EAAEQ,aAAa,EAAEO,SAAS,EAAE;IACpF,IAAI,CAACP,aAAa,EAAE;MAClB;IACF,CAAC,CAAC;;IAGF,IAAIQ,IAAI,GAAGR,aAAa,CAAC,CAAC,GAAGO,SAAS,CAAC;IACvCC,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,EAAE,GAAG,EAAE;IACpC,IAAIxB,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIyB,OAAO,GAAGzB,cAAc,CAACY,GAAG,CAAC,SAAS,CAAC;IAC3C,IAAIc,QAAQ,GAAG1B,cAAc,CAAC0B,QAAQ;IACtC,IAAIC,QAAQ,GAAG,IAAI,CAACjC,OAAO,CAACkC,SAAS;IAErC,IAAIC,QAAQ,GAAG,IAAI,CAACC,eAAe,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEH,SAAS,KAAK,CAAC,GAAG,CAACE,OAAO,GAAGC,QAAQ,CAAC,CAAC,CAAC,GAAGD,OAAO,CAAC,EAAEE,QAAQ,CAAC;IAEpH,IAAII,KAAK,GAAG,IAAI,CAACD,eAAe,CAACP,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAG,KAAK,EAAEI,QAAQ,CAAC;IAE9E,IAAIK,MAAM,GAAG,IAAI,CAACrB,OAAO;IACzB,IAAIsB,cAAc,GAAG,IAAI,CAACjC,cAAc,CAACiC,cAAc;IACvD,IAAI,CAACzB,KAAK,CAAC0B,GAAG,CAAC,IAAIlE,OAAO,CAACmE,IAAI,CAAC;MAC9BC,KAAK,EAAE1D,eAAe,CAACuD,cAAc,EAAE;QACrCI,CAAC,EAAER,QAAQ,CAAC,CAAC,CAAC;QACdS,CAAC,EAAET,QAAQ,CAAC,CAAC,CAAC;QACdU,aAAa,EAAEP,MAAM,KAAK,YAAY,GAAG,QAAQ,GAAGD,KAAK;QACzDA,KAAK,EAAEC,MAAM,KAAK,YAAY,GAAGD,KAAK,GAAG,QAAQ;QACjDP,IAAI,EAAEA;MACR,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EAEDpC,cAAc,CAACU,SAAS,CAACiB,UAAU,GAAG,UAAUyB,WAAW,EAAE;IAC3D,IAAIxC,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIyC,MAAM,GAAG,IAAI,CAAC/C,OAAO;IACzB,IAAIgC,QAAQ,GAAG1B,cAAc,CAAC0B,QAAQ;IACtC,IAAIM,MAAM,GAAG,IAAI,CAACrB,OAAO;IACzB,IAAI+B,SAAS,GAAG,IAAI,CAAC7B,UAAU;IAC/B,IAAI8B,SAAS,GAAGxE,MAAM,CAACyE,YAAY,CAAC5C,cAAc,EAAE,IAAI,CAACE,GAAG,EAAEwB,QAAQ,CAAC;IAEvE,IAAIE,SAAS,GAAGa,MAAM,CAACb,SAAS,GAAG,IAAI,CAACiB,eAAe,CAACF,SAAS,CAAC;IAElE,IAAIG,gBAAgB,GAAG,IAAI9E,OAAO,CAAC+E,KAAK,CAAC,CAAC;IAC1CnB,SAAS,CAACM,GAAG,CAACY,gBAAgB,CAAC,CAAC,CAAC;;IAEjCA,gBAAgB,CAACZ,GAAG,CAACO,MAAM,CAACO,UAAU,GAAGC,aAAa,CAAC,CAAC,CAAC;IACzDH,gBAAgB,CAACZ,GAAG,CAACO,MAAM,CAACS,OAAO,GAAGD,aAAa,CAAC,IAAI,EAAEP,SAAS,GAAGS,SAAS,CAAC,IAAI,CAACxC,OAAO,CAAC,GAAG,IAAI,EAAE/C,MAAM,CAACwF,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,EAAEzF,MAAM,CAACwF,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7MP,gBAAgB,CAACQ,WAAW,CAAC,IAAItF,OAAO,CAACuF,IAAI,CAAC;MAC5CC,KAAK,EAAE;QACLnB,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJmB,KAAK,EAAE/B,QAAQ,CAAC,CAAC,CAAC;QAClBgC,MAAM,EAAEhC,QAAQ,CAAC,CAAC,CAAC;QACnBiC,CAAC,EAAE;MACL;IACF,CAAC,CAAC,CAAC;IACH,IAAIC,QAAQ,GAAG5D,cAAc,CAACiC,cAAc,CAAC4B,WAAW,CAAC,GAAG,CAAC;IAC7D,IAAIC,QAAQ,GAAG9E,OAAO,CAAC4E,QAAQ,CAACH,KAAK,EAAEG,QAAQ,CAACF,MAAM,CAAC,CAAC,CAAC;;IAEzD,IAAIhB,SAAS,EAAE;MACbD,MAAM,CAACsB,YAAY,GAAG,EAAE;MACxBtB,MAAM,CAACuB,YAAY,GAAG,EAAE;MACxBvB,MAAM,CAACwB,iBAAiB,GAAG,EAAE;MAE7B,IAAI,CAACC,aAAa,CAAClE,cAAc,EAAE4B,SAAS,EAAE,CAAC,EAAEF,QAAQ,EAAEoC,QAAQ,EAAE9B,MAAM,CAAC;MAE5E,IAAI,CAACkC,aAAa,CAAClE,cAAc,EAAE4B,SAAS,EAAE,CAAC,EAAEF,QAAQ,EAAEoC,QAAQ,EAAE9B,MAAM,CAAC;IAC9E;IAEA,IAAI,CAACmC,gBAAgB,CAACnE,cAAc,EAAE4B,SAAS,EAAEF,QAAQ,EAAEoC,QAAQ,EAAE9B,MAAM,CAAC;IAE5EQ,WAAW,CAACN,GAAG,CAACN,SAAS,CAAC;EAC5B,CAAC;EAEDxC,cAAc,CAACU,SAAS,CAACoE,aAAa,GAAG,UAAUlE,cAAc,EAAE4B,SAAS,EAAEwC,WAAW,EAAE1C,QAAQ,EAAEoC,QAAQ,EAAE9B,MAAM,EAAE;IACrH,IAAIqC,OAAO,GAAGzG,MAAM,CAACwF,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,EAAEe,WAAW,EAAE,KAAK,CAAC;IACrE,IAAIE,SAAS,GAAG1G,MAAM,CAACwF,IAAI,CAAC,IAAI,CAACC,WAAW,EAAE,IAAI,EAAEe,WAAW,EAAE,IAAI,CAAC;IACtE,IAAIG,UAAU,GAAGlG,YAAY,CAAC2B,cAAc,CAACY,GAAG,CAAC,YAAY,CAAC,EAAEc,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAI8C,WAAW,GAAGjG,YAAY,CAACyB,cAAc,CAACY,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC2D,UAAU,GAAG,CAAC,EAAE,CAACA,UAAU,GAAG,CAAC,EAAEA,UAAU,EAAEA,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC;IACtI,IAAIE,MAAM,GAAGtB,SAAS,CAAC,IAAI,CAACxC,OAAO,CAAC;IACpC6D,WAAW,CAACE,IAAI,CAAC;MACfD,MAAM,EAAEA,MAAM;MACdE,SAAS,EAAE,IAAI;MACfC,KAAK,EAAEP,OAAO;MACdQ,SAAS,EAAEP,SAAS;MACpBQ,WAAW,EAAE,SAAAA,CAAUC,CAAC,EAAE;QACxBjH,SAAS,CAACkH,IAAI,CAACD,CAAC,CAACE,KAAK,CAAC;MACzB;IACF,CAAC,CAAC;IACFT,WAAW,CAACnC,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IAC/B8C,WAAW,CAACU,QAAQ,CAAClF,cAAc,CAACmF,QAAQ,CAAC,aAAa,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAC3EZ,WAAW,CAACa,QAAQ,CAAC;MACnBC,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFf,WAAW,CAACpC,KAAK,CAACoD,SAAS,IAAI,CAAC;IAChChB,WAAW,CAACiB,WAAW,CAAC,UAAU,CAAC,CAACrD,KAAK,GAAGpC,cAAc,CAACmF,QAAQ,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IAC/G9G,uBAAuB,CAACkG,WAAW,EAAE,IAAI,CAAC;IAC1C5C,SAAS,CAACM,GAAG,CAACsC,WAAW,CAAC,CAAC,CAAC;IAC5B;IACA;IACA;;IAEA,IAAIvC,cAAc,GAAG,IAAI,CAACjC,cAAc,CAACiC,cAAc;IACvD,IAAIyD,WAAW,GAAG,IAAI1H,OAAO,CAACmE,IAAI,CAAC;MACjCsC,MAAM,EAAEA,MAAM;MACdE,SAAS,EAAE,IAAI;MACfC,KAAK,EAAEP,OAAO;MACdS,WAAW,EAAE,SAAAA,CAAUC,CAAC,EAAE;QACxB;QACAjH,SAAS,CAACkH,IAAI,CAACD,CAAC,CAACE,KAAK,CAAC;MACzB,CAAC;MACDJ,SAAS,EAAEP,SAAS;MACpBlC,KAAK,EAAE1D,eAAe,CAACuD,cAAc,EAAE;QACrCI,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJd,IAAI,EAAE;MACR,CAAC;IACH,CAAC,CAAC;IACFkE,WAAW,CAACD,WAAW,CAAC,MAAM,CAAC,CAACrD,KAAK,GAAG;MACtCuD,OAAO,EAAE;IACX,CAAC;IACDD,WAAW,CAACE,eAAe,GAAG;MAC5BC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAI,CAACrF,KAAK,CAAC0B,GAAG,CAACwD,WAAW,CAAC;IAC3B,IAAII,gBAAgB,GAAG,CAACvB,UAAU,EAAE,CAAC,CAAC;IACtC,IAAI9B,MAAM,GAAG,IAAI,CAAC/C,OAAO;IACzB+C,MAAM,CAACsB,YAAY,CAACK,WAAW,CAAC,GAAGI,WAAW;IAC9C/B,MAAM,CAACwB,iBAAiB,CAACG,WAAW,CAAC,GAAG0B,gBAAgB;IACxDrD,MAAM,CAACuB,YAAY,CAACI,WAAW,CAAC,GAAGsB,WAAW;EAChD,CAAC;EAEDtG,cAAc,CAACU,SAAS,CAACqE,gBAAgB,GAAG,UAAUnE,cAAc,EAAE4B,SAAS,EAAEF,QAAQ,EAAEoC,QAAQ,EAAE9B,MAAM,EAAE;IAC3G,IAAI+D,KAAK,GAAG1H,YAAY,CAAC2B,cAAc,CAACY,GAAG,CAAC,eAAe,CAAC,EAAEc,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1E,IAAIsE,SAAS,GAAGzH,YAAY,CAACyB,cAAc,CAACY,GAAG,CAAC,eAAe,CAAC,EAAE,CAACmF,KAAK,GAAG,CAAC,EAAE,CAACA,KAAK,GAAG,CAAC,EAAEA,KAAK,EAAEA,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IACnHC,SAAS,CAACtB,IAAI,CAAC;MACbD,MAAM,EAAE,MAAM;MACdwB,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE,IAAI;MACZ7D,CAAC,EAAEX,QAAQ,CAAC,CAAC,CAAC,GAAG;IACnB,CAAC,CAAC;IACF,IAAIyE,cAAc,GAAGnG,cAAc,CAACmF,QAAQ,CAAC,gBAAgB,CAAC,CAACC,YAAY,CAAC,CAAC;IAE7E,IAAIY,SAAS,YAAYxH,OAAO,EAAE;MAChC,IAAI4H,SAAS,GAAGJ,SAAS,CAAC5D,KAAK;MAC/B4D,SAAS,CAACd,QAAQ,CAACtH,MAAM,CAACyI,MAAM,CAAC;QAC/B;QACAC,KAAK,EAAEF,SAAS,CAACE,KAAK;QACtBjE,CAAC,EAAE+D,SAAS,CAAC/D,CAAC;QACdC,CAAC,EAAE8D,SAAS,CAAC9D,CAAC;QACdmB,KAAK,EAAE2C,SAAS,CAAC3C,KAAK;QACtBC,MAAM,EAAE0C,SAAS,CAAC1C;MACpB,CAAC,EAAEyC,cAAc,CAAC,CAAC;IACrB,CAAC,MAAM;MACLH,SAAS,CAACd,QAAQ,CAACiB,cAAc,CAAC;IACpC;IAEAvE,SAAS,CAACM,GAAG,CAAC8D,SAAS,CAAC;IACxB,IAAI/D,cAAc,GAAG,IAAI,CAACjC,cAAc,CAACiC,cAAc;IACvD,IAAIsE,cAAc,GAAG,IAAIvI,OAAO,CAACmE,IAAI,CAAC;MACpC+D,MAAM,EAAE,IAAI;MACZD,SAAS,EAAE,IAAI;MACf7D,KAAK,EAAE1D,eAAe,CAACuD,cAAc,EAAE;QACrCI,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJd,IAAI,EAAE;MACR,CAAC;IACH,CAAC,CAAC;IACF,IAAI,CAAChB,KAAK,CAAC0B,GAAG,CAACqE,cAAc,CAAC;IAC9B,IAAIC,mBAAmB,GAAG,CAAC,CAACxE,MAAM,KAAK,YAAY,GAAG8B,QAAQ,GAAG,CAAC,GAAG3E,cAAc,IAAIuC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC1G,IAAIe,MAAM,GAAG,IAAI,CAAC/C,OAAO;IACzB+C,MAAM,CAACuD,SAAS,GAAGA,SAAS;IAC5BvD,MAAM,CAAC8D,cAAc,GAAGA,cAAc;IACtC9D,MAAM,CAAC+D,mBAAmB,GAAGA,mBAAmB;IAChD,IAAI,CAACC,mBAAmB,GAAG,IAAI;EACjC,CAAC;EAEDrH,cAAc,CAACU,SAAS,CAACuD,WAAW,GAAG,UAAUe,WAAW,EAAEsC,KAAK;EAAE;EACrEC,EAAE,EAAEC,EAAE,EAAE;IACN,IAAI,CAAC,IAAI,CAAC/F,UAAU,EAAE;MACpB;IACF;IAEA,IAAI,CAACgG,SAAS,GAAG,CAACH,KAAK;IAEvB,IAAI,CAACA,KAAK,EAAE;MACV;MACA,IAAII,MAAM,GAAG,IAAI,CAAChF,eAAe,CAAC,CAAC6E,EAAE,EAAEC,EAAE,CAAC,EAAE,IAAI,CAAClH,OAAO,CAACkC,SAAS,EAAE,IAAI,CAAC;MAEzE,IAAI,CAACmF,eAAe,CAAC3C,WAAW,EAAE0C,MAAM,CAAC,CAAC,CAAC,CAAC;MAE5C,IAAI,CAACE,cAAc,CAAC,CAAC,CAAC,CAAC;MACvB;;MAGA,IAAI,CAAC9F,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC;;IAGF,IAAIwF,KAAK,KAAK,CAAC,IAAI,CAAC1G,cAAc,CAACY,GAAG,CAAC,UAAU,CAAC,EAAE;MAClD;MACA,IAAI,CAACV,GAAG,CAAC+G,cAAc,CAAC;QACtBxH,IAAI,EAAE,iBAAiB;QACvBY,IAAI,EAAE,IAAI,CAACC,GAAG;QACd4G,WAAW,EAAE,IAAI,CAAClH,cAAc,CAACmH,EAAE;QACnCC,QAAQ,EAAE,IAAI,CAACzH,aAAa,CAAC0H,KAAK,CAAC;MACrC,CAAC,CAAC;IACJ;IAEA,IAAIX,KAAK,EAAE;MACT,CAAC,IAAI,CAACY,SAAS,IAAI,IAAI,CAACC,uBAAuB,CAAC,CAAC;IACnD,CAAC,MAAM,IAAIC,oBAAoB,CAAC,IAAI,CAACxH,cAAc,CAAC,EAAE;MACpD,IAAI,CAACyH,oBAAoB,CAAC,IAAI,CAAC7H,WAAW,CAACwE,WAAW,CAAC,EAAE,KAAK,CAAC;IACjE;EACF,CAAC;EAEDhF,cAAc,CAACU,SAAS,CAACgB,cAAc,GAAG,YAAY;IACpD,IAAId,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI0H,YAAY,GAAG,IAAI,CAAC/H,aAAa,GAAGK,cAAc,CAAC2H,WAAW,CAAC,CAAC;IACpE,IAAIC,UAAU,GAAG5H,cAAc,CAAC6H,SAAS,CAAC,CAAC;IAC3C,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAE9H,cAAc,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC9B,WAAW,GAAG,CAACjB,SAAS,CAAC+I,YAAY,CAAC,CAAC,CAAC,EAAEE,UAAU,EAAEE,UAAU,EAAE,IAAI,CAAC,EAAEnJ,SAAS,CAAC+I,YAAY,CAAC,CAAC,CAAC,EAAEE,UAAU,EAAEE,UAAU,EAAE,IAAI,CAAC,CAAC;EACzI,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;;EAGE1I,cAAc,CAACU,SAAS,CAACiH,eAAe,GAAG,UAAU3C,WAAW,EAAE2D,KAAK,EAAE;IACvEA,KAAK,GAAGA,KAAK,IAAI,CAAC;IAClB,IAAI/H,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIgI,UAAU,GAAG,IAAI,CAACpI,WAAW;IACjC,IAAIkI,UAAU,GAAG,CAAC,CAAC,EAAE9H,cAAc,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChDxD,UAAU,CAAC6J,KAAK,EAAEC,UAAU,EAAEF,UAAU,EAAE1D,WAAW;IAAE;IACvD,CAAC,CAAC;IACF,IAAIwD,UAAU,GAAG5H,cAAc,CAAC6H,SAAS,CAAC,CAAC,CAAC,CAAC;;IAE7C,IAAI,CAAClI,aAAa,GAAG,CAAChB,SAAS,CAACqJ,UAAU,CAAC,CAAC,CAAC,EAAEF,UAAU,EAAEF,UAAU,EAAE,IAAI,CAAC,EAAEjJ,SAAS,CAACqJ,UAAU,CAAC,CAAC,CAAC,EAAEF,UAAU,EAAEF,UAAU,EAAE,IAAI,CAAC,CAAC;EACvI,CAAC;EAEDxI,cAAc,CAACU,SAAS,CAACoB,WAAW,GAAG,UAAU+G,SAAS,EAAE;IAC1D,IAAIjI,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI4H,UAAU,GAAG5H,cAAc,CAAC6H,SAAS,CAAC,CAAC;IAC3C,IAAIpF,MAAM,GAAG,IAAI,CAAC/C,OAAO;IACzB,IAAIwI,oBAAoB,GAAG,CAAC,CAAC,EAAElI,cAAc,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1D,IAAIyG,iBAAiB,GAAGF,SAAS,GAAGC,oBAAoB,GAAG,IAAI,CAACtI,WAAW;IAE3E,IAAIwI,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC1I,aAAa,EAAEiI,UAAU,EAAEO,iBAAiB,EAAE,SAAS,CAAC;IAEvG,IAAIG,gBAAgB,GAAG,IAAI,CAACD,gBAAgB,CAACT,UAAU,EAAEA,UAAU,EAAEM,oBAAoB,EAAE,YAAY,CAAC;IAExGzF,MAAM,CAACS,OAAO,CAACmC,QAAQ,CAAC;MACtBkD,IAAI,EAAEH,aAAa,CAACI,QAAQ,CAAC;IAE/B,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,EAAEL,aAAa,CAACM,SAAS,CAAC;IAC9CjG,MAAM,CAACO,UAAU,CAACqC,QAAQ,CAAC;MACzBkD,IAAI,EAAED,gBAAgB,CAACE,QAAQ,CAAC;IAElC,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,EAAEH,gBAAgB,CAACI,SAAS,CAAC;IAEjD,IAAI,CAACC,aAAa,CAACR,iBAAiB,EAAEC,aAAa,CAAC;EACtD,CAAC;EAEDhJ,cAAc,CAACU,SAAS,CAACuI,gBAAgB,GAAG,UAAUX,YAAY,EAAEE,UAAU,EAAEI,UAAU,EAAEY,UAAU,EAAE;IACtG,IAAIC,IAAI,GAAG;MACTD,UAAU,EAAEA,UAAU;MACtBE,qBAAqB,EAAE;IACzB,CAAC;IAED,IAAIC,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAACtB,YAAY,EAAEmB,IAAI,CAAC;IAE5D,IAAII,WAAW,GAAG,CAAC,IAAI,CAACC,mBAAmB,CAACxB,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,EAAEmB,IAAI,CAAC,EAAE,IAAI,CAACK,mBAAmB,CAACxB,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,EAAEmB,IAAI,CAAC,CAAC;IAEhJ,IAAIH,SAAS,GAAG,IAAI,CAACS,gBAAgB,CAACnB,UAAU,EAAEiB,WAAW,CAAC;IAE9D,OAAO;MACLT,QAAQ,EAAE,IAAI3K,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEkL,UAAU,CAAC;MACpDL,SAAS,EAAEA,SAAS;MACpBU,YAAY,EAAE,CAACL,UAAU,CAAC,CAAC,CAAC,CAACM,KAAK,EAAEN,UAAU,CAACA,UAAU,CAACO,MAAM,GAAG,CAAC,CAAC,CAACD,KAAK;IAC7E,CAAC;EACH,CAAC;EAEDjK,cAAc,CAACU,SAAS,CAACkJ,kBAAkB,GAAG,UAAUtB,YAAY,EAAEmB,IAAI,EAAE;IAC1E;IACA;IACA;IACA,IAAIU,YAAY,GAAG,GAAG,CAAC,CAAC;;IAExB,IAAIR,UAAU,GAAG,EAAE;IACnB,IAAIS,IAAI,GAAG,CAAC9B,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,IAAI6B,YAAY;IAC7DR,UAAU,CAACU,IAAI,CAAC;MACdJ,KAAK,EAAE,IAAI,CAACH,mBAAmB,CAACxB,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,EAAEmB,IAAI,CAAC;MAC/Da,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACrC,IAAIC,SAAS,GAAGlC,YAAY,CAAC,CAAC,CAAC,GAAG8B,IAAI,GAAGG,CAAC;MAE1C,IAAIC,SAAS,GAAGlC,YAAY,CAAC,CAAC,CAAC,EAAE;QAC/B;MACF;MAEAqB,UAAU,CAACU,IAAI,CAAC;QACdJ,KAAK,EAAE,IAAI,CAACH,mBAAmB,CAACU,SAAS,EAAE,OAAO,EAAEf,IAAI,CAAC;QACzDa,MAAM,EAAEC,CAAC,GAAGJ;MACd,CAAC,CAAC;IACJ;IAEAR,UAAU,CAACU,IAAI,CAAC;MACdJ,KAAK,EAAE,IAAI,CAACH,mBAAmB,CAACxB,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,EAAEmB,IAAI,CAAC;MAC/Da,MAAM,EAAE;IACV,CAAC,CAAC;IACF,OAAOX,UAAU;EACnB,CAAC;EAED3J,cAAc,CAACU,SAAS,CAACqJ,gBAAgB,GAAG,UAAUnB,UAAU,EAAEiB,WAAW,EAAE;IAC7E,IAAIvH,QAAQ,GAAG,IAAI,CAAC1B,cAAc,CAAC0B,QAAQ;IAC3C,OAAO,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,GAAGuH,WAAW,CAAC,CAAC,CAAC,EAAEjB,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAACtG,QAAQ,CAAC,CAAC,CAAC,EAAEsG,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAACtG,QAAQ,CAAC,CAAC,CAAC,EAAEsG,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAACtG,QAAQ,CAAC,CAAC,CAAC,GAAGuH,WAAW,CAAC,CAAC,CAAC,EAAEjB,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EACnK,CAAC;EAED5I,cAAc,CAACU,SAAS,CAAC+C,eAAe,GAAG,UAAUF,SAAS,EAAE;IAC9D,IAAIX,MAAM,GAAG,IAAI,CAACrB,OAAO;IACzB,IAAIkJ,OAAO,GAAG,IAAI,CAAC7J,cAAc,CAACY,GAAG,CAAC,SAAS,CAAC;IAChD,OAAO,IAAI5C,OAAO,CAAC+E,KAAK,CAACf,MAAM,KAAK,YAAY,IAAI,CAAC6H,OAAO,GAAG;MAC7DC,MAAM,EAAEnH,SAAS,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;MACvCoH,QAAQ,EAAEjL,IAAI,CAACkL,EAAE,GAAG;IACtB,CAAC,GAAGhI,MAAM,KAAK,YAAY,IAAI6H,OAAO,GAAG;MACvCC,MAAM,EAAEnH,SAAS,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;MACvCoH,QAAQ,EAAE,CAACjL,IAAI,CAACkL,EAAE,GAAG;IACvB,CAAC,GAAGhI,MAAM,KAAK,UAAU,IAAI,CAAC6H,OAAO,GAAG;MACtCC,MAAM,EAAEnH,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACrCsH,MAAM,EAAE,CAAC;IACX,CAAC,GAAG;MACFH,MAAM,EAAEnH,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC;EAEDvD,cAAc,CAACU,SAAS,CAAC6I,aAAa,GAAG,UAAUX,UAAU,EAAEI,aAAa,EAAE;IAC5E,IAAI,CAAC,IAAI,CAACvH,UAAU,EAAE;MACpB;IACF;IAEA,IAAI4B,MAAM,GAAG,IAAI,CAAC/C,OAAO;IACzB,IAAIM,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI+D,YAAY,GAAGtB,MAAM,CAACsB,YAAY;IACtC,IAAIC,YAAY,GAAGvB,MAAM,CAACuB,YAAY;IACtC,IAAItC,QAAQ,GAAG1B,cAAc,CAAC0B,QAAQ;IACtC,IAAIkG,UAAU,GAAG5H,cAAc,CAAC6H,SAAS,CAAC,CAAC;IAC3CjJ,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAUwF,WAAW,EAAE;MAClC,IAAII,WAAW,GAAGT,YAAY,CAACK,WAAW,CAAC;MAC3CI,WAAW,CAACa,QAAQ,CAAC,MAAM,EAAE+C,aAAa,CAACgB,YAAY,CAAChF,WAAW,CAAC,CAAC;MACrEI,WAAW,CAAClC,CAAC,GAAG0F,UAAU,CAAC5D,WAAW,CAAC;MACvC,IAAI8F,GAAG,GAAGvL,SAAS,CAACqJ,UAAU,CAAC5D,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE1C,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEkG,UAAU,EAAE,IAAI,CAAC;MAChF,IAAIuC,UAAU,GAAG,IAAI,CAACjB,mBAAmB,CAACgB,GAAG,EAAE,YAAY,CAAC;MAC5D1F,WAAW,CAACsF,MAAM,GAAGtF,WAAW,CAACyF,MAAM,GAAGE,UAAU,GAAGzI,QAAQ,CAAC,CAAC,CAAC;MAClE8C,WAAW,CAACnC,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,GAAGyI,UAAU,GAAG,CAAC,CAAC,CAAC;;MAE9C,IAAIC,SAAS,GAAGpM,OAAO,CAACqM,cAAc,CAAC5H,MAAM,CAACwB,iBAAiB,CAACG,WAAW,CAAC,EAAEpG,OAAO,CAACsM,YAAY,CAAC9F,WAAW,EAAE,IAAI,CAAChE,KAAK,CAAC,CAAC;MAC5HwD,YAAY,CAACI,WAAW,CAAC,CAACiB,QAAQ,CAAC;QACjChD,CAAC,EAAE+H,SAAS,CAAC,CAAC,CAAC;QACf9H,CAAC,EAAE8H,SAAS,CAAC,CAAC,CAAC;QACf5I,IAAI,EAAExB,cAAc,CAACuK,eAAe,CAAC,IAAI,CAAC5K,aAAa,CAACyE,WAAW,CAAC,CAAC;QACrE7B,aAAa,EAAE,QAAQ;QACvBR,KAAK,EAAE,IAAI,CAACpB,OAAO,KAAK,UAAU,GAAG,IAAI,CAACmB,eAAe,CAAC,MAAM,EAAEW,MAAM,CAACb,SAAS,CAAC,GAAG;MACxF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAEDxC,cAAc,CAACU,SAAS,CAAC0K,cAAc,GAAG,UAAUC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,iBAAiB,EAAE;IAC1G,IAAI5K,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI4H,UAAU,GAAG5H,cAAc,CAAC6H,SAAS,CAAC,CAAC;IAC3C,IAAInG,QAAQ,GAAG1B,cAAc,CAAC0B,QAAQ;IACtC,IAAIoG,UAAU,GAAG,CAAC,CAAC,EAAEpG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjC,IAAIe,MAAM,GAAG,IAAI,CAAC/C,OAAO;IACzB,IAAIsG,SAAS,GAAGvD,MAAM,CAACuD,SAAS;IAEhC,IAAI,CAACA,SAAS,EAAE;MACd;IACF;IAEAA,SAAS,CAACtB,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC;IAClC,IAAImE,IAAI,GAAG;MACTC,qBAAqB,EAAE;IACzB,CAAC;IACD,IAAIO,KAAK,GAAG,IAAI,CAACH,mBAAmB,CAACuB,WAAW,EAAE,OAAO,EAAE5B,IAAI,CAAC;IAChE,IAAIsB,UAAU,GAAG,IAAI,CAACjB,mBAAmB,CAACuB,WAAW,EAAE,YAAY,CAAC;IACpE,IAAInI,CAAC,GAAG3D,SAAS,CAAC8L,WAAW,EAAE7C,UAAU,EAAEE,UAAU,EAAE,IAAI,CAAC;IAC5D,IAAIzF,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,GAAGyI,UAAU,GAAG,CAAC;IACpC,IAAIU,eAAe,GAAG;MACpBxI,CAAC,EAAE2D,SAAS,CAAC3D,CAAC;MACdC,CAAC,EAAE0D,SAAS,CAAC1D;IACf,CAAC,CAAC,CAAC;;IAEH0D,SAAS,CAAC1D,CAAC,GAAGA,CAAC;IACf0D,SAAS,CAAC3D,CAAC,GAAGA,CAAC;IACf,IAAI+H,SAAS,GAAGpM,OAAO,CAACqM,cAAc,CAAC5H,MAAM,CAAC+D,mBAAmB,EAAExI,OAAO,CAACsM,YAAY,CAACtE,SAAS,EAAE,IAAI,CAACxF,KAAK,CAAC,CAAC;IAC/G,IAAI+F,cAAc,GAAG9D,MAAM,CAAC8D,cAAc;IAC1CA,cAAc,CAAC7B,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC;IAEvC,IAAI3C,KAAK,GAAG,IAAI,CAACD,eAAe,CAAC,MAAM,EAAEW,MAAM,CAACb,SAAS,CAAC;IAE1D,IAAII,MAAM,GAAG,IAAI,CAACrB,OAAO;IACzB,IAAImK,YAAY,GAAG9I,MAAM,KAAK,YAAY;IAC1CuE,cAAc,CAAClB,QAAQ,CAAC;MACtB7D,IAAI,EAAE,CAACmJ,WAAW,GAAGA,WAAW,GAAG,EAAE,IAAI3K,cAAc,CAACuK,eAAe,CAACG,SAAS,CAAC;MAClFnI,aAAa,EAAEuI,YAAY,GAAG/I,KAAK,GAAG,QAAQ;MAC9CA,KAAK,EAAE+I,YAAY,GAAG,QAAQ,GAAG/I;IACnC,CAAC,CAAC;IACF,IAAIgJ,iBAAiB,GAAG;MACtB1I,CAAC,EAAEA,CAAC;MACJC,CAAC,EAAEA,CAAC;MACJF,KAAK,EAAE;QACLmG,IAAI,EAAEc;MACR;IACF,CAAC;IACD,IAAI2B,aAAa,GAAG;MAClB5I,KAAK,EAAE;QACLC,CAAC,EAAE+H,SAAS,CAAC,CAAC,CAAC;QACf9H,CAAC,EAAE8H,SAAS,CAAC,CAAC;MAChB;IACF,CAAC;IAED,IAAIpK,cAAc,CAACC,OAAO,CAACgL,kBAAkB,CAAC,CAAC,IAAI,CAAC,IAAI,CAACxE,mBAAmB,EAAE;MAC5E,IAAIyE,YAAY,GAAG;QACjBrF,QAAQ,EAAE,GAAG;QACbsF,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE;MACZ,CAAC;MACDpF,SAAS,CAAC3D,CAAC,GAAGwI,eAAe,CAACxI,CAAC;MAC/B2D,SAAS,CAAC1D,CAAC,GAAGuI,eAAe,CAACvI,CAAC;MAC/B0D,SAAS,CAACqF,SAAS,CAACN,iBAAiB,EAAEG,YAAY,CAAC;MACpD3E,cAAc,CAAC8E,SAAS,CAACL,aAAa,EAAEE,YAAY,CAAC;IACvD,CAAC,MAAM;MACLlF,SAAS,CAACtB,IAAI,CAACqG,iBAAiB,CAAC;MACjCxE,cAAc,CAAC7B,IAAI,CAACsG,aAAa,CAAC;IACpC;IAEA,IAAI,CAACvE,mBAAmB,GAAG,KAAK;IAChC,IAAIzC,YAAY,GAAG,IAAI,CAACtE,OAAO,CAACsE,YAAY;IAE5C,IAAIA,YAAY,EAAE;MAChB,KAAK,IAAI2F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3F,YAAY,CAACsF,MAAM,EAAEK,CAAC,EAAE,EAAE;QAC5C;QACA;QACA,IAAI,CAACvJ,IAAI,CAACkL,SAAS,CAACtH,YAAY,CAAC2F,CAAC,CAAC,CAAC;MACtC;IACF;EACF,CAAC;EAEDvK,cAAc,CAACU,SAAS,CAACsB,wBAAwB,GAAG,YAAY;IAC9D,IAAImK,IAAI,GAAG,IAAI;IAEf,IAAI,CAAC7L,OAAO,CAACkC,SAAS,CAAC4J,EAAE,CAAC,WAAW,EAAE,UAAUzG,CAAC,EAAE;MAClDwG,IAAI,CAACjE,SAAS,GAAG,IAAI;MAErB,IAAI,CAACiE,IAAI,CAAC1E,SAAS,EAAE;QACnB,IAAInF,QAAQ,GAAG6J,IAAI,CAACvL,cAAc,CAAC0B,QAAQ;QAE3C,IAAI+J,GAAG,GAAGF,IAAI,CAACzJ,eAAe,CAAC,CAACiD,CAAC,CAAC2G,OAAO,EAAE3G,CAAC,CAAC4G,OAAO,CAAC,EAAEJ,IAAI,CAAC7L,OAAO,CAACkC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAC5F;;QAGA6J,GAAG,CAAC,CAAC,CAAC,GAAG5M,OAAO,CAACG,OAAO,CAAC,CAAC,EAAEyM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE/J,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEjD6J,IAAI,CAAC9D,oBAAoB,CAACgE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAI/J,QAAQ,CAAC,CAAC,CAAC,CAAC;MACzE;IACF,CAAC,CAAC,CAAC8J,EAAE,CAAC,UAAU,EAAE,YAAY;MAC5B;MACA;MACAD,IAAI,CAACjE,SAAS,GAAG,KAAK;MACtB,CAACiE,IAAI,CAAC1E,SAAS,IAAI0E,IAAI,CAAChE,uBAAuB,CAAC,CAAC;IACnD,CAAC,CAAC;EACJ,CAAC;EAEDnI,cAAc,CAACU,SAAS,CAACuB,0BAA0B,GAAG,YAAY;IAChE,IAAIuK,EAAE,GAAG,IAAI,CAAC1L,GAAG,CAAC2L,KAAK,CAAC,CAAC;IAEzB,IAAI,IAAI,CAAC7L,cAAc,CAAC8L,MAAM,CAACC,SAAS,EAAE;MACxCH,EAAE,CAACJ,EAAE,CAAC,WAAW,EAAE,IAAI,CAACQ,6BAA6B,EAAE,IAAI,CAAC;MAC5DJ,EAAE,CAACJ,EAAE,CAAC,UAAU,EAAE,IAAI,CAACxE,cAAc,EAAE,IAAI,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI,CAACiF,yBAAyB,CAAC,CAAC;IAClC;EACF,CAAC;EAED7M,cAAc,CAACU,SAAS,CAAC2H,oBAAoB,GAAG,UAAUyE,SAAS,EAAEC,UAAU,EAAE;IAC/E,IAAInM,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAI0B,QAAQ,GAAG1B,cAAc,CAAC0B,QAAQ;IAEtC,IAAI,CAAC1B,cAAc,CAAC8L,MAAM,CAACC,SAAS,EAAE;MACpC;IACF;IAEA,IAAIjE,UAAU,GAAG,CAAC,CAAC,EAAEpG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjC,IAAIkG,UAAU,GAAG5H,cAAc,CAAC6H,SAAS,CAAC,CAAC,CAAC,CAAC;;IAE7CqE,SAAS,GAAGrN,OAAO,CAACG,OAAO,CAAC8I,UAAU,CAAC,CAAC,CAAC,EAAEoE,SAAS,CAAC,EAAEpE,UAAU,CAAC,CAAC,CAAC,CAAC;IACrE,IAAI8C,iBAAiB,GAAGwB,oBAAoB,CAACpM,cAAc,EAAE4H,UAAU,EAAEE,UAAU,CAAC;IACpF,IAAIuE,UAAU,GAAG,CAACH,SAAS,GAAGtB,iBAAiB,EAAEsB,SAAS,GAAGtB,iBAAiB,CAAC;IAC/E,IAAIH,WAAW,GAAG9L,SAAS,CAACuN,SAAS,EAAEpE,UAAU,EAAEF,UAAU,EAAE,IAAI,CAAC;IACpE,IAAI0E,UAAU,GAAG,CAAC3N,SAAS,CAAC0N,UAAU,CAAC,CAAC,CAAC,EAAEvE,UAAU,EAAEF,UAAU,EAAE,IAAI,CAAC,EAAEjJ,SAAS,CAAC0N,UAAU,CAAC,CAAC,CAAC,EAAEvE,UAAU,EAAEF,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACnI;;IAEAyE,UAAU,CAAC,CAAC,CAAC,GAAGvE,UAAU,CAAC,CAAC,CAAC,KAAKwE,UAAU,CAAC,CAAC,CAAC,GAAG,CAACC,QAAQ,CAAC;IAC5DF,UAAU,CAAC,CAAC,CAAC,GAAGvE,UAAU,CAAC,CAAC,CAAC,KAAKwE,UAAU,CAAC,CAAC,CAAC,GAAGC,QAAQ,CAAC,CAAC,CAAC;IAC7D;;IAEA,IAAIJ,UAAU,EAAE;MACd,IAAIG,UAAU,CAAC,CAAC,CAAC,KAAK,CAACC,QAAQ,EAAE;QAC/B,IAAI,CAAC/B,cAAc,CAACC,WAAW,EAAE6B,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE1B,iBAAiB,CAAC;MAC1E,CAAC,MAAM,IAAI0B,UAAU,CAAC,CAAC,CAAC,KAAKC,QAAQ,EAAE;QACrC,IAAI,CAAC/B,cAAc,CAACC,WAAW,EAAE6B,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE1B,iBAAiB,CAAC;MAC1E,CAAC,MAAM;QACL,IAAI,CAACJ,cAAc,CAACC,WAAW,EAAEA,WAAW,EAAE,IAAI,EAAEG,iBAAiB,CAAC;MACxE;IACF,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;;IAGA,IAAI4B,QAAQ,GAAG,IAAI,CAAC3M,qBAAqB;IACzC,IAAI4M,QAAQ,GAAG,EAAE;IAEjB,IAAIN,UAAU,IAAI3E,oBAAoB,CAACxH,cAAc,CAAC,EAAE;MACtDyM,QAAQ,GAAG,IAAI,CAAC5M,qBAAqB,GAAGG,cAAc,CAAC0M,qBAAqB,CAACJ,UAAU,CAAC;IAC1F;IAEA,IAAIK,aAAa,GAAGvO,SAAS,CAACwO,eAAe,CAACJ,QAAQ,EAAEC,QAAQ,CAAC;IAEjE,IAAI,CAACI,iBAAiB,CAAC,UAAU,EAAE1O,MAAM,CAAC2O,iBAAiB,CAACH,aAAa,CAAC,CAAC,CAAC,EAAE3M,cAAc,CAAC,CAAC;IAE9F,IAAI,CAAC6M,iBAAiB,CAAC,WAAW,EAAE1O,MAAM,CAAC2O,iBAAiB,CAACH,aAAa,CAAC,CAAC,CAAC,EAAE3M,cAAc,CAAC,CAAC;EACjG,CAAC;EAEDZ,cAAc,CAACU,SAAS,CAACkM,6BAA6B,GAAG,UAAUjH,CAAC,EAAE;IACpE,IAAIgI,EAAE,GAAGhI,CAAC,CAACiI,MAAM;IACjB,IAAIhN,cAAc,GAAG,IAAI,CAACA,cAAc;IAExC,IAAI,CAAC+M,EAAE,IAAItO,SAAS,CAACsO,EAAE,CAAC,CAACE,SAAS,IAAI,IAAI,EAAE;MAC1C;IACF;IAEA,IAAIC,MAAM,GAAGzO,SAAS,CAACsO,EAAE,CAAC;IAC1B,IAAII,SAAS,GAAG,IAAI,CAAClN,OAAO,CAACmN,gBAAgB,CAACF,MAAM,CAACG,WAAW,CAAC;IAEjE,IAAI,CAACrN,cAAc,CAACsN,cAAc,CAACH,SAAS,CAAC,EAAE;MAC7C;IACF;IAEA,IAAII,IAAI,GAAGJ,SAAS,CAACK,OAAO,CAACN,MAAM,CAACO,QAAQ,CAAC;IAC7C,IAAIC,KAAK,GAAGH,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC/M,GAAG,CAACZ,cAAc,CAAC4N,qBAAqB,CAACL,IAAI,CAAC,EAAEL,MAAM,CAACD,SAAS,CAAC;IAE7F,IAAI,CAACY,KAAK,CAACH,KAAK,CAAC,EAAE;MACjB,IAAI,CAAClD,cAAc,CAACkD,KAAK,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EAEDtO,cAAc,CAACU,SAAS,CAACkH,cAAc,GAAG,YAAY;IACpD,IAAIvE,MAAM,GAAG,IAAI,CAAC/C,OAAO;IACzB+C,MAAM,CAACuD,SAAS,IAAIvD,MAAM,CAACuD,SAAS,CAACtB,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;IAC5DjC,MAAM,CAAC8D,cAAc,IAAI9D,MAAM,CAAC8D,cAAc,CAAC7B,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;IACtE,IAAIV,YAAY,GAAG,IAAI,CAACtE,OAAO,CAACsE,YAAY;IAE5C,IAAIA,YAAY,EAAE;MAChB,KAAK,IAAI2F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3F,YAAY,CAACsF,MAAM,EAAEK,CAAC,EAAE,EAAE;QAC5C;QACA;QACA,IAAI,CAACvJ,IAAI,CAAC0N,SAAS,CAAC9J,YAAY,CAAC2F,CAAC,CAAC,CAAC;MACtC;IACF;EACF,CAAC;EAEDvK,cAAc,CAACU,SAAS,CAACyH,uBAAuB,GAAG,YAAY;IAC7D,IAAI,CAACP,cAAc,CAAC,CAAC;IAErB,IAAI+G,OAAO,GAAG,IAAI,CAAClO,qBAAqB;IAExC,IAAI,CAACgN,iBAAiB,CAAC,UAAU,EAAE1O,MAAM,CAAC2O,iBAAiB,CAACiB,OAAO,EAAE,IAAI,CAAC/N,cAAc,CAAC,CAAC;IAE1F+N,OAAO,CAACzE,MAAM,GAAG,CAAC;EACpB,CAAC;EAEDlK,cAAc,CAACU,SAAS,CAACmM,yBAAyB,GAAG,YAAY;IAC/D,IAAI,CAACjF,cAAc,CAAC,CAAC;IAErB,IAAI4E,EAAE,GAAG,IAAI,CAAC1L,GAAG,CAAC2L,KAAK,CAAC,CAAC;IACzBD,EAAE,CAACoC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAChC,6BAA6B,CAAC;IACvDJ,EAAE,CAACoC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAChH,cAAc,CAAC;EACzC,CAAC;EAED5H,cAAc,CAACU,SAAS,CAACgC,eAAe,GAAG,UAAUgF,MAAM,EAAEmH,OAAO,EAAEpE,OAAO,EAAEqE,MAAM,EAAE;IACrF,IAAIC,SAAS,GAAGnQ,OAAO,CAACsM,YAAY,CAAC2D,OAAO,EAAEC,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC1N,KAAK,CAAC;IACzE,OAAO5C,MAAM,CAACwQ,OAAO,CAACtH,MAAM,CAAC,GAAG9I,OAAO,CAACqM,cAAc,CAACvD,MAAM,EAAEqH,SAAS,EAAEtE,OAAO,CAAC,GAAG7L,OAAO,CAACqQ,kBAAkB,CAACvH,MAAM,EAAEqH,SAAS,EAAEtE,OAAO,CAAC;EAC7I,CAAC,CAAC,CAAC;;EAGHzK,cAAc,CAACU,SAAS,CAAC+M,iBAAiB,GAAG,UAAUpN,IAAI,EAAE6O,KAAK,EAAE;IAClEA,KAAK,IAAIA,KAAK,CAAChF,MAAM,IAAI,IAAI,CAACpJ,GAAG,CAAC+G,cAAc,CAAC;MAC/CxH,IAAI,EAAEA,IAAI;MACV6O,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;;EAGElP,cAAc,CAACU,SAAS,CAACyO,OAAO,GAAG,YAAY;IAC7C,IAAI,CAACtC,yBAAyB,CAAC,CAAC;IAEhC,IAAI,CAAC1E,uBAAuB,CAAC,CAAC;EAChC,CAAC;EACD;AACF;AACA;;EAGEnI,cAAc,CAACU,SAAS,CAAC0O,MAAM,GAAG,YAAY;IAC5C,IAAI,CAACvC,yBAAyB,CAAC,CAAC;IAEhC,IAAI,CAAC1E,uBAAuB,CAAC,CAAC;EAChC,CAAC;EAEDnI,cAAc,CAACK,IAAI,GAAG,sBAAsB;EAC5C,OAAOL,cAAc;AACvB,CAAC,CAACrB,aAAa,CAAC;AAEhB,SAASkF,aAAaA,CAACwL,MAAM,EAAEhK,MAAM,EAAEJ,OAAO,EAAEC,SAAS,EAAE;EACzD,OAAO,IAAItG,OAAO,CAAC0Q,OAAO,CAAC;IACzBlL,KAAK,EAAE;MACLiL,MAAM,EAAEA;IACV,CAAC;IACD9J,SAAS,EAAE,CAAC,CAACN,OAAO;IACpBI,MAAM,EAAEA,MAAM;IACdG,KAAK,EAAEP,OAAO;IACdS,WAAW,EAAE,SAAAA,CAAUC,CAAC,EAAE;MACxB;MACAjH,SAAS,CAACkH,IAAI,CAACD,CAAC,CAACE,KAAK,CAAC;IACzB,CAAC;IACDJ,SAAS,EAAEP;EACb,CAAC,CAAC;AACJ;AAEA,SAAS8H,oBAAoBA,CAACpM,cAAc,EAAE4H,UAAU,EAAEE,UAAU,EAAE;EACpE,IAAI8C,iBAAiB,GAAG1L,eAAe,GAAG,CAAC;EAC3C,IAAIyP,iBAAiB,GAAG3O,cAAc,CAACY,GAAG,CAAC,mBAAmB,CAAC;EAE/D,IAAI+N,iBAAiB,EAAE;IACrB/D,iBAAiB,GAAGjM,SAAS,CAACgQ,iBAAiB,EAAE/G,UAAU,EAAEE,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC;EACpF;EAEA,OAAO8C,iBAAiB;AAC1B;AAEA,SAASpD,oBAAoBA,CAACxH,cAAc,EAAE;EAC5C,IAAI4O,iBAAiB,GAAG5O,cAAc,CAACY,GAAG,CAAC,mBAAmB,CAAC;EAC/D,OAAO,CAAC,EAAEgO,iBAAiB,IAAI,IAAI,GAAG5O,cAAc,CAACY,GAAG,CAAC,UAAU,CAAC,GAAGgO,iBAAiB,CAAC;AAC3F;AAEA,SAASzL,SAASA,CAACnB,MAAM,EAAE;EACzB,OAAOA,MAAM,KAAK,UAAU,GAAG,WAAW,GAAG,WAAW;AAC1D;AAEA,eAAe5C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}