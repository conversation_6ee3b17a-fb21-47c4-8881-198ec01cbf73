{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\"; // TODO Optimize on polar\n\nimport * as colorUtil from 'zrender/lib/tool/color.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as numberUtil from '../../util/number.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, setStatesStylesFromModel } from '../../util/states.js';\nimport * as markerHelper from './markerHelper.js';\nimport MarkerView from './MarkerView.js';\nimport { retrieve, mergeAll, map, curry, filter, extend, isString } from 'zrender/lib/core/util.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport MarkerModel from './MarkerModel.js';\nimport { makeInner } from '../../util/model.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nvar inner = makeInner();\nvar markAreaTransform = function (seriesModel, coordSys, maModel, item) {\n  var lt = markerHelper.dataTransform(seriesModel, item[0]);\n  var rb = markerHelper.dataTransform(seriesModel, item[1]); // FIXME make sure lt is less than rb\n\n  var ltCoord = lt.coord;\n  var rbCoord = rb.coord;\n  ltCoord[0] = retrieve(ltCoord[0], -Infinity);\n  ltCoord[1] = retrieve(ltCoord[1], -Infinity);\n  rbCoord[0] = retrieve(rbCoord[0], Infinity);\n  rbCoord[1] = retrieve(rbCoord[1], Infinity); // Merge option into one\n\n  var result = mergeAll([{}, lt, rb]);\n  result.coord = [lt.coord, rb.coord];\n  result.x0 = lt.x;\n  result.y0 = lt.y;\n  result.x1 = rb.x;\n  result.y1 = rb.y;\n  return result;\n};\nfunction isInifinity(val) {\n  return !isNaN(val) && !isFinite(val);\n} // If a markArea has one dim\n\nfunction ifMarkAreaHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {\n  var otherDimIndex = 1 - dimIndex;\n  return isInifinity(fromCoord[otherDimIndex]) && isInifinity(toCoord[otherDimIndex]);\n}\nfunction markAreaFilter(coordSys, item) {\n  var fromCoord = item.coord[0];\n  var toCoord = item.coord[1];\n  var item0 = {\n    coord: fromCoord,\n    x: item.x0,\n    y: item.y0\n  };\n  var item1 = {\n    coord: toCoord,\n    x: item.x1,\n    y: item.y1\n  };\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    // In case\n    // {\n    //  markArea: {\n    //    data: [{ yAxis: 2 }]\n    //  }\n    // }\n    if (fromCoord && toCoord && (ifMarkAreaHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkAreaHasOnlyDim(0, fromCoord, toCoord, coordSys))) {\n      return true;\n    } //Directly returning true may also do the work,\n    //because markArea will not be shown automatically\n    //when it's not included in coordinate system.\n    //But filtering ahead can avoid keeping rendering markArea\n    //when there are too many of them.\n\n    return markerHelper.zoneFilter(coordSys, item0, item1);\n  }\n  return markerHelper.dataFilter(coordSys, item0) || markerHelper.dataFilter(coordSys, item1);\n} // dims can be ['x0', 'y0'], ['x1', 'y1'], ['x0', 'y1'], ['x1', 'y0']\n\nfunction getSingleMarkerEndPoint(data, idx, dims, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  var itemModel = data.getItemModel(idx);\n  var point;\n  var xPx = numberUtil.parsePercent(itemModel.get(dims[0]), api.getWidth());\n  var yPx = numberUtil.parsePercent(itemModel.get(dims[1]), api.getHeight());\n  if (!isNaN(xPx) && !isNaN(yPx)) {\n    point = [xPx, yPx];\n  } else {\n    // Chart like bar may have there own marker positioning logic\n    if (seriesModel.getMarkerPosition) {\n      // Use the getMarkerPoisition\n      point = seriesModel.getMarkerPosition(data.getValues(dims, idx));\n    } else {\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      var pt = [x, y];\n      coordSys.clampData && coordSys.clampData(pt, pt);\n      point = coordSys.dataToPoint(pt, true);\n    }\n    if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n      // TODO: TYPE ts@4.1 may still infer it as Axis instead of Axis2D. Not sure if it's a bug\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      if (isInifinity(x)) {\n        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[dims[0] === 'x0' ? 0 : 1]);\n      } else if (isInifinity(y)) {\n        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[dims[1] === 'y0' ? 0 : 1]);\n      }\n    } // Use x, y if has any\n\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n  }\n  return point;\n}\nvar dimPermutations = [['x0', 'y0'], ['x1', 'y0'], ['x1', 'y1'], ['x0', 'y1']];\nvar MarkAreaView = /** @class */\nfunction (_super) {\n  __extends(MarkAreaView, _super);\n  function MarkAreaView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkAreaView.type;\n    return _this;\n  }\n  MarkAreaView.prototype.updateTransform = function (markAreaModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var maModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markArea');\n      if (maModel) {\n        var areaData_1 = maModel.getData();\n        areaData_1.each(function (idx) {\n          var points = map(dimPermutations, function (dim) {\n            return getSingleMarkerEndPoint(areaData_1, idx, dim, seriesModel, api);\n          }); // Layout\n\n          areaData_1.setItemLayout(idx, points);\n          var el = areaData_1.getItemGraphicEl(idx);\n          el.setShape('points', points);\n        });\n      }\n    }, this);\n  };\n  MarkAreaView.prototype.renderSeries = function (seriesModel, maModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var areaGroupMap = this.markerGroupMap;\n    var polygonGroup = areaGroupMap.get(seriesId) || areaGroupMap.set(seriesId, {\n      group: new graphic.Group()\n    });\n    this.group.add(polygonGroup.group);\n    this.markKeep(polygonGroup);\n    var areaData = createList(coordSys, seriesModel, maModel); // Line data for tooltip and formatter\n\n    maModel.setData(areaData); // Update visual and layout of line\n\n    areaData.each(function (idx) {\n      // Layout\n      var points = map(dimPermutations, function (dim) {\n        return getSingleMarkerEndPoint(areaData, idx, dim, seriesModel, api);\n      });\n      var xAxisScale = coordSys.getAxis('x').scale;\n      var yAxisScale = coordSys.getAxis('y').scale;\n      var xAxisExtent = xAxisScale.getExtent();\n      var yAxisExtent = yAxisScale.getExtent();\n      var xPointExtent = [xAxisScale.parse(areaData.get('x0', idx)), xAxisScale.parse(areaData.get('x1', idx))];\n      var yPointExtent = [yAxisScale.parse(areaData.get('y0', idx)), yAxisScale.parse(areaData.get('y1', idx))];\n      numberUtil.asc(xPointExtent);\n      numberUtil.asc(yPointExtent);\n      var overlapped = !(xAxisExtent[0] > xPointExtent[1] || xAxisExtent[1] < xPointExtent[0] || yAxisExtent[0] > yPointExtent[1] || yAxisExtent[1] < yPointExtent[0]); // If none of the area is inside coordSys, allClipped is set to be true\n      // in layout so that label will not be displayed. See #12591\n\n      var allClipped = !overlapped;\n      areaData.setItemLayout(idx, {\n        points: points,\n        allClipped: allClipped\n      });\n      var style = areaData.getItemModel(idx).getModel('itemStyle').getItemStyle();\n      var color = getVisualFromData(seriesData, 'color');\n      if (!style.fill) {\n        style.fill = color;\n        if (isString(style.fill)) {\n          style.fill = colorUtil.modifyAlpha(style.fill, 0.4);\n        }\n      }\n      if (!style.stroke) {\n        style.stroke = color;\n      } // Visual\n\n      areaData.setItemVisual(idx, 'style', style);\n    });\n    areaData.diff(inner(polygonGroup).data).add(function (idx) {\n      var layout = areaData.getItemLayout(idx);\n      if (!layout.allClipped) {\n        var polygon = new graphic.Polygon({\n          shape: {\n            points: layout.points\n          }\n        });\n        areaData.setItemGraphicEl(idx, polygon);\n        polygonGroup.group.add(polygon);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(oldIdx);\n      var layout = areaData.getItemLayout(newIdx);\n      if (!layout.allClipped) {\n        if (polygon) {\n          graphic.updateProps(polygon, {\n            shape: {\n              points: layout.points\n            }\n          }, maModel, newIdx);\n        } else {\n          polygon = new graphic.Polygon({\n            shape: {\n              points: layout.points\n            }\n          });\n        }\n        areaData.setItemGraphicEl(newIdx, polygon);\n        polygonGroup.group.add(polygon);\n      } else if (polygon) {\n        polygonGroup.group.remove(polygon);\n      }\n    }).remove(function (idx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(idx);\n      polygonGroup.group.remove(polygon);\n    }).execute();\n    areaData.eachItemGraphicEl(function (polygon, idx) {\n      var itemModel = areaData.getItemModel(idx);\n      var style = areaData.getItemVisual(idx, 'style');\n      polygon.useStyle(areaData.getItemVisual(idx, 'style'));\n      setLabelStyle(polygon, getLabelStatesModels(itemModel), {\n        labelFetcher: maModel,\n        labelDataIndex: idx,\n        defaultText: areaData.getName(idx) || '',\n        inheritColor: isString(style.fill) ? colorUtil.modifyAlpha(style.fill, 1) : '#000'\n      });\n      setStatesStylesFromModel(polygon, itemModel);\n      toggleHoverEmphasis(polygon, null, null, itemModel.get(['emphasis', 'disabled']));\n      getECData(polygon).dataModel = maModel;\n    });\n    inner(polygonGroup).data = areaData;\n    polygonGroup.group.silent = maModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkAreaView.type = 'markArea';\n  return MarkAreaView;\n}(MarkerView);\nfunction createList(coordSys, seriesModel, maModel) {\n  var areaData;\n  var dataDims;\n  var dims = ['x0', 'y0', 'x1', 'y1'];\n  if (coordSys) {\n    var coordDimsInfos_1 = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var data = seriesModel.getData();\n      var info = data.getDimensionInfo(data.mapDimension(coordDim)) || {}; // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n    dataDims = map(dims, function (dim, idx) {\n      return {\n        name: dim,\n        type: coordDimsInfos_1[idx % 2].type\n      };\n    });\n    areaData = new SeriesData(dataDims, maModel);\n  } else {\n    dataDims = [{\n      name: 'value',\n      type: 'float'\n    }];\n    areaData = new SeriesData(dataDims, maModel);\n  }\n  var optData = map(maModel.get('data'), curry(markAreaTransform, seriesModel, coordSys, maModel));\n  if (coordSys) {\n    optData = filter(optData, curry(markAreaFilter, coordSys));\n  }\n  var dimValueGetter = coordSys ? function (item, dimName, dataIndex, dimIndex) {\n    // TODO should convert to ParsedValue?\n    var rawVal = item.coord[Math.floor(dimIndex / 2)][dimIndex % 2];\n    return parseDataValue(rawVal, dataDims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dataDims[dimIndex]);\n  };\n  areaData.initData(optData, null, dimValueGetter);\n  areaData.hasItemOption = true;\n  return areaData;\n}\nexport default MarkAreaView;", "map": {"version": 3, "names": ["__extends", "colorUtil", "SeriesData", "numberUtil", "graphic", "toggleHoverEmphasis", "setStatesStylesFromModel", "marker<PERSON>elper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retrieve", "mergeAll", "map", "curry", "filter", "extend", "isString", "isCoordinateSystemType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "makeInner", "getVisualFromData", "setLabelStyle", "getLabelStatesModels", "getECData", "parseDataValue", "inner", "markAreaTransform", "seriesModel", "coordSys", "maModel", "item", "lt", "dataTransform", "rb", "ltCoord", "coord", "rbCoord", "Infinity", "result", "x0", "x", "y0", "y", "x1", "y1", "isInifinity", "val", "isNaN", "isFinite", "ifMarkAreaHasOnlyDim", "dimIndex", "fromCoord", "toCoord", "otherDimIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item0", "item1", "zoneFilter", "dataFilter", "getSingleMarkerEndPoint", "data", "idx", "dims", "api", "coordinateSystem", "itemModel", "getItemModel", "point", "xPx", "parsePercent", "get", "getWidth", "yPx", "getHeight", "getMarkerPosition", "getV<PERSON>ues", "pt", "clampData", "dataToPoint", "xAxis", "getAxis", "yAxis", "toGlobalCoord", "getExtent", "dimPermutations", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "_this", "apply", "arguments", "type", "prototype", "updateTransform", "markAreaModel", "ecModel", "eachSeries", "getMarkerModelFromSeries", "areaData_1", "getData", "each", "points", "dim", "setItemLayout", "el", "getItemGraphicEl", "setShape", "renderSeries", "seriesId", "id", "seriesData", "areaGroupMap", "markerGroupMap", "polygonGroup", "set", "group", "Group", "add", "<PERSON><PERSON><PERSON>", "areaData", "createList", "setData", "xAxisScale", "scale", "yAxisScale", "xAxisExtent", "yAxisExtent", "xPointExtent", "parse", "yPointExtent", "asc", "overlapped", "allClipped", "style", "getModel", "getItemStyle", "color", "fill", "modifyAlpha", "stroke", "setItemVisual", "diff", "layout", "getItemLayout", "polygon", "Polygon", "shape", "setItemGraphicEl", "update", "newIdx", "oldIdx", "updateProps", "remove", "execute", "eachItemGraphicEl", "getItemVisual", "useStyle", "labelFetcher", "labelDataIndex", "defaultText", "getName", "inheritColor", "dataModel", "silent", "dataDims", "coordDimsInfos_1", "dimensions", "coordDim", "info", "getDimensionInfo", "mapDimension", "name", "ordinalMeta", "optData", "dimValueGetter", "dimName", "dataIndex", "rawVal", "Math", "floor", "value", "initData", "hasItemOption"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/echarts/lib/component/marker/MarkAreaView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\"; // TODO Optimize on polar\n\nimport * as colorUtil from 'zrender/lib/tool/color.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as numberUtil from '../../util/number.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, setStatesStylesFromModel } from '../../util/states.js';\nimport * as markerHelper from './markerHelper.js';\nimport MarkerView from './MarkerView.js';\nimport { retrieve, mergeAll, map, curry, filter, extend, isString } from 'zrender/lib/core/util.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport MarkerModel from './MarkerModel.js';\nimport { makeInner } from '../../util/model.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nvar inner = makeInner();\n\nvar markAreaTransform = function (seriesModel, coordSys, maModel, item) {\n  var lt = markerHelper.dataTransform(seriesModel, item[0]);\n  var rb = markerHelper.dataTransform(seriesModel, item[1]); // FIXME make sure lt is less than rb\n\n  var ltCoord = lt.coord;\n  var rbCoord = rb.coord;\n  ltCoord[0] = retrieve(ltCoord[0], -Infinity);\n  ltCoord[1] = retrieve(ltCoord[1], -Infinity);\n  rbCoord[0] = retrieve(rbCoord[0], Infinity);\n  rbCoord[1] = retrieve(rbCoord[1], Infinity); // Merge option into one\n\n  var result = mergeAll([{}, lt, rb]);\n  result.coord = [lt.coord, rb.coord];\n  result.x0 = lt.x;\n  result.y0 = lt.y;\n  result.x1 = rb.x;\n  result.y1 = rb.y;\n  return result;\n};\n\nfunction isInifinity(val) {\n  return !isNaN(val) && !isFinite(val);\n} // If a markArea has one dim\n\n\nfunction ifMarkAreaHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {\n  var otherDimIndex = 1 - dimIndex;\n  return isInifinity(fromCoord[otherDimIndex]) && isInifinity(toCoord[otherDimIndex]);\n}\n\nfunction markAreaFilter(coordSys, item) {\n  var fromCoord = item.coord[0];\n  var toCoord = item.coord[1];\n  var item0 = {\n    coord: fromCoord,\n    x: item.x0,\n    y: item.y0\n  };\n  var item1 = {\n    coord: toCoord,\n    x: item.x1,\n    y: item.y1\n  };\n\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    // In case\n    // {\n    //  markArea: {\n    //    data: [{ yAxis: 2 }]\n    //  }\n    // }\n    if (fromCoord && toCoord && (ifMarkAreaHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkAreaHasOnlyDim(0, fromCoord, toCoord, coordSys))) {\n      return true;\n    } //Directly returning true may also do the work,\n    //because markArea will not be shown automatically\n    //when it's not included in coordinate system.\n    //But filtering ahead can avoid keeping rendering markArea\n    //when there are too many of them.\n\n\n    return markerHelper.zoneFilter(coordSys, item0, item1);\n  }\n\n  return markerHelper.dataFilter(coordSys, item0) || markerHelper.dataFilter(coordSys, item1);\n} // dims can be ['x0', 'y0'], ['x1', 'y1'], ['x0', 'y1'], ['x1', 'y0']\n\n\nfunction getSingleMarkerEndPoint(data, idx, dims, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  var itemModel = data.getItemModel(idx);\n  var point;\n  var xPx = numberUtil.parsePercent(itemModel.get(dims[0]), api.getWidth());\n  var yPx = numberUtil.parsePercent(itemModel.get(dims[1]), api.getHeight());\n\n  if (!isNaN(xPx) && !isNaN(yPx)) {\n    point = [xPx, yPx];\n  } else {\n    // Chart like bar may have there own marker positioning logic\n    if (seriesModel.getMarkerPosition) {\n      // Use the getMarkerPoisition\n      point = seriesModel.getMarkerPosition(data.getValues(dims, idx));\n    } else {\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      var pt = [x, y];\n      coordSys.clampData && coordSys.clampData(pt, pt);\n      point = coordSys.dataToPoint(pt, true);\n    }\n\n    if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n      // TODO: TYPE ts@4.1 may still infer it as Axis instead of Axis2D. Not sure if it's a bug\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n\n      if (isInifinity(x)) {\n        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[dims[0] === 'x0' ? 0 : 1]);\n      } else if (isInifinity(y)) {\n        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[dims[1] === 'y0' ? 0 : 1]);\n      }\n    } // Use x, y if has any\n\n\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n  }\n\n  return point;\n}\n\nvar dimPermutations = [['x0', 'y0'], ['x1', 'y0'], ['x1', 'y1'], ['x0', 'y1']];\n\nvar MarkAreaView =\n/** @class */\nfunction (_super) {\n  __extends(MarkAreaView, _super);\n\n  function MarkAreaView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = MarkAreaView.type;\n    return _this;\n  }\n\n  MarkAreaView.prototype.updateTransform = function (markAreaModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var maModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markArea');\n\n      if (maModel) {\n        var areaData_1 = maModel.getData();\n        areaData_1.each(function (idx) {\n          var points = map(dimPermutations, function (dim) {\n            return getSingleMarkerEndPoint(areaData_1, idx, dim, seriesModel, api);\n          }); // Layout\n\n          areaData_1.setItemLayout(idx, points);\n          var el = areaData_1.getItemGraphicEl(idx);\n          el.setShape('points', points);\n        });\n      }\n    }, this);\n  };\n\n  MarkAreaView.prototype.renderSeries = function (seriesModel, maModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var areaGroupMap = this.markerGroupMap;\n    var polygonGroup = areaGroupMap.get(seriesId) || areaGroupMap.set(seriesId, {\n      group: new graphic.Group()\n    });\n    this.group.add(polygonGroup.group);\n    this.markKeep(polygonGroup);\n    var areaData = createList(coordSys, seriesModel, maModel); // Line data for tooltip and formatter\n\n    maModel.setData(areaData); // Update visual and layout of line\n\n    areaData.each(function (idx) {\n      // Layout\n      var points = map(dimPermutations, function (dim) {\n        return getSingleMarkerEndPoint(areaData, idx, dim, seriesModel, api);\n      });\n      var xAxisScale = coordSys.getAxis('x').scale;\n      var yAxisScale = coordSys.getAxis('y').scale;\n      var xAxisExtent = xAxisScale.getExtent();\n      var yAxisExtent = yAxisScale.getExtent();\n      var xPointExtent = [xAxisScale.parse(areaData.get('x0', idx)), xAxisScale.parse(areaData.get('x1', idx))];\n      var yPointExtent = [yAxisScale.parse(areaData.get('y0', idx)), yAxisScale.parse(areaData.get('y1', idx))];\n      numberUtil.asc(xPointExtent);\n      numberUtil.asc(yPointExtent);\n      var overlapped = !(xAxisExtent[0] > xPointExtent[1] || xAxisExtent[1] < xPointExtent[0] || yAxisExtent[0] > yPointExtent[1] || yAxisExtent[1] < yPointExtent[0]); // If none of the area is inside coordSys, allClipped is set to be true\n      // in layout so that label will not be displayed. See #12591\n\n      var allClipped = !overlapped;\n      areaData.setItemLayout(idx, {\n        points: points,\n        allClipped: allClipped\n      });\n      var style = areaData.getItemModel(idx).getModel('itemStyle').getItemStyle();\n      var color = getVisualFromData(seriesData, 'color');\n\n      if (!style.fill) {\n        style.fill = color;\n\n        if (isString(style.fill)) {\n          style.fill = colorUtil.modifyAlpha(style.fill, 0.4);\n        }\n      }\n\n      if (!style.stroke) {\n        style.stroke = color;\n      } // Visual\n\n\n      areaData.setItemVisual(idx, 'style', style);\n    });\n    areaData.diff(inner(polygonGroup).data).add(function (idx) {\n      var layout = areaData.getItemLayout(idx);\n\n      if (!layout.allClipped) {\n        var polygon = new graphic.Polygon({\n          shape: {\n            points: layout.points\n          }\n        });\n        areaData.setItemGraphicEl(idx, polygon);\n        polygonGroup.group.add(polygon);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(oldIdx);\n      var layout = areaData.getItemLayout(newIdx);\n\n      if (!layout.allClipped) {\n        if (polygon) {\n          graphic.updateProps(polygon, {\n            shape: {\n              points: layout.points\n            }\n          }, maModel, newIdx);\n        } else {\n          polygon = new graphic.Polygon({\n            shape: {\n              points: layout.points\n            }\n          });\n        }\n\n        areaData.setItemGraphicEl(newIdx, polygon);\n        polygonGroup.group.add(polygon);\n      } else if (polygon) {\n        polygonGroup.group.remove(polygon);\n      }\n    }).remove(function (idx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(idx);\n      polygonGroup.group.remove(polygon);\n    }).execute();\n    areaData.eachItemGraphicEl(function (polygon, idx) {\n      var itemModel = areaData.getItemModel(idx);\n      var style = areaData.getItemVisual(idx, 'style');\n      polygon.useStyle(areaData.getItemVisual(idx, 'style'));\n      setLabelStyle(polygon, getLabelStatesModels(itemModel), {\n        labelFetcher: maModel,\n        labelDataIndex: idx,\n        defaultText: areaData.getName(idx) || '',\n        inheritColor: isString(style.fill) ? colorUtil.modifyAlpha(style.fill, 1) : '#000'\n      });\n      setStatesStylesFromModel(polygon, itemModel);\n      toggleHoverEmphasis(polygon, null, null, itemModel.get(['emphasis', 'disabled']));\n      getECData(polygon).dataModel = maModel;\n    });\n    inner(polygonGroup).data = areaData;\n    polygonGroup.group.silent = maModel.get('silent') || seriesModel.get('silent');\n  };\n\n  MarkAreaView.type = 'markArea';\n  return MarkAreaView;\n}(MarkerView);\n\nfunction createList(coordSys, seriesModel, maModel) {\n  var areaData;\n  var dataDims;\n  var dims = ['x0', 'y0', 'x1', 'y1'];\n\n  if (coordSys) {\n    var coordDimsInfos_1 = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var data = seriesModel.getData();\n      var info = data.getDimensionInfo(data.mapDimension(coordDim)) || {}; // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n    dataDims = map(dims, function (dim, idx) {\n      return {\n        name: dim,\n        type: coordDimsInfos_1[idx % 2].type\n      };\n    });\n    areaData = new SeriesData(dataDims, maModel);\n  } else {\n    dataDims = [{\n      name: 'value',\n      type: 'float'\n    }];\n    areaData = new SeriesData(dataDims, maModel);\n  }\n\n  var optData = map(maModel.get('data'), curry(markAreaTransform, seriesModel, coordSys, maModel));\n\n  if (coordSys) {\n    optData = filter(optData, curry(markAreaFilter, coordSys));\n  }\n\n  var dimValueGetter = coordSys ? function (item, dimName, dataIndex, dimIndex) {\n    // TODO should convert to ParsedValue?\n    var rawVal = item.coord[Math.floor(dimIndex / 2)][dimIndex % 2];\n    return parseDataValue(rawVal, dataDims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dataDims[dimIndex]);\n  };\n  areaData.initData(optData, null, dimValueGetter);\n  areaData.hasItemOption = true;\n  return areaData;\n}\n\nexport default MarkAreaView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO,CAAC,CAAC;;AAEnC,OAAO,KAAKC,SAAS,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,EAAEC,wBAAwB,QAAQ,sBAAsB;AACpF,OAAO,KAAKC,YAAY,MAAM,mBAAmB;AACjD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,0BAA0B;AACnG,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,cAAc,QAAQ,sCAAsC;AACrE,IAAIC,KAAK,GAAGN,SAAS,CAAC,CAAC;AAEvB,IAAIO,iBAAiB,GAAG,SAAAA,CAAUC,WAAW,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAE;EACtE,IAAIC,EAAE,GAAGvB,YAAY,CAACwB,aAAa,CAACL,WAAW,EAAEG,IAAI,CAAC,CAAC,CAAC,CAAC;EACzD,IAAIG,EAAE,GAAGzB,YAAY,CAACwB,aAAa,CAACL,WAAW,EAAEG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE3D,IAAII,OAAO,GAAGH,EAAE,CAACI,KAAK;EACtB,IAAIC,OAAO,GAAGH,EAAE,CAACE,KAAK;EACtBD,OAAO,CAAC,CAAC,CAAC,GAAGxB,QAAQ,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAE,CAACG,QAAQ,CAAC;EAC5CH,OAAO,CAAC,CAAC,CAAC,GAAGxB,QAAQ,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAE,CAACG,QAAQ,CAAC;EAC5CD,OAAO,CAAC,CAAC,CAAC,GAAG1B,QAAQ,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC;EAC3CD,OAAO,CAAC,CAAC,CAAC,GAAG1B,QAAQ,CAAC0B,OAAO,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAAC;;EAE7C,IAAIC,MAAM,GAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEoB,EAAE,EAAEE,EAAE,CAAC,CAAC;EACnCK,MAAM,CAACH,KAAK,GAAG,CAACJ,EAAE,CAACI,KAAK,EAAEF,EAAE,CAACE,KAAK,CAAC;EACnCG,MAAM,CAACC,EAAE,GAAGR,EAAE,CAACS,CAAC;EAChBF,MAAM,CAACG,EAAE,GAAGV,EAAE,CAACW,CAAC;EAChBJ,MAAM,CAACK,EAAE,GAAGV,EAAE,CAACO,CAAC;EAChBF,MAAM,CAACM,EAAE,GAAGX,EAAE,CAACS,CAAC;EAChB,OAAOJ,MAAM;AACf,CAAC;AAED,SAASO,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAO,CAACC,KAAK,CAACD,GAAG,CAAC,IAAI,CAACE,QAAQ,CAACF,GAAG,CAAC;AACtC,CAAC,CAAC;;AAGF,SAASG,oBAAoBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAExB,QAAQ,EAAE;EACpE,IAAIyB,aAAa,GAAG,CAAC,GAAGH,QAAQ;EAChC,OAAOL,WAAW,CAACM,SAAS,CAACE,aAAa,CAAC,CAAC,IAAIR,WAAW,CAACO,OAAO,CAACC,aAAa,CAAC,CAAC;AACrF;AAEA,SAASC,cAAcA,CAAC1B,QAAQ,EAAEE,IAAI,EAAE;EACtC,IAAIqB,SAAS,GAAGrB,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC;EAC7B,IAAIiB,OAAO,GAAGtB,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC;EAC3B,IAAIoB,KAAK,GAAG;IACVpB,KAAK,EAAEgB,SAAS;IAChBX,CAAC,EAAEV,IAAI,CAACS,EAAE;IACVG,CAAC,EAAEZ,IAAI,CAACW;EACV,CAAC;EACD,IAAIe,KAAK,GAAG;IACVrB,KAAK,EAAEiB,OAAO;IACdZ,CAAC,EAAEV,IAAI,CAACa,EAAE;IACVD,CAAC,EAAEZ,IAAI,CAACc;EACV,CAAC;EAED,IAAI3B,sBAAsB,CAACW,QAAQ,EAAE,aAAa,CAAC,EAAE;IACnD;IACA;IACA;IACA;IACA;IACA;IACA,IAAIuB,SAAS,IAAIC,OAAO,KAAKH,oBAAoB,CAAC,CAAC,EAAEE,SAAS,EAAEC,OAAO,EAAExB,QAAQ,CAAC,IAAIqB,oBAAoB,CAAC,CAAC,EAAEE,SAAS,EAAEC,OAAO,EAAExB,QAAQ,CAAC,CAAC,EAAE;MAC5I,OAAO,IAAI;IACb,CAAC,CAAC;IACF;IACA;IACA;IACA;;IAGA,OAAOpB,YAAY,CAACiD,UAAU,CAAC7B,QAAQ,EAAE2B,KAAK,EAAEC,KAAK,CAAC;EACxD;EAEA,OAAOhD,YAAY,CAACkD,UAAU,CAAC9B,QAAQ,EAAE2B,KAAK,CAAC,IAAI/C,YAAY,CAACkD,UAAU,CAAC9B,QAAQ,EAAE4B,KAAK,CAAC;AAC7F,CAAC,CAAC;;AAGF,SAASG,uBAAuBA,CAACC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEnC,WAAW,EAAEoC,GAAG,EAAE;EAClE,IAAInC,QAAQ,GAAGD,WAAW,CAACqC,gBAAgB;EAC3C,IAAIC,SAAS,GAAGL,IAAI,CAACM,YAAY,CAACL,GAAG,CAAC;EACtC,IAAIM,KAAK;EACT,IAAIC,GAAG,GAAGhE,UAAU,CAACiE,YAAY,CAACJ,SAAS,CAACK,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAG,CAACQ,QAAQ,CAAC,CAAC,CAAC;EACzE,IAAIC,GAAG,GAAGpE,UAAU,CAACiE,YAAY,CAACJ,SAAS,CAACK,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEC,GAAG,CAACU,SAAS,CAAC,CAAC,CAAC;EAE1E,IAAI,CAAC1B,KAAK,CAACqB,GAAG,CAAC,IAAI,CAACrB,KAAK,CAACyB,GAAG,CAAC,EAAE;IAC9BL,KAAK,GAAG,CAACC,GAAG,EAAEI,GAAG,CAAC;EACpB,CAAC,MAAM;IACL;IACA,IAAI7C,WAAW,CAAC+C,iBAAiB,EAAE;MACjC;MACAP,KAAK,GAAGxC,WAAW,CAAC+C,iBAAiB,CAACd,IAAI,CAACe,SAAS,CAACb,IAAI,EAAED,GAAG,CAAC,CAAC;IAClE,CAAC,MAAM;MACL,IAAIrB,CAAC,GAAGoB,IAAI,CAACU,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC;MAC9B,IAAInB,CAAC,GAAGkB,IAAI,CAACU,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC;MAC9B,IAAIe,EAAE,GAAG,CAACpC,CAAC,EAAEE,CAAC,CAAC;MACfd,QAAQ,CAACiD,SAAS,IAAIjD,QAAQ,CAACiD,SAAS,CAACD,EAAE,EAAEA,EAAE,CAAC;MAChDT,KAAK,GAAGvC,QAAQ,CAACkD,WAAW,CAACF,EAAE,EAAE,IAAI,CAAC;IACxC;IAEA,IAAI3D,sBAAsB,CAACW,QAAQ,EAAE,aAAa,CAAC,EAAE;MACnD;MACA,IAAImD,KAAK,GAAGnD,QAAQ,CAACoD,OAAO,CAAC,GAAG,CAAC;MACjC,IAAIC,KAAK,GAAGrD,QAAQ,CAACoD,OAAO,CAAC,GAAG,CAAC;MACjC,IAAIxC,CAAC,GAAGoB,IAAI,CAACU,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC;MAC9B,IAAInB,CAAC,GAAGkB,IAAI,CAACU,GAAG,CAACR,IAAI,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC;MAE9B,IAAIhB,WAAW,CAACL,CAAC,CAAC,EAAE;QAClB2B,KAAK,CAAC,CAAC,CAAC,GAAGY,KAAK,CAACG,aAAa,CAACH,KAAK,CAACI,SAAS,CAAC,CAAC,CAACrB,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7E,CAAC,MAAM,IAAIjB,WAAW,CAACH,CAAC,CAAC,EAAE;QACzByB,KAAK,CAAC,CAAC,CAAC,GAAGc,KAAK,CAACC,aAAa,CAACD,KAAK,CAACE,SAAS,CAAC,CAAC,CAACrB,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7E;IACF,CAAC,CAAC;;IAGF,IAAI,CAACf,KAAK,CAACqB,GAAG,CAAC,EAAE;MACfD,KAAK,CAAC,CAAC,CAAC,GAAGC,GAAG;IAChB;IAEA,IAAI,CAACrB,KAAK,CAACyB,GAAG,CAAC,EAAE;MACfL,KAAK,CAAC,CAAC,CAAC,GAAGK,GAAG;IAChB;EACF;EAEA,OAAOL,KAAK;AACd;AAEA,IAAIiB,eAAe,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAE9E,IAAIC,YAAY,GAChB;AACA,UAAUC,MAAM,EAAE;EAChBrF,SAAS,CAACoF,YAAY,EAAEC,MAAM,CAAC;EAE/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,YAAY,CAACK,IAAI;IAC9B,OAAOH,KAAK;EACd;EAEAF,YAAY,CAACM,SAAS,CAACC,eAAe,GAAG,UAAUC,aAAa,EAAEC,OAAO,EAAE/B,GAAG,EAAE;IAC9E+B,OAAO,CAACC,UAAU,CAAC,UAAUpE,WAAW,EAAE;MACxC,IAAIE,OAAO,GAAGX,WAAW,CAAC8E,wBAAwB,CAACrE,WAAW,EAAE,UAAU,CAAC;MAE3E,IAAIE,OAAO,EAAE;QACX,IAAIoE,UAAU,GAAGpE,OAAO,CAACqE,OAAO,CAAC,CAAC;QAClCD,UAAU,CAACE,IAAI,CAAC,UAAUtC,GAAG,EAAE;UAC7B,IAAIuC,MAAM,GAAGxF,GAAG,CAACwE,eAAe,EAAE,UAAUiB,GAAG,EAAE;YAC/C,OAAO1C,uBAAuB,CAACsC,UAAU,EAAEpC,GAAG,EAAEwC,GAAG,EAAE1E,WAAW,EAAEoC,GAAG,CAAC;UACxE,CAAC,CAAC,CAAC,CAAC;;UAEJkC,UAAU,CAACK,aAAa,CAACzC,GAAG,EAAEuC,MAAM,CAAC;UACrC,IAAIG,EAAE,GAAGN,UAAU,CAACO,gBAAgB,CAAC3C,GAAG,CAAC;UACzC0C,EAAE,CAACE,QAAQ,CAAC,QAAQ,EAAEL,MAAM,CAAC;QAC/B,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAEDf,YAAY,CAACM,SAAS,CAACe,YAAY,GAAG,UAAU/E,WAAW,EAAEE,OAAO,EAAEiE,OAAO,EAAE/B,GAAG,EAAE;IAClF,IAAInC,QAAQ,GAAGD,WAAW,CAACqC,gBAAgB;IAC3C,IAAI2C,QAAQ,GAAGhF,WAAW,CAACiF,EAAE;IAC7B,IAAIC,UAAU,GAAGlF,WAAW,CAACuE,OAAO,CAAC,CAAC;IACtC,IAAIY,YAAY,GAAG,IAAI,CAACC,cAAc;IACtC,IAAIC,YAAY,GAAGF,YAAY,CAACxC,GAAG,CAACqC,QAAQ,CAAC,IAAIG,YAAY,CAACG,GAAG,CAACN,QAAQ,EAAE;MAC1EO,KAAK,EAAE,IAAI7G,OAAO,CAAC8G,KAAK,CAAC;IAC3B,CAAC,CAAC;IACF,IAAI,CAACD,KAAK,CAACE,GAAG,CAACJ,YAAY,CAACE,KAAK,CAAC;IAClC,IAAI,CAACG,QAAQ,CAACL,YAAY,CAAC;IAC3B,IAAIM,QAAQ,GAAGC,UAAU,CAAC3F,QAAQ,EAAED,WAAW,EAAEE,OAAO,CAAC,CAAC,CAAC;;IAE3DA,OAAO,CAAC2F,OAAO,CAACF,QAAQ,CAAC,CAAC,CAAC;;IAE3BA,QAAQ,CAACnB,IAAI,CAAC,UAAUtC,GAAG,EAAE;MAC3B;MACA,IAAIuC,MAAM,GAAGxF,GAAG,CAACwE,eAAe,EAAE,UAAUiB,GAAG,EAAE;QAC/C,OAAO1C,uBAAuB,CAAC2D,QAAQ,EAAEzD,GAAG,EAAEwC,GAAG,EAAE1E,WAAW,EAAEoC,GAAG,CAAC;MACtE,CAAC,CAAC;MACF,IAAI0D,UAAU,GAAG7F,QAAQ,CAACoD,OAAO,CAAC,GAAG,CAAC,CAAC0C,KAAK;MAC5C,IAAIC,UAAU,GAAG/F,QAAQ,CAACoD,OAAO,CAAC,GAAG,CAAC,CAAC0C,KAAK;MAC5C,IAAIE,WAAW,GAAGH,UAAU,CAACtC,SAAS,CAAC,CAAC;MACxC,IAAI0C,WAAW,GAAGF,UAAU,CAACxC,SAAS,CAAC,CAAC;MACxC,IAAI2C,YAAY,GAAG,CAACL,UAAU,CAACM,KAAK,CAACT,QAAQ,CAAChD,GAAG,CAAC,IAAI,EAAET,GAAG,CAAC,CAAC,EAAE4D,UAAU,CAACM,KAAK,CAACT,QAAQ,CAAChD,GAAG,CAAC,IAAI,EAAET,GAAG,CAAC,CAAC,CAAC;MACzG,IAAImE,YAAY,GAAG,CAACL,UAAU,CAACI,KAAK,CAACT,QAAQ,CAAChD,GAAG,CAAC,IAAI,EAAET,GAAG,CAAC,CAAC,EAAE8D,UAAU,CAACI,KAAK,CAACT,QAAQ,CAAChD,GAAG,CAAC,IAAI,EAAET,GAAG,CAAC,CAAC,CAAC;MACzGzD,UAAU,CAAC6H,GAAG,CAACH,YAAY,CAAC;MAC5B1H,UAAU,CAAC6H,GAAG,CAACD,YAAY,CAAC;MAC5B,IAAIE,UAAU,GAAG,EAAEN,WAAW,CAAC,CAAC,CAAC,GAAGE,YAAY,CAAC,CAAC,CAAC,IAAIF,WAAW,CAAC,CAAC,CAAC,GAAGE,YAAY,CAAC,CAAC,CAAC,IAAID,WAAW,CAAC,CAAC,CAAC,GAAGG,YAAY,CAAC,CAAC,CAAC,IAAIH,WAAW,CAAC,CAAC,CAAC,GAAGG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClK;;MAEA,IAAIG,UAAU,GAAG,CAACD,UAAU;MAC5BZ,QAAQ,CAAChB,aAAa,CAACzC,GAAG,EAAE;QAC1BuC,MAAM,EAAEA,MAAM;QACd+B,UAAU,EAAEA;MACd,CAAC,CAAC;MACF,IAAIC,KAAK,GAAGd,QAAQ,CAACpD,YAAY,CAACL,GAAG,CAAC,CAACwE,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;MAC3E,IAAIC,KAAK,GAAGnH,iBAAiB,CAACyF,UAAU,EAAE,OAAO,CAAC;MAElD,IAAI,CAACuB,KAAK,CAACI,IAAI,EAAE;QACfJ,KAAK,CAACI,IAAI,GAAGD,KAAK;QAElB,IAAIvH,QAAQ,CAACoH,KAAK,CAACI,IAAI,CAAC,EAAE;UACxBJ,KAAK,CAACI,IAAI,GAAGtI,SAAS,CAACuI,WAAW,CAACL,KAAK,CAACI,IAAI,EAAE,GAAG,CAAC;QACrD;MACF;MAEA,IAAI,CAACJ,KAAK,CAACM,MAAM,EAAE;QACjBN,KAAK,CAACM,MAAM,GAAGH,KAAK;MACtB,CAAC,CAAC;;MAGFjB,QAAQ,CAACqB,aAAa,CAAC9E,GAAG,EAAE,OAAO,EAAEuE,KAAK,CAAC;IAC7C,CAAC,CAAC;IACFd,QAAQ,CAACsB,IAAI,CAACnH,KAAK,CAACuF,YAAY,CAAC,CAACpD,IAAI,CAAC,CAACwD,GAAG,CAAC,UAAUvD,GAAG,EAAE;MACzD,IAAIgF,MAAM,GAAGvB,QAAQ,CAACwB,aAAa,CAACjF,GAAG,CAAC;MAExC,IAAI,CAACgF,MAAM,CAACV,UAAU,EAAE;QACtB,IAAIY,OAAO,GAAG,IAAI1I,OAAO,CAAC2I,OAAO,CAAC;UAChCC,KAAK,EAAE;YACL7C,MAAM,EAAEyC,MAAM,CAACzC;UACjB;QACF,CAAC,CAAC;QACFkB,QAAQ,CAAC4B,gBAAgB,CAACrF,GAAG,EAAEkF,OAAO,CAAC;QACvC/B,YAAY,CAACE,KAAK,CAACE,GAAG,CAAC2B,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,CAACI,MAAM,CAAC,UAAUC,MAAM,EAAEC,MAAM,EAAE;MAClC,IAAIN,OAAO,GAAGtH,KAAK,CAACuF,YAAY,CAAC,CAACpD,IAAI,CAAC4C,gBAAgB,CAAC6C,MAAM,CAAC;MAC/D,IAAIR,MAAM,GAAGvB,QAAQ,CAACwB,aAAa,CAACM,MAAM,CAAC;MAE3C,IAAI,CAACP,MAAM,CAACV,UAAU,EAAE;QACtB,IAAIY,OAAO,EAAE;UACX1I,OAAO,CAACiJ,WAAW,CAACP,OAAO,EAAE;YAC3BE,KAAK,EAAE;cACL7C,MAAM,EAAEyC,MAAM,CAACzC;YACjB;UACF,CAAC,EAAEvE,OAAO,EAAEuH,MAAM,CAAC;QACrB,CAAC,MAAM;UACLL,OAAO,GAAG,IAAI1I,OAAO,CAAC2I,OAAO,CAAC;YAC5BC,KAAK,EAAE;cACL7C,MAAM,EAAEyC,MAAM,CAACzC;YACjB;UACF,CAAC,CAAC;QACJ;QAEAkB,QAAQ,CAAC4B,gBAAgB,CAACE,MAAM,EAAEL,OAAO,CAAC;QAC1C/B,YAAY,CAACE,KAAK,CAACE,GAAG,CAAC2B,OAAO,CAAC;MACjC,CAAC,MAAM,IAAIA,OAAO,EAAE;QAClB/B,YAAY,CAACE,KAAK,CAACqC,MAAM,CAACR,OAAO,CAAC;MACpC;IACF,CAAC,CAAC,CAACQ,MAAM,CAAC,UAAU1F,GAAG,EAAE;MACvB,IAAIkF,OAAO,GAAGtH,KAAK,CAACuF,YAAY,CAAC,CAACpD,IAAI,CAAC4C,gBAAgB,CAAC3C,GAAG,CAAC;MAC5DmD,YAAY,CAACE,KAAK,CAACqC,MAAM,CAACR,OAAO,CAAC;IACpC,CAAC,CAAC,CAACS,OAAO,CAAC,CAAC;IACZlC,QAAQ,CAACmC,iBAAiB,CAAC,UAAUV,OAAO,EAAElF,GAAG,EAAE;MACjD,IAAII,SAAS,GAAGqD,QAAQ,CAACpD,YAAY,CAACL,GAAG,CAAC;MAC1C,IAAIuE,KAAK,GAAGd,QAAQ,CAACoC,aAAa,CAAC7F,GAAG,EAAE,OAAO,CAAC;MAChDkF,OAAO,CAACY,QAAQ,CAACrC,QAAQ,CAACoC,aAAa,CAAC7F,GAAG,EAAE,OAAO,CAAC,CAAC;MACtDxC,aAAa,CAAC0H,OAAO,EAAEzH,oBAAoB,CAAC2C,SAAS,CAAC,EAAE;QACtD2F,YAAY,EAAE/H,OAAO;QACrBgI,cAAc,EAAEhG,GAAG;QACnBiG,WAAW,EAAExC,QAAQ,CAACyC,OAAO,CAAClG,GAAG,CAAC,IAAI,EAAE;QACxCmG,YAAY,EAAEhJ,QAAQ,CAACoH,KAAK,CAACI,IAAI,CAAC,GAAGtI,SAAS,CAACuI,WAAW,CAACL,KAAK,CAACI,IAAI,EAAE,CAAC,CAAC,GAAG;MAC9E,CAAC,CAAC;MACFjI,wBAAwB,CAACwI,OAAO,EAAE9E,SAAS,CAAC;MAC5C3D,mBAAmB,CAACyI,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE9E,SAAS,CAACK,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;MACjF/C,SAAS,CAACwH,OAAO,CAAC,CAACkB,SAAS,GAAGpI,OAAO;IACxC,CAAC,CAAC;IACFJ,KAAK,CAACuF,YAAY,CAAC,CAACpD,IAAI,GAAG0D,QAAQ;IACnCN,YAAY,CAACE,KAAK,CAACgD,MAAM,GAAGrI,OAAO,CAACyC,GAAG,CAAC,QAAQ,CAAC,IAAI3C,WAAW,CAAC2C,GAAG,CAAC,QAAQ,CAAC;EAChF,CAAC;EAEDe,YAAY,CAACK,IAAI,GAAG,UAAU;EAC9B,OAAOL,YAAY;AACrB,CAAC,CAAC5E,UAAU,CAAC;AAEb,SAAS8G,UAAUA,CAAC3F,QAAQ,EAAED,WAAW,EAAEE,OAAO,EAAE;EAClD,IAAIyF,QAAQ;EACZ,IAAI6C,QAAQ;EACZ,IAAIrG,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEnC,IAAIlC,QAAQ,EAAE;IACZ,IAAIwI,gBAAgB,GAAGxJ,GAAG,CAACgB,QAAQ,IAAIA,QAAQ,CAACyI,UAAU,EAAE,UAAUC,QAAQ,EAAE;MAC9E,IAAI1G,IAAI,GAAGjC,WAAW,CAACuE,OAAO,CAAC,CAAC;MAChC,IAAIqE,IAAI,GAAG3G,IAAI,CAAC4G,gBAAgB,CAAC5G,IAAI,CAAC6G,YAAY,CAACH,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;MAErE,OAAOvJ,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC,EAAEwJ,IAAI,CAAC,EAAE;QAC9BG,IAAI,EAAEJ,QAAQ;QACd;QACAK,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;IACFR,QAAQ,GAAGvJ,GAAG,CAACkD,IAAI,EAAE,UAAUuC,GAAG,EAAExC,GAAG,EAAE;MACvC,OAAO;QACL6G,IAAI,EAAErE,GAAG;QACTX,IAAI,EAAE0E,gBAAgB,CAACvG,GAAG,GAAG,CAAC,CAAC,CAAC6B;MAClC,CAAC;IACH,CAAC,CAAC;IACF4B,QAAQ,GAAG,IAAInH,UAAU,CAACgK,QAAQ,EAAEtI,OAAO,CAAC;EAC9C,CAAC,MAAM;IACLsI,QAAQ,GAAG,CAAC;MACVO,IAAI,EAAE,OAAO;MACbhF,IAAI,EAAE;IACR,CAAC,CAAC;IACF4B,QAAQ,GAAG,IAAInH,UAAU,CAACgK,QAAQ,EAAEtI,OAAO,CAAC;EAC9C;EAEA,IAAI+I,OAAO,GAAGhK,GAAG,CAACiB,OAAO,CAACyC,GAAG,CAAC,MAAM,CAAC,EAAEzD,KAAK,CAACa,iBAAiB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,OAAO,CAAC,CAAC;EAEhG,IAAID,QAAQ,EAAE;IACZgJ,OAAO,GAAG9J,MAAM,CAAC8J,OAAO,EAAE/J,KAAK,CAACyC,cAAc,EAAE1B,QAAQ,CAAC,CAAC;EAC5D;EAEA,IAAIiJ,cAAc,GAAGjJ,QAAQ,GAAG,UAAUE,IAAI,EAAEgJ,OAAO,EAAEC,SAAS,EAAE7H,QAAQ,EAAE;IAC5E;IACA,IAAI8H,MAAM,GAAGlJ,IAAI,CAACK,KAAK,CAAC8I,IAAI,CAACC,KAAK,CAAChI,QAAQ,GAAG,CAAC,CAAC,CAAC,CAACA,QAAQ,GAAG,CAAC,CAAC;IAC/D,OAAO1B,cAAc,CAACwJ,MAAM,EAAEb,QAAQ,CAACjH,QAAQ,CAAC,CAAC;EACnD,CAAC,GAAG,UAAUpB,IAAI,EAAEgJ,OAAO,EAAEC,SAAS,EAAE7H,QAAQ,EAAE;IAChD,OAAO1B,cAAc,CAACM,IAAI,CAACqJ,KAAK,EAAEhB,QAAQ,CAACjH,QAAQ,CAAC,CAAC;EACvD,CAAC;EACDoE,QAAQ,CAAC8D,QAAQ,CAACR,OAAO,EAAE,IAAI,EAAEC,cAAc,CAAC;EAChDvD,QAAQ,CAAC+D,aAAa,GAAG,IAAI;EAC7B,OAAO/D,QAAQ;AACjB;AAEA,eAAejC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}