{"ast": null, "code": "import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function setUTCDay(dirtyDate, dirtyDay, dirtyOptions) {\n  requiredArgs(2, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;\n  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);\n  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  var date = toDate(dirtyDate);\n  var day = toInteger(dirtyDay);\n  var currentDay = date.getUTCDay();\n  var remainder = day % 7;\n  var dayIndex = (remainder + 7) % 7;\n  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "requiredArgs", "setUTCDay", "dirtyDate", "dirtyDay", "dirtyOptions", "arguments", "options", "locale", "localeWeekStartsOn", "weekStartsOn", "defaultWeekStartsOn", "RangeError", "date", "day", "currentDay", "getUTCDay", "remainder", "dayIndex", "diff", "setUTCDate", "getUTCDate"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/_lib/setUTCDay/index.js"], "sourcesContent": ["import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function setUTCDay(dirtyDate, dirtyDay, dirtyOptions) {\n  requiredArgs(2, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;\n  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);\n  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n\n  var date = toDate(dirtyDate);\n  var day = toInteger(dirtyDay);\n  var currentDay = date.getUTCDay();\n  var remainder = day % 7;\n  var dayIndex = (remainder + 7) % 7;\n  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,YAAY,MAAM,0BAA0B,CAAC,CAAC;AACrD;;AAEA,eAAe,SAASC,SAASA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EACnEJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,OAAO,GAAGF,YAAY,IAAI,CAAC,CAAC;EAChC,IAAIG,MAAM,GAAGD,OAAO,CAACC,MAAM;EAC3B,IAAIC,kBAAkB,GAAGD,MAAM,IAAIA,MAAM,CAACD,OAAO,IAAIC,MAAM,CAACD,OAAO,CAACG,YAAY;EAChF,IAAIC,mBAAmB,GAAGF,kBAAkB,IAAI,IAAI,GAAG,CAAC,GAAGV,SAAS,CAACU,kBAAkB,CAAC;EACxF,IAAIC,YAAY,GAAGH,OAAO,CAACG,YAAY,IAAI,IAAI,GAAGC,mBAAmB,GAAGZ,SAAS,CAACQ,OAAO,CAACG,YAAY,CAAC,CAAC,CAAC;;EAEzG,IAAI,EAAEA,YAAY,IAAI,CAAC,IAAIA,YAAY,IAAI,CAAC,CAAC,EAAE;IAC7C,MAAM,IAAIE,UAAU,CAAC,kDAAkD,CAAC;EAC1E;EAEA,IAAIC,IAAI,GAAGb,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIW,GAAG,GAAGf,SAAS,CAACK,QAAQ,CAAC;EAC7B,IAAIW,UAAU,GAAGF,IAAI,CAACG,SAAS,CAAC,CAAC;EACjC,IAAIC,SAAS,GAAGH,GAAG,GAAG,CAAC;EACvB,IAAII,QAAQ,GAAG,CAACD,SAAS,GAAG,CAAC,IAAI,CAAC;EAClC,IAAIE,IAAI,GAAG,CAACD,QAAQ,GAAGR,YAAY,GAAG,CAAC,GAAG,CAAC,IAAII,GAAG,GAAGC,UAAU;EAC/DF,IAAI,CAACO,UAAU,CAACP,IAAI,CAACQ,UAAU,CAAC,CAAC,GAAGF,IAAI,CAAC;EACzC,OAAON,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}