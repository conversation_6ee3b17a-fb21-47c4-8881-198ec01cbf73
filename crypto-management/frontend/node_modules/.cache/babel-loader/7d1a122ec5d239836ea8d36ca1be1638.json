{"ast": null, "code": "var JSBigInt = require('./biginteger')['JSBigInt'];\n\n/**\nCopyright (c) 2017, moneroexamples\n\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n1. Redistributions of source code must retain the above copyright notice, this\nlist of conditions and the following disclaimer.\n\n2. Redistributions in binary form must reproduce the above copyright notice,\nthis list of conditions and the following disclaimer in the documentation\nand/or other materials provided with the distribution.\n\n3. Neither the name of the copyright holder nor the names of its contributors\nmay be used to endorse or promote products derived from this software without\nspecific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\nANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\nWARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\nDISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\nFOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIA<PERSON>\nDAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\nSERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\nCAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\nOR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\nParts of the project are originally copyright (c) 2014-2017, MyMonero.com\n*/\n\nvar cnBase58 = function () {\n  var b58 = {};\n  var alphabet_str = \"**********************************************************\";\n  var alphabet = [];\n  for (var i = 0; i < alphabet_str.length; i++) {\n    alphabet.push(alphabet_str.charCodeAt(i));\n  }\n  var encoded_block_sizes = [0, 2, 3, 5, 6, 7, 9, 10, 11];\n  var alphabet_size = alphabet.length;\n  var full_block_size = 8;\n  var full_encoded_block_size = 11;\n  var UINT64_MAX = new JSBigInt(2).pow(64);\n  function hextobin(hex) {\n    if (hex.length % 2 !== 0) throw \"Hex string has invalid length!\";\n    var res = new Uint8Array(hex.length / 2);\n    for (var i = 0; i < hex.length / 2; ++i) {\n      res[i] = parseInt(hex.slice(i * 2, i * 2 + 2), 16);\n    }\n    return res;\n  }\n  function bintohex(bin) {\n    var out = [];\n    for (var i = 0; i < bin.length; ++i) {\n      out.push((\"0\" + bin[i].toString(16)).slice(-2));\n    }\n    return out.join(\"\");\n  }\n  function strtobin(str) {\n    var res = new Uint8Array(str.length);\n    for (var i = 0; i < str.length; i++) {\n      res[i] = str.charCodeAt(i);\n    }\n    return res;\n  }\n  function bintostr(bin) {\n    var out = [];\n    for (var i = 0; i < bin.length; i++) {\n      out.push(String.fromCharCode(bin[i]));\n    }\n    return out.join(\"\");\n  }\n  function uint8_be_to_64(data) {\n    if (data.length < 1 || data.length > 8) {\n      throw \"Invalid input length\";\n    }\n    var res = JSBigInt.ZERO;\n    var twopow8 = new JSBigInt(2).pow(8);\n    var i = 0;\n    switch (9 - data.length) {\n      case 1:\n        res = res.add(data[i++]);\n      case 2:\n        res = res.multiply(twopow8).add(data[i++]);\n      case 3:\n        res = res.multiply(twopow8).add(data[i++]);\n      case 4:\n        res = res.multiply(twopow8).add(data[i++]);\n      case 5:\n        res = res.multiply(twopow8).add(data[i++]);\n      case 6:\n        res = res.multiply(twopow8).add(data[i++]);\n      case 7:\n        res = res.multiply(twopow8).add(data[i++]);\n      case 8:\n        res = res.multiply(twopow8).add(data[i++]);\n        break;\n      default:\n        throw \"Impossible condition\";\n    }\n    return res;\n  }\n  function uint64_to_8be(num, size) {\n    var res = new Uint8Array(size);\n    if (size < 1 || size > 8) {\n      throw \"Invalid input length\";\n    }\n    var twopow8 = new JSBigInt(2).pow(8);\n    for (var i = size - 1; i >= 0; i--) {\n      res[i] = num.remainder(twopow8).toJSValue();\n      num = num.divide(twopow8);\n    }\n    return res;\n  }\n  b58.encode_block = function (data, buf, index) {\n    if (data.length < 1 || data.length > full_encoded_block_size) {\n      throw \"Invalid block length: \" + data.length;\n    }\n    var num = uint8_be_to_64(data);\n    var i = encoded_block_sizes[data.length] - 1;\n    // while num > 0\n    while (num.compare(0) === 1) {\n      var div = num.divRem(alphabet_size);\n      // remainder = num % alphabet_size\n      var remainder = div[1];\n      // num = num / alphabet_size\n      num = div[0];\n      buf[index + i] = alphabet[remainder.toJSValue()];\n      i--;\n    }\n    return buf;\n  };\n  b58.encode = function (hex) {\n    var data = hextobin(hex);\n    if (data.length === 0) {\n      return \"\";\n    }\n    var full_block_count = Math.floor(data.length / full_block_size);\n    var last_block_size = data.length % full_block_size;\n    var res_size = full_block_count * full_encoded_block_size + encoded_block_sizes[last_block_size];\n    var res = new Uint8Array(res_size);\n    var i;\n    for (i = 0; i < res_size; ++i) {\n      res[i] = alphabet[0];\n    }\n    for (i = 0; i < full_block_count; i++) {\n      res = b58.encode_block(data.subarray(i * full_block_size, i * full_block_size + full_block_size), res, i * full_encoded_block_size);\n    }\n    if (last_block_size > 0) {\n      res = b58.encode_block(data.subarray(full_block_count * full_block_size, full_block_count * full_block_size + last_block_size), res, full_block_count * full_encoded_block_size);\n    }\n    return bintostr(res);\n  };\n  b58.decode_block = function (data, buf, index) {\n    if (data.length < 1 || data.length > full_encoded_block_size) {\n      throw \"Invalid block length: \" + data.length;\n    }\n    var res_size = encoded_block_sizes.indexOf(data.length);\n    if (res_size <= 0) {\n      throw \"Invalid block size\";\n    }\n    var res_num = new JSBigInt(0);\n    var order = new JSBigInt(1);\n    for (var i = data.length - 1; i >= 0; i--) {\n      var digit = alphabet.indexOf(data[i]);\n      if (digit < 0) {\n        throw \"Invalid symbol\";\n      }\n      var product = order.multiply(digit).add(res_num);\n      // if product > UINT64_MAX\n      if (product.compare(UINT64_MAX) === 1) {\n        throw \"Overflow\";\n      }\n      res_num = product;\n      order = order.multiply(alphabet_size);\n    }\n    if (res_size < full_block_size && new JSBigInt(2).pow(8 * res_size).compare(res_num) <= 0) {\n      throw \"Overflow 2\";\n    }\n    buf.set(uint64_to_8be(res_num, res_size), index);\n    return buf;\n  };\n  b58.decode = function (enc) {\n    enc = strtobin(enc);\n    if (enc.length === 0) {\n      return \"\";\n    }\n    var full_block_count = Math.floor(enc.length / full_encoded_block_size);\n    var last_block_size = enc.length % full_encoded_block_size;\n    var last_block_decoded_size = encoded_block_sizes.indexOf(last_block_size);\n    if (last_block_decoded_size < 0) {\n      throw \"Invalid encoded length\";\n    }\n    var data_size = full_block_count * full_block_size + last_block_decoded_size;\n    var data = new Uint8Array(data_size);\n    for (var i = 0; i < full_block_count; i++) {\n      data = b58.decode_block(enc.subarray(i * full_encoded_block_size, i * full_encoded_block_size + full_encoded_block_size), data, i * full_block_size);\n    }\n    if (last_block_size > 0) {\n      data = b58.decode_block(enc.subarray(full_block_count * full_encoded_block_size, full_block_count * full_encoded_block_size + last_block_size), data, full_block_count * full_block_size);\n    }\n    return bintohex(data);\n  };\n  return b58;\n}();\nmodule.exports = cnBase58;", "map": {"version": 3, "names": ["JSBigInt", "require", "cnBase58", "b58", "alphabet_str", "alphabet", "i", "length", "push", "charCodeAt", "encoded_block_sizes", "alphabet_size", "full_block_size", "full_encoded_block_size", "UINT64_MAX", "pow", "hex<PERSON><PERSON>", "hex", "res", "Uint8Array", "parseInt", "slice", "bintohex", "bin", "out", "toString", "join", "str<PERSON>bin", "str", "bintostr", "String", "fromCharCode", "uint8_be_to_64", "data", "ZERO", "twopow8", "add", "multiply", "uint64_to_8be", "num", "size", "remainder", "toJSValue", "divide", "encode_block", "buf", "index", "compare", "div", "divRem", "encode", "full_block_count", "Math", "floor", "last_block_size", "res_size", "subarray", "decode_block", "indexOf", "res_num", "order", "digit", "product", "set", "decode", "enc", "last_block_decoded_size", "data_size", "module", "exports"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/multicoin-address-validator/src/crypto/cnBase58.js"], "sourcesContent": ["var JSBigInt = require('./biginteger')['JSBigInt'];\n\n/**\nCopyright (c) 2017, moneroexamples\n\nAll rights reserved.\n\nRedistribution and use in source and binary forms, with or without\nmodification, are permitted provided that the following conditions are met:\n\n1. Redistributions of source code must retain the above copyright notice, this\nlist of conditions and the following disclaimer.\n\n2. Redistributions in binary form must reproduce the above copyright notice,\nthis list of conditions and the following disclaimer in the documentation\nand/or other materials provided with the distribution.\n\n3. Neither the name of the copyright holder nor the names of its contributors\nmay be used to endorse or promote products derived from this software without\nspecific prior written permission.\n\nTHIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\nANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\nWARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\nDISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\nFOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIA<PERSON>\nDAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\nSERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\nCAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\nOR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\nOF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\nParts of the project are originally copyright (c) 2014-2017, MyMonero.com\n*/\n\nvar cnBase58 = (function () {\n    var b58 = {};\n\n    var alphabet_str = \"**********************************************************\";\n    var alphabet = [];\n    for (var i = 0; i < alphabet_str.length; i++) {\n        alphabet.push(alphabet_str.charCodeAt(i));\n    }\n    var encoded_block_sizes = [0, 2, 3, 5, 6, 7, 9, 10, 11];\n\n    var alphabet_size = alphabet.length;\n    var full_block_size = 8;\n    var full_encoded_block_size = 11;\n\n    var UINT64_MAX = new JSBigInt(2).pow(64);\n\n    function hextobin(hex) {\n        if (hex.length % 2 !== 0) throw \"Hex string has invalid length!\";\n        var res = new Uint8Array(hex.length / 2);\n        for (var i = 0; i < hex.length / 2; ++i) {\n            res[i] = parseInt(hex.slice(i * 2, i * 2 + 2), 16);\n        }\n        return res;\n    }\n\n    function bintohex(bin) {\n        var out = [];\n        for (var i = 0; i < bin.length; ++i) {\n            out.push((\"0\" + bin[i].toString(16)).slice(-2));\n        }\n        return out.join(\"\");\n    }\n\n    function strtobin(str) {\n        var res = new Uint8Array(str.length);\n        for (var i = 0; i < str.length; i++) {\n            res[i] = str.charCodeAt(i);\n        }\n        return res;\n    }\n\n    function bintostr(bin) {\n        var out = [];\n        for (var i = 0; i < bin.length; i++) {\n            out.push(String.fromCharCode(bin[i]));\n        }\n        return out.join(\"\");\n    }\n\n    function uint8_be_to_64(data) {\n        if (data.length < 1 || data.length > 8) {\n            throw \"Invalid input length\";\n        }\n        var res = JSBigInt.ZERO;\n        var twopow8 = new JSBigInt(2).pow(8);\n        var i = 0;\n        switch (9 - data.length) {\n        case 1:\n            res = res.add(data[i++]);\n        case 2:\n            res = res.multiply(twopow8).add(data[i++]);\n        case 3:\n            res = res.multiply(twopow8).add(data[i++]);\n        case 4:\n            res = res.multiply(twopow8).add(data[i++]);\n        case 5:\n            res = res.multiply(twopow8).add(data[i++]);\n        case 6:\n            res = res.multiply(twopow8).add(data[i++]);\n        case 7:\n            res = res.multiply(twopow8).add(data[i++]);\n        case 8:\n            res = res.multiply(twopow8).add(data[i++]);\n            break;\n        default:\n            throw \"Impossible condition\";\n        }\n        return res;\n    }\n\n    function uint64_to_8be(num, size) {\n        var res = new Uint8Array(size);\n        if (size < 1 || size > 8) {\n            throw \"Invalid input length\";\n        }\n        var twopow8 = new JSBigInt(2).pow(8);\n        for (var i = size - 1; i >= 0; i--) {\n            res[i] = num.remainder(twopow8).toJSValue();\n            num = num.divide(twopow8);\n        }\n        return res;\n    }\n\n    b58.encode_block = function (data, buf, index) {\n        if (data.length < 1 || data.length > full_encoded_block_size) {\n            throw \"Invalid block length: \" + data.length;\n        }\n        var num = uint8_be_to_64(data);\n        var i = encoded_block_sizes[data.length] - 1;\n        // while num > 0\n        while (num.compare(0) === 1) {\n            var div = num.divRem(alphabet_size);\n            // remainder = num % alphabet_size\n            var remainder = div[1];\n            // num = num / alphabet_size\n            num = div[0];\n            buf[index + i] = alphabet[remainder.toJSValue()];\n            i--;\n        }\n        return buf;\n    };\n\n    b58.encode = function (hex) {\n        var data = hextobin(hex);\n        if (data.length === 0) {\n            return \"\";\n        }\n        var full_block_count = Math.floor(data.length / full_block_size);\n        var last_block_size = data.length % full_block_size;\n        var res_size = full_block_count * full_encoded_block_size + encoded_block_sizes[last_block_size];\n\n        var res = new Uint8Array(res_size);\n        var i;\n        for (i = 0; i < res_size; ++i) {\n            res[i] = alphabet[0];\n        }\n        for (i = 0; i < full_block_count; i++) {\n            res = b58.encode_block(data.subarray(i * full_block_size, i * full_block_size + full_block_size), res, i * full_encoded_block_size);\n        }\n        if (last_block_size > 0) {\n            res = b58.encode_block(data.subarray(full_block_count * full_block_size, full_block_count * full_block_size + last_block_size), res, full_block_count * full_encoded_block_size)\n        }\n        return bintostr(res);\n    };\n\n    b58.decode_block = function (data, buf, index) {\n        if (data.length < 1 || data.length > full_encoded_block_size) {\n            throw \"Invalid block length: \" + data.length;\n        }\n\n        var res_size = encoded_block_sizes.indexOf(data.length);\n        if (res_size <= 0) {\n            throw \"Invalid block size\";\n        }\n        var res_num = new JSBigInt(0);\n        var order = new JSBigInt(1);\n        for (var i = data.length - 1; i >= 0; i--) {\n            var digit = alphabet.indexOf(data[i]);\n            if (digit < 0) {\n                throw \"Invalid symbol\";\n            }\n            var product = order.multiply(digit).add(res_num);\n            // if product > UINT64_MAX\n            if (product.compare(UINT64_MAX) === 1) {\n                throw \"Overflow\";\n            }\n            res_num = product;\n            order = order.multiply(alphabet_size);\n        }\n        if (res_size < full_block_size && (new JSBigInt(2).pow(8 * res_size).compare(res_num) <= 0)) {\n            throw \"Overflow 2\";\n        }\n        buf.set(uint64_to_8be(res_num, res_size), index);\n        return buf;\n    };\n\n    b58.decode = function (enc) {\n        enc = strtobin(enc);\n        if (enc.length === 0) {\n            return \"\";\n        }\n        var full_block_count = Math.floor(enc.length / full_encoded_block_size);\n        var last_block_size = enc.length % full_encoded_block_size;\n        var last_block_decoded_size = encoded_block_sizes.indexOf(last_block_size);\n        if (last_block_decoded_size < 0) {\n            throw \"Invalid encoded length\";\n        }\n        var data_size = full_block_count * full_block_size + last_block_decoded_size;\n        var data = new Uint8Array(data_size);\n        for (var i = 0; i < full_block_count; i++) {\n            data = b58.decode_block(enc.subarray(i * full_encoded_block_size, i * full_encoded_block_size + full_encoded_block_size), data, i * full_block_size);\n        }\n        if (last_block_size > 0) {\n            data = b58.decode_block(enc.subarray(full_block_count * full_encoded_block_size, full_block_count * full_encoded_block_size + last_block_size), data, full_block_count * full_block_size);\n        }\n        return bintohex(data);\n    };\n\n    return b58;\n})();\nmodule.exports = cnBase58;"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,QAAQ,GAAI,YAAY;EACxB,IAAIC,GAAG,GAAG,CAAC,CAAC;EAEZ,IAAIC,YAAY,GAAG,4DAA4D;EAC/E,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,YAAY,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1CD,QAAQ,CAACG,IAAI,CAACJ,YAAY,CAACK,UAAU,CAACH,CAAC,CAAC,CAAC;EAC7C;EACA,IAAII,mBAAmB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAEvD,IAAIC,aAAa,GAAGN,QAAQ,CAACE,MAAM;EACnC,IAAIK,eAAe,GAAG,CAAC;EACvB,IAAIC,uBAAuB,GAAG,EAAE;EAEhC,IAAIC,UAAU,GAAG,IAAId,QAAQ,CAAC,CAAC,CAAC,CAACe,GAAG,CAAC,EAAE,CAAC;EAExC,SAASC,QAAQA,CAACC,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACV,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,gCAAgC;IAChE,IAAIW,GAAG,GAAG,IAAIC,UAAU,CAACF,GAAG,CAACV,MAAM,GAAG,CAAC,CAAC;IACxC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,GAAG,CAACV,MAAM,GAAG,CAAC,EAAE,EAAED,CAAC,EAAE;MACrCY,GAAG,CAACZ,CAAC,CAAC,GAAGc,QAAQ,CAACH,GAAG,CAACI,KAAK,CAACf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;IACtD;IACA,OAAOY,GAAG;EACd;EAEA,SAASI,QAAQA,CAACC,GAAG,EAAE;IACnB,IAAIC,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,GAAG,CAAChB,MAAM,EAAE,EAAED,CAAC,EAAE;MACjCkB,GAAG,CAAChB,IAAI,CAAC,CAAC,GAAG,GAAGe,GAAG,CAACjB,CAAC,CAAC,CAACmB,QAAQ,CAAC,EAAE,CAAC,EAAEJ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD;IACA,OAAOG,GAAG,CAACE,IAAI,CAAC,EAAE,CAAC;EACvB;EAEA,SAASC,QAAQA,CAACC,GAAG,EAAE;IACnB,IAAIV,GAAG,GAAG,IAAIC,UAAU,CAACS,GAAG,CAACrB,MAAM,CAAC;IACpC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,GAAG,CAACrB,MAAM,EAAED,CAAC,EAAE,EAAE;MACjCY,GAAG,CAACZ,CAAC,CAAC,GAAGsB,GAAG,CAACnB,UAAU,CAACH,CAAC,CAAC;IAC9B;IACA,OAAOY,GAAG;EACd;EAEA,SAASW,QAAQA,CAACN,GAAG,EAAE;IACnB,IAAIC,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,GAAG,CAAChB,MAAM,EAAED,CAAC,EAAE,EAAE;MACjCkB,GAAG,CAAChB,IAAI,CAACsB,MAAM,CAACC,YAAY,CAACR,GAAG,CAACjB,CAAC,CAAC,CAAC,CAAC;IACzC;IACA,OAAOkB,GAAG,CAACE,IAAI,CAAC,EAAE,CAAC;EACvB;EAEA,SAASM,cAAcA,CAACC,IAAI,EAAE;IAC1B,IAAIA,IAAI,CAAC1B,MAAM,GAAG,CAAC,IAAI0B,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAE;MACpC,MAAM,sBAAsB;IAChC;IACA,IAAIW,GAAG,GAAGlB,QAAQ,CAACkC,IAAI;IACvB,IAAIC,OAAO,GAAG,IAAInC,QAAQ,CAAC,CAAC,CAAC,CAACe,GAAG,CAAC,CAAC,CAAC;IACpC,IAAIT,CAAC,GAAG,CAAC;IACT,QAAQ,CAAC,GAAG2B,IAAI,CAAC1B,MAAM;MACvB,KAAK,CAAC;QACFW,GAAG,GAAGA,GAAG,CAACkB,GAAG,CAACH,IAAI,CAAC3B,CAAC,EAAE,CAAC,CAAC;MAC5B,KAAK,CAAC;QACFY,GAAG,GAAGA,GAAG,CAACmB,QAAQ,CAACF,OAAO,CAAC,CAACC,GAAG,CAACH,IAAI,CAAC3B,CAAC,EAAE,CAAC,CAAC;MAC9C,KAAK,CAAC;QACFY,GAAG,GAAGA,GAAG,CAACmB,QAAQ,CAACF,OAAO,CAAC,CAACC,GAAG,CAACH,IAAI,CAAC3B,CAAC,EAAE,CAAC,CAAC;MAC9C,KAAK,CAAC;QACFY,GAAG,GAAGA,GAAG,CAACmB,QAAQ,CAACF,OAAO,CAAC,CAACC,GAAG,CAACH,IAAI,CAAC3B,CAAC,EAAE,CAAC,CAAC;MAC9C,KAAK,CAAC;QACFY,GAAG,GAAGA,GAAG,CAACmB,QAAQ,CAACF,OAAO,CAAC,CAACC,GAAG,CAACH,IAAI,CAAC3B,CAAC,EAAE,CAAC,CAAC;MAC9C,KAAK,CAAC;QACFY,GAAG,GAAGA,GAAG,CAACmB,QAAQ,CAACF,OAAO,CAAC,CAACC,GAAG,CAACH,IAAI,CAAC3B,CAAC,EAAE,CAAC,CAAC;MAC9C,KAAK,CAAC;QACFY,GAAG,GAAGA,GAAG,CAACmB,QAAQ,CAACF,OAAO,CAAC,CAACC,GAAG,CAACH,IAAI,CAAC3B,CAAC,EAAE,CAAC,CAAC;MAC9C,KAAK,CAAC;QACFY,GAAG,GAAGA,GAAG,CAACmB,QAAQ,CAACF,OAAO,CAAC,CAACC,GAAG,CAACH,IAAI,CAAC3B,CAAC,EAAE,CAAC,CAAC;QAC1C;MACJ;QACI,MAAM,sBAAsB;IAChC;IACA,OAAOY,GAAG;EACd;EAEA,SAASoB,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAE;IAC9B,IAAItB,GAAG,GAAG,IAAIC,UAAU,CAACqB,IAAI,CAAC;IAC9B,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,CAAC,EAAE;MACtB,MAAM,sBAAsB;IAChC;IACA,IAAIL,OAAO,GAAG,IAAInC,QAAQ,CAAC,CAAC,CAAC,CAACe,GAAG,CAAC,CAAC,CAAC;IACpC,KAAK,IAAIT,CAAC,GAAGkC,IAAI,GAAG,CAAC,EAAElC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAChCY,GAAG,CAACZ,CAAC,CAAC,GAAGiC,GAAG,CAACE,SAAS,CAACN,OAAO,CAAC,CAACO,SAAS,CAAC,CAAC;MAC3CH,GAAG,GAAGA,GAAG,CAACI,MAAM,CAACR,OAAO,CAAC;IAC7B;IACA,OAAOjB,GAAG;EACd;EAEAf,GAAG,CAACyC,YAAY,GAAG,UAAUX,IAAI,EAAEY,GAAG,EAAEC,KAAK,EAAE;IAC3C,IAAIb,IAAI,CAAC1B,MAAM,GAAG,CAAC,IAAI0B,IAAI,CAAC1B,MAAM,GAAGM,uBAAuB,EAAE;MAC1D,MAAM,wBAAwB,GAAGoB,IAAI,CAAC1B,MAAM;IAChD;IACA,IAAIgC,GAAG,GAAGP,cAAc,CAACC,IAAI,CAAC;IAC9B,IAAI3B,CAAC,GAAGI,mBAAmB,CAACuB,IAAI,CAAC1B,MAAM,CAAC,GAAG,CAAC;IAC5C;IACA,OAAOgC,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;MACzB,IAAIC,GAAG,GAAGT,GAAG,CAACU,MAAM,CAACtC,aAAa,CAAC;MACnC;MACA,IAAI8B,SAAS,GAAGO,GAAG,CAAC,CAAC,CAAC;MACtB;MACAT,GAAG,GAAGS,GAAG,CAAC,CAAC,CAAC;MACZH,GAAG,CAACC,KAAK,GAAGxC,CAAC,CAAC,GAAGD,QAAQ,CAACoC,SAAS,CAACC,SAAS,CAAC,CAAC,CAAC;MAChDpC,CAAC,EAAE;IACP;IACA,OAAOuC,GAAG;EACd,CAAC;EAED1C,GAAG,CAAC+C,MAAM,GAAG,UAAUjC,GAAG,EAAE;IACxB,IAAIgB,IAAI,GAAGjB,QAAQ,CAACC,GAAG,CAAC;IACxB,IAAIgB,IAAI,CAAC1B,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO,EAAE;IACb;IACA,IAAI4C,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACpB,IAAI,CAAC1B,MAAM,GAAGK,eAAe,CAAC;IAChE,IAAI0C,eAAe,GAAGrB,IAAI,CAAC1B,MAAM,GAAGK,eAAe;IACnD,IAAI2C,QAAQ,GAAGJ,gBAAgB,GAAGtC,uBAAuB,GAAGH,mBAAmB,CAAC4C,eAAe,CAAC;IAEhG,IAAIpC,GAAG,GAAG,IAAIC,UAAU,CAACoC,QAAQ,CAAC;IAClC,IAAIjD,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,QAAQ,EAAE,EAAEjD,CAAC,EAAE;MAC3BY,GAAG,CAACZ,CAAC,CAAC,GAAGD,QAAQ,CAAC,CAAC,CAAC;IACxB;IACA,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,gBAAgB,EAAE7C,CAAC,EAAE,EAAE;MACnCY,GAAG,GAAGf,GAAG,CAACyC,YAAY,CAACX,IAAI,CAACuB,QAAQ,CAAClD,CAAC,GAAGM,eAAe,EAAEN,CAAC,GAAGM,eAAe,GAAGA,eAAe,CAAC,EAAEM,GAAG,EAAEZ,CAAC,GAAGO,uBAAuB,CAAC;IACvI;IACA,IAAIyC,eAAe,GAAG,CAAC,EAAE;MACrBpC,GAAG,GAAGf,GAAG,CAACyC,YAAY,CAACX,IAAI,CAACuB,QAAQ,CAACL,gBAAgB,GAAGvC,eAAe,EAAEuC,gBAAgB,GAAGvC,eAAe,GAAG0C,eAAe,CAAC,EAAEpC,GAAG,EAAEiC,gBAAgB,GAAGtC,uBAAuB,CAAC;IACpL;IACA,OAAOgB,QAAQ,CAACX,GAAG,CAAC;EACxB,CAAC;EAEDf,GAAG,CAACsD,YAAY,GAAG,UAAUxB,IAAI,EAAEY,GAAG,EAAEC,KAAK,EAAE;IAC3C,IAAIb,IAAI,CAAC1B,MAAM,GAAG,CAAC,IAAI0B,IAAI,CAAC1B,MAAM,GAAGM,uBAAuB,EAAE;MAC1D,MAAM,wBAAwB,GAAGoB,IAAI,CAAC1B,MAAM;IAChD;IAEA,IAAIgD,QAAQ,GAAG7C,mBAAmB,CAACgD,OAAO,CAACzB,IAAI,CAAC1B,MAAM,CAAC;IACvD,IAAIgD,QAAQ,IAAI,CAAC,EAAE;MACf,MAAM,oBAAoB;IAC9B;IACA,IAAII,OAAO,GAAG,IAAI3D,QAAQ,CAAC,CAAC,CAAC;IAC7B,IAAI4D,KAAK,GAAG,IAAI5D,QAAQ,CAAC,CAAC,CAAC;IAC3B,KAAK,IAAIM,CAAC,GAAG2B,IAAI,CAAC1B,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvC,IAAIuD,KAAK,GAAGxD,QAAQ,CAACqD,OAAO,CAACzB,IAAI,CAAC3B,CAAC,CAAC,CAAC;MACrC,IAAIuD,KAAK,GAAG,CAAC,EAAE;QACX,MAAM,gBAAgB;MAC1B;MACA,IAAIC,OAAO,GAAGF,KAAK,CAACvB,QAAQ,CAACwB,KAAK,CAAC,CAACzB,GAAG,CAACuB,OAAO,CAAC;MAChD;MACA,IAAIG,OAAO,CAACf,OAAO,CAACjC,UAAU,CAAC,KAAK,CAAC,EAAE;QACnC,MAAM,UAAU;MACpB;MACA6C,OAAO,GAAGG,OAAO;MACjBF,KAAK,GAAGA,KAAK,CAACvB,QAAQ,CAAC1B,aAAa,CAAC;IACzC;IACA,IAAI4C,QAAQ,GAAG3C,eAAe,IAAK,IAAIZ,QAAQ,CAAC,CAAC,CAAC,CAACe,GAAG,CAAC,CAAC,GAAGwC,QAAQ,CAAC,CAACR,OAAO,CAACY,OAAO,CAAC,IAAI,CAAE,EAAE;MACzF,MAAM,YAAY;IACtB;IACAd,GAAG,CAACkB,GAAG,CAACzB,aAAa,CAACqB,OAAO,EAAEJ,QAAQ,CAAC,EAAET,KAAK,CAAC;IAChD,OAAOD,GAAG;EACd,CAAC;EAED1C,GAAG,CAAC6D,MAAM,GAAG,UAAUC,GAAG,EAAE;IACxBA,GAAG,GAAGtC,QAAQ,CAACsC,GAAG,CAAC;IACnB,IAAIA,GAAG,CAAC1D,MAAM,KAAK,CAAC,EAAE;MAClB,OAAO,EAAE;IACb;IACA,IAAI4C,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACY,GAAG,CAAC1D,MAAM,GAAGM,uBAAuB,CAAC;IACvE,IAAIyC,eAAe,GAAGW,GAAG,CAAC1D,MAAM,GAAGM,uBAAuB;IAC1D,IAAIqD,uBAAuB,GAAGxD,mBAAmB,CAACgD,OAAO,CAACJ,eAAe,CAAC;IAC1E,IAAIY,uBAAuB,GAAG,CAAC,EAAE;MAC7B,MAAM,wBAAwB;IAClC;IACA,IAAIC,SAAS,GAAGhB,gBAAgB,GAAGvC,eAAe,GAAGsD,uBAAuB;IAC5E,IAAIjC,IAAI,GAAG,IAAId,UAAU,CAACgD,SAAS,CAAC;IACpC,KAAK,IAAI7D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,gBAAgB,EAAE7C,CAAC,EAAE,EAAE;MACvC2B,IAAI,GAAG9B,GAAG,CAACsD,YAAY,CAACQ,GAAG,CAACT,QAAQ,CAAClD,CAAC,GAAGO,uBAAuB,EAAEP,CAAC,GAAGO,uBAAuB,GAAGA,uBAAuB,CAAC,EAAEoB,IAAI,EAAE3B,CAAC,GAAGM,eAAe,CAAC;IACxJ;IACA,IAAI0C,eAAe,GAAG,CAAC,EAAE;MACrBrB,IAAI,GAAG9B,GAAG,CAACsD,YAAY,CAACQ,GAAG,CAACT,QAAQ,CAACL,gBAAgB,GAAGtC,uBAAuB,EAAEsC,gBAAgB,GAAGtC,uBAAuB,GAAGyC,eAAe,CAAC,EAAErB,IAAI,EAAEkB,gBAAgB,GAAGvC,eAAe,CAAC;IAC7L;IACA,OAAOU,QAAQ,CAACW,IAAI,CAAC;EACzB,CAAC;EAED,OAAO9B,GAAG;AACd,CAAC,CAAE,CAAC;AACJiE,MAAM,CAACC,OAAO,GAAGnE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script"}