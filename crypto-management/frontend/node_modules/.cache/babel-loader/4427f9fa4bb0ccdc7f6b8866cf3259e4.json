{"ast": null, "code": "import getDate from \"../getDate/index.js\";\nimport getDay from \"../getDay/index.js\";\nimport startOfMonth from \"../startOfMonth/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getWeekOfMonth\n * @category Week Helpers\n * @summary Get the week of the month of the given date.\n *\n * @description\n * Get the week of the month of the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Number} the week of month\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // Which week of the month is 9 November 2017?\n * var result = getWeekOfMonth(new Date(2017, 10, 9))\n * //=> 2\n */\n\nexport default function getWeekOfMonth(date, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;\n  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);\n  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  var currentDayOfMonth = getDate(date);\n  if (isNaN(currentDayOfMonth)) {\n    return currentDayOfMonth;\n  }\n  var startWeekDay = getDay(startOfMonth(date));\n  var lastDayOfFirstWeek = 0;\n  if (startWeekDay >= weekStartsOn) {\n    lastDayOfFirstWeek = weekStartsOn + 7 - startWeekDay;\n  } else {\n    lastDayOfFirstWeek = weekStartsOn - startWeekDay;\n  }\n  var weekNumber = 1;\n  if (currentDayOfMonth > lastDayOfFirstWeek) {\n    var remainingDaysAfterFirstWeek = currentDayOfMonth - lastDayOfFirstWeek;\n    weekNumber = weekNumber + Math.ceil(remainingDaysAfterFirstWeek / 7);\n  }\n  return weekNumber;\n}", "map": {"version": 3, "names": ["getDate", "getDay", "startOfMonth", "toInteger", "requiredArgs", "getWeekOfMonth", "date", "dirtyOptions", "arguments", "options", "locale", "localeWeekStartsOn", "weekStartsOn", "defaultWeekStartsOn", "RangeError", "currentDayOfMonth", "isNaN", "startWeekDay", "lastDayOfFirstWeek", "weekNumber", "remainingDaysAfterFirstWeek", "Math", "ceil"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/getWeekOfMonth/index.js"], "sourcesContent": ["import getDate from \"../getDate/index.js\";\nimport getDay from \"../getDay/index.js\";\nimport startOfMonth from \"../startOfMonth/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getWeekOfMonth\n * @category Week Helpers\n * @summary Get the week of the month of the given date.\n *\n * @description\n * Get the week of the month of the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the given date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Number} the week of month\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // Which week of the month is 9 November 2017?\n * var result = getWeekOfMonth(new Date(2017, 10, 9))\n * //=> 2\n */\n\nexport default function getWeekOfMonth(date, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;\n  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);\n  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n\n  var currentDayOfMonth = getDate(date);\n\n  if (isNaN(currentDayOfMonth)) {\n    return currentDayOfMonth;\n  }\n\n  var startWeekDay = getDay(startOfMonth(date));\n  var lastDayOfFirstWeek = 0;\n\n  if (startWeekDay >= weekStartsOn) {\n    lastDayOfFirstWeek = weekStartsOn + 7 - startWeekDay;\n  } else {\n    lastDayOfFirstWeek = weekStartsOn - startWeekDay;\n  }\n\n  var weekNumber = 1;\n\n  if (currentDayOfMonth > lastDayOfFirstWeek) {\n    var remainingDaysAfterFirstWeek = currentDayOfMonth - lastDayOfFirstWeek;\n    weekNumber = weekNumber + Math.ceil(remainingDaysAfterFirstWeek / 7);\n  }\n\n  return weekNumber;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,qBAAqB;AACzC,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,cAAcA,CAACC,IAAI,EAAEC,YAAY,EAAE;EACzDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,OAAO,GAAGF,YAAY,IAAI,CAAC,CAAC;EAChC,IAAIG,MAAM,GAAGD,OAAO,CAACC,MAAM;EAC3B,IAAIC,kBAAkB,GAAGD,MAAM,IAAIA,MAAM,CAACD,OAAO,IAAIC,MAAM,CAACD,OAAO,CAACG,YAAY;EAChF,IAAIC,mBAAmB,GAAGF,kBAAkB,IAAI,IAAI,GAAG,CAAC,GAAGR,SAAS,CAACQ,kBAAkB,CAAC;EACxF,IAAIC,YAAY,GAAGH,OAAO,CAACG,YAAY,IAAI,IAAI,GAAGC,mBAAmB,GAAGV,SAAS,CAACM,OAAO,CAACG,YAAY,CAAC,CAAC,CAAC;;EAEzG,IAAI,EAAEA,YAAY,IAAI,CAAC,IAAIA,YAAY,IAAI,CAAC,CAAC,EAAE;IAC7C,MAAM,IAAIE,UAAU,CAAC,kDAAkD,CAAC;EAC1E;EAEA,IAAIC,iBAAiB,GAAGf,OAAO,CAACM,IAAI,CAAC;EAErC,IAAIU,KAAK,CAACD,iBAAiB,CAAC,EAAE;IAC5B,OAAOA,iBAAiB;EAC1B;EAEA,IAAIE,YAAY,GAAGhB,MAAM,CAACC,YAAY,CAACI,IAAI,CAAC,CAAC;EAC7C,IAAIY,kBAAkB,GAAG,CAAC;EAE1B,IAAID,YAAY,IAAIL,YAAY,EAAE;IAChCM,kBAAkB,GAAGN,YAAY,GAAG,CAAC,GAAGK,YAAY;EACtD,CAAC,MAAM;IACLC,kBAAkB,GAAGN,YAAY,GAAGK,YAAY;EAClD;EAEA,IAAIE,UAAU,GAAG,CAAC;EAElB,IAAIJ,iBAAiB,GAAGG,kBAAkB,EAAE;IAC1C,IAAIE,2BAA2B,GAAGL,iBAAiB,GAAGG,kBAAkB;IACxEC,UAAU,GAAGA,UAAU,GAAGE,IAAI,CAACC,IAAI,CAACF,2BAA2B,GAAG,CAAC,CAAC;EACtE;EAEA,OAAOD,UAAU;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}