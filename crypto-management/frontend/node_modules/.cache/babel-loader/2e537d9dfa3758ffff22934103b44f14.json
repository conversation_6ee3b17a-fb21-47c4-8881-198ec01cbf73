{"ast": null, "code": "import eachWeekendOfInterval from \"../eachWeekendOfInterval/index.js\";\nimport startOfMonth from \"../startOfMonth/index.js\";\nimport endOfMonth from \"../endOfMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfMonth\n * @category Month Helpers\n * @summary List all the Saturdays and Sundays in the given month.\n *\n * @description\n * Get all the Saturdays and Sundays in the given month.\n *\n * @param {Date|Number} date - the given month\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The passed date is invalid\n *\n * @example\n * // Lists all Saturdays and Sundays in the given month\n * const result = eachWeekendOfMonth(new Date(2022, 1, 1))\n * //=> [\n * //   Sat Feb 05 2022 00:00:00,\n * //   Sun Feb 06 2022 00:00:00,\n * //   Sat Feb 12 2022 00:00:00,\n * //   Sun Feb 13 2022 00:00:00,\n * //   Sat Feb 19 2022 00:00:00,\n * //   Sun Feb 20 2022 00:00:00,\n * //   Sat Feb 26 2022 00:00:00,\n * //   Sun Feb 27 2022 00:00:00\n * // ]\n */\n\nexport default function eachWeekendOfMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  var startDate = startOfMonth(dirtyDate);\n  if (isNaN(startDate.getTime())) throw new RangeError('The passed date is invalid');\n  var endDate = endOfMonth(dirtyDate);\n  return eachWeekendOfInterval({\n    start: startDate,\n    end: endDate\n  });\n}", "map": {"version": 3, "names": ["eachWeekendOfInterval", "startOfMonth", "endOfMonth", "requiredArgs", "eachWeekendOfMonth", "dirtyDate", "arguments", "startDate", "isNaN", "getTime", "RangeError", "endDate", "start", "end"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/eachWeekendOfMonth/index.js"], "sourcesContent": ["import eachWeekendOfInterval from \"../eachWeekendOfInterval/index.js\";\nimport startOfMonth from \"../startOfMonth/index.js\";\nimport endOfMonth from \"../endOfMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfMonth\n * @category Month Helpers\n * @summary List all the Saturdays and Sundays in the given month.\n *\n * @description\n * Get all the Saturdays and Sundays in the given month.\n *\n * @param {Date|Number} date - the given month\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The passed date is invalid\n *\n * @example\n * // Lists all Saturdays and Sundays in the given month\n * const result = eachWeekendOfMonth(new Date(2022, 1, 1))\n * //=> [\n * //   Sat Feb 05 2022 00:00:00,\n * //   Sun Feb 06 2022 00:00:00,\n * //   Sat Feb 12 2022 00:00:00,\n * //   Sun Feb 13 2022 00:00:00,\n * //   Sat Feb 19 2022 00:00:00,\n * //   Sun Feb 20 2022 00:00:00,\n * //   Sat Feb 26 2022 00:00:00,\n * //   Sun Feb 27 2022 00:00:00\n * // ]\n */\n\nexport default function eachWeekendOfMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  var startDate = startOfMonth(dirtyDate);\n  if (isNaN(startDate.getTime())) throw new RangeError('The passed date is invalid');\n  var endDate = endOfMonth(dirtyDate);\n  return eachWeekendOfInterval({\n    start: startDate,\n    end: endDate\n  });\n}"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,mCAAmC;AACrE,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,kBAAkBA,CAACC,SAAS,EAAE;EACpDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,SAAS,GAAGN,YAAY,CAACI,SAAS,CAAC;EACvC,IAAIG,KAAK,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,IAAIC,UAAU,CAAC,4BAA4B,CAAC;EAClF,IAAIC,OAAO,GAAGT,UAAU,CAACG,SAAS,CAAC;EACnC,OAAOL,qBAAqB,CAAC;IAC3BY,KAAK,EAAEL,SAAS;IAChBM,GAAG,EAAEF;EACP,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}