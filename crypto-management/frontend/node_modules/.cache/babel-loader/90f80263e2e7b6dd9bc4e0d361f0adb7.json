{"ast": null, "code": "/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nvar SUPPORTED_LOCALE = {\n  tr: {\n    regexp: /\\u0130|\\u0049|\\u0049\\u0307/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\"\n    }\n  },\n  az: {\n    regexp: /\\u0130/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\"\n    }\n  },\n  lt: {\n    regexp: /\\u0049|\\u004A|\\u012E|\\u00CC|\\u00CD|\\u0128/g,\n    map: {\n      I: \"\\u0069\\u0307\",\n      J: \"\\u006A\\u0307\",\n      Į: \"\\u012F\\u0307\",\n      Ì: \"\\u0069\\u0307\\u0300\",\n      Í: \"\\u0069\\u0307\\u0301\",\n      Ĩ: \"\\u0069\\u0307\\u0303\"\n    }\n  }\n};\n/**\n * Localized lower case.\n */\nexport function localeLowerCase(str, locale) {\n  var lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n  if (lang) return lowerCase(str.replace(lang.regexp, function (m) {\n    return lang.map[m];\n  }));\n  return lowerCase(str);\n}\n/**\n * Lower case as a function.\n */\nexport function lowerCase(str) {\n  return str.toLowerCase();\n}", "map": {"version": 3, "names": ["SUPPORTED_LOCALE", "tr", "regexp", "map", "İ", "I", "İ", "az", "lt", "J", "Į", "Ì", "Í", "Ĩ", "localeLowerCase", "str", "locale", "lang", "toLowerCase", "lowerCase", "replace", "m"], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * Locale character mapping rules.\n */\ninterface Locale {\n  regexp: RegExp;\n  map: Record<string, string>;\n}\n\n/**\n * Source: ftp://ftp.unicode.org/Public/UCD/latest/ucd/SpecialCasing.txt\n */\nconst SUPPORTED_LOCALE: Record<string, Locale> = {\n  tr: {\n    regexp: /\\u0130|\\u0049|\\u0049\\u0307/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\",\n    },\n  },\n  az: {\n    regexp: /\\u0130/g,\n    map: {\n      İ: \"\\u0069\",\n      I: \"\\u0131\",\n      İ: \"\\u0069\",\n    },\n  },\n  lt: {\n    regexp: /\\u0049|\\u004A|\\u012E|\\u00CC|\\u00CD|\\u0128/g,\n    map: {\n      I: \"\\u0069\\u0307\",\n      J: \"\\u006A\\u0307\",\n      Į: \"\\u012F\\u0307\",\n      Ì: \"\\u0069\\u0307\\u0300\",\n      Í: \"\\u0069\\u0307\\u0301\",\n      Ĩ: \"\\u0069\\u0307\\u0303\",\n    },\n  },\n};\n\n/**\n * Localized lower case.\n */\nexport function localeLowerCase(str: string, locale: string) {\n  const lang = SUPPORTED_LOCALE[locale.toLowerCase()];\n  if (lang) return lowerCase(str.replace(lang.regexp, (m) => lang.map[m]));\n  return lowerCase(str);\n}\n\n/**\n * Lower case as a function.\n */\nexport function lowerCase(str: string) {\n  return str.toLowerCase();\n}\n"], "mappings": "AAQA;;;AAGA,IAAMA,gBAAgB,GAA2B;EAC/CC,EAAE,EAAE;IACFC,MAAM,EAAE,6BAA6B;IACrCC,GAAG,EAAE;MACHC,CAAC,EAAE,QAAQ;MACXC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;;GAEP;EACDC,EAAE,EAAE;IACFL,MAAM,EAAE,SAAS;IACjBC,GAAG,EAAE;MACHC,CAAC,EAAE,QAAQ;MACXC,CAAC,EAAE,QAAQ;MACXC,EAAE,EAAE;;GAEP;EACDE,EAAE,EAAE;IACFN,MAAM,EAAE,4CAA4C;IACpDC,GAAG,EAAE;MACHE,CAAC,EAAE,cAAc;MACjBI,CAAC,EAAE,cAAc;MACjBC,CAAC,EAAE,cAAc;MACjBC,CAAC,EAAE,oBAAoB;MACvBC,CAAC,EAAE,oBAAoB;MACvBC,CAAC,EAAE;;;CAGR;AAED;;;AAGA,OAAM,SAAUC,eAAeA,CAACC,GAAW,EAAEC,MAAc;EACzD,IAAMC,IAAI,GAAGjB,gBAAgB,CAACgB,MAAM,CAACE,WAAW,EAAE,CAAC;EACnD,IAAID,IAAI,EAAE,OAAOE,SAAS,CAACJ,GAAG,CAACK,OAAO,CAACH,IAAI,CAACf,MAAM,EAAE,UAACmB,CAAC;IAAK,OAAAJ,IAAI,CAACd,GAAG,CAACkB,CAAC,CAAC;EAAX,CAAW,CAAC,CAAC;EACxE,OAAOF,SAAS,CAACJ,GAAG,CAAC;AACvB;AAEA;;;AAGA,OAAM,SAAUI,SAASA,CAACJ,GAAW;EACnC,OAAOA,GAAG,CAACG,WAAW,EAAE;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}