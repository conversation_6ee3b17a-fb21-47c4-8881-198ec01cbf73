{"ast": null, "code": "import differenceInDays from \"../differenceInDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\n/**\n * @name differenceInWeeks\n * @category Week Helpers\n * @summary Get the number of full weeks between the given dates.\n *\n * @description\n * Get the number of full weeks between two dates. Fractional weeks are\n * truncated towards zero by default.\n *\n * One \"full week\" is the distance between a local time in one day to the same\n * local time 7 days earlier or later. A full week can sometimes be less than\n * or more than 7*24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 7*24-hour periods, use this instead:\n * `Math.floor(differenceInHours(dateLeft, dateRight)/(7*24))|0`.\n *\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of full weeks\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInWeeks(new Date(2014, 6, 20), new Date(2014, 6, 5))\n * //=> 2\n *\n * // How many full weeks are between\n * // 1 March 2020 0:00 and 6 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 8 weeks (54 days),\n * // even if DST starts and the period has\n * // only 54*24-1 hours.\n * const result = differenceInWeeks(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 6)\n * )\n * //=> 8\n */\n\nexport default function differenceInWeeks(dateLeft, dateRight, options) {\n  requiredArgs(2, arguments);\n  var diff = differenceInDays(dateLeft, dateRight) / 7;\n  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}", "map": {"version": 3, "names": ["differenceInDays", "requiredArgs", "getRoundingMethod", "differenceInWeeks", "dateLeft", "dateRight", "options", "arguments", "diff", "roundingMethod"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/differenceInWeeks/index.js"], "sourcesContent": ["import differenceInDays from \"../differenceInDays/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getRoundingMethod } from \"../_lib/roundingMethods/index.js\";\n/**\n * @name differenceInWeeks\n * @category Week Helpers\n * @summary Get the number of full weeks between the given dates.\n *\n * @description\n * Get the number of full weeks between two dates. Fractional weeks are\n * truncated towards zero by default.\n *\n * One \"full week\" is the distance between a local time in one day to the same\n * local time 7 days earlier or later. A full week can sometimes be less than\n * or more than 7*24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 7*24-hour periods, use this instead:\n * `Math.floor(differenceInHours(dateLeft, dateRight)/(7*24))|0`.\n *\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @param {Object} [options] - an object with options.\n * @param {String} [options.roundingMethod='trunc'] - a rounding method (`ceil`, `floor`, `round` or `trunc`)\n * @returns {Number} the number of full weeks\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many full weeks are between 5 July 2014 and 20 July 2014?\n * const result = differenceInWeeks(new Date(2014, 6, 20), new Date(2014, 6, 5))\n * //=> 2\n *\n * // How many full weeks are between\n * // 1 March 2020 0:00 and 6 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 8 weeks (54 days),\n * // even if DST starts and the period has\n * // only 54*24-1 hours.\n * const result = differenceInWeeks(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 6)\n * )\n * //=> 8\n */\n\nexport default function differenceInWeeks(dateLeft, dateRight, options) {\n  requiredArgs(2, arguments);\n  var diff = differenceInDays(dateLeft, dateRight) / 7;\n  return getRoundingMethod(options === null || options === void 0 ? void 0 : options.roundingMethod)(diff);\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,YAAY,MAAM,+BAA+B;AACxD,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,iBAAiBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACtEL,YAAY,CAAC,CAAC,EAAEM,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGR,gBAAgB,CAACI,QAAQ,EAAEC,SAAS,CAAC,GAAG,CAAC;EACpD,OAAOH,iBAAiB,CAACI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,cAAc,CAAC,CAACD,IAAI,CAAC;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module"}