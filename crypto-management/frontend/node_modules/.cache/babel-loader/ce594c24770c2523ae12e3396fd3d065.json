{"ast": null, "code": "import eachWeekendOfInterval from \"../eachWeekendOfInterval/index.js\";\nimport startOfYear from \"../startOfYear/index.js\";\nimport endOfYear from \"../endOfYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfYear\n * @category Year Helpers\n * @summary List all the Saturdays and Sundays in the year.\n *\n * @description\n * Get all the Saturdays and Sundays in the year.\n *\n * @param {Date|Number} date - the given year\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The passed date is invalid\n *\n * @example\n * // Lists all Saturdays and Sundays in the year\n * var result = eachWeekendOfYear(new Date(2020, 1, 1))\n * //=> [\n * //   Sat Jan 03 2020 00:00:00,\n * //   Sun Jan 04 2020 00:00:00,\n * //   ...\n * //   Sun Dec 27 2020 00:00:00\n * // ]\n * ]\n */\n\nexport default function eachWeekendOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var startDate = startOfYear(dirtyDate);\n  if (isNaN(startDate)) throw new RangeError('The passed date is invalid');\n  var endDate = endOfYear(dirtyDate);\n  return eachWeekendOfInterval({\n    start: startDate,\n    end: endDate\n  });\n}", "map": {"version": 3, "names": ["eachWeekendOfInterval", "startOfYear", "endOfYear", "requiredArgs", "eachWeekendOfYear", "dirtyDate", "arguments", "startDate", "isNaN", "RangeError", "endDate", "start", "end"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/eachWeekendOfYear/index.js"], "sourcesContent": ["import eachWeekendOfInterval from \"../eachWeekendOfInterval/index.js\";\nimport startOfYear from \"../startOfYear/index.js\";\nimport endOfYear from \"../endOfYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfYear\n * @category Year Helpers\n * @summary List all the Saturdays and Sundays in the year.\n *\n * @description\n * Get all the Saturdays and Sundays in the year.\n *\n * @param {Date|Number} date - the given year\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The passed date is invalid\n *\n * @example\n * // Lists all Saturdays and Sundays in the year\n * var result = eachWeekendOfYear(new Date(2020, 1, 1))\n * //=> [\n * //   Sat Jan 03 2020 00:00:00,\n * //   Sun Jan 04 2020 00:00:00,\n * //   ...\n * //   Sun Dec 27 2020 00:00:00\n * // ]\n * ]\n */\n\nexport default function eachWeekendOfYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var startDate = startOfYear(dirtyDate);\n  if (isNaN(startDate)) throw new RangeError('The passed date is invalid');\n  var endDate = endOfYear(dirtyDate);\n  return eachWeekendOfInterval({\n    start: startDate,\n    end: endDate\n  });\n}"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,mCAAmC;AACrE,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EACnDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,SAAS,GAAGN,WAAW,CAACI,SAAS,CAAC;EACtC,IAAIG,KAAK,CAACD,SAAS,CAAC,EAAE,MAAM,IAAIE,UAAU,CAAC,4BAA4B,CAAC;EACxE,IAAIC,OAAO,GAAGR,SAAS,CAACG,SAAS,CAAC;EAClC,OAAOL,qBAAqB,CAAC;IAC3BW,KAAK,EAAEJ,SAAS;IAChBK,GAAG,EAAEF;EACP,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}