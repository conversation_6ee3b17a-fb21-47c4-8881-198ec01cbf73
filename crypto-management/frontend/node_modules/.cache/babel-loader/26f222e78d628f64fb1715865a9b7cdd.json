{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - Now, `closestTo` doesn't throw an exception\n *   when the second argument is not an array, and returns Invalid Date instead.\n *\n * @param {Date|Number} dateToCompare - the date to compare with\n * @param {Date[]|Number[]} datesArray - the array to search\n * @returns {Date} the date from the array closest to the given date\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * var dateToCompare = new Date(2015, 8, 6)\n * var result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\n\nexport default function closestTo(dirtyDateToCompare, dirtyDatesArray) {\n  requiredArgs(2, arguments);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  if (isNaN(dateToCompare)) {\n    return new Date(NaN);\n  }\n  var timeToCompare = dateToCompare.getTime();\n  var datesArray; // `dirtyDatesArray` is undefined or null\n\n  if (dirtyDatesArray == null) {\n    datesArray = []; // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  } else if (typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray; // If `dirtyDatesArray` is Array-like Object, convert to Array. Otherwise, make it empty Array\n  } else {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  }\n  var result;\n  var minDistance;\n  datesArray.forEach(function (dirtyDate) {\n    var currentDate = toDate(dirtyDate);\n    if (isNaN(currentDate)) {\n      result = new Date(NaN);\n      minDistance = NaN;\n      return;\n    }\n    var distance = Math.abs(timeToCompare - currentDate.getTime());\n    if (result == null || distance < minDistance) {\n      result = currentDate;\n      minDistance = distance;\n    }\n  });\n  return result;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "closestTo", "dirtyDateToCompare", "dirtyDatesArray", "arguments", "dateToCompare", "isNaN", "Date", "NaN", "timeToCompare", "getTime", "datesArray", "for<PERSON>ach", "Array", "prototype", "slice", "call", "result", "minDistance", "dirtyDate", "currentDate", "distance", "Math", "abs"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/closestTo/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - Now, `closestTo` doesn't throw an exception\n *   when the second argument is not an array, and returns Invalid Date instead.\n *\n * @param {Date|Number} dateToCompare - the date to compare with\n * @param {Date[]|Number[]} datesArray - the array to search\n * @returns {Date} the date from the array closest to the given date\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * var dateToCompare = new Date(2015, 8, 6)\n * var result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\n\nexport default function closestTo(dirtyDateToCompare, dirtyDatesArray) {\n  requiredArgs(2, arguments);\n  var dateToCompare = toDate(dirtyDateToCompare);\n\n  if (isNaN(dateToCompare)) {\n    return new Date(NaN);\n  }\n\n  var timeToCompare = dateToCompare.getTime();\n  var datesArray; // `dirtyDatesArray` is undefined or null\n\n  if (dirtyDatesArray == null) {\n    datesArray = []; // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  } else if (typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray; // If `dirtyDatesArray` is Array-like Object, convert to Array. Otherwise, make it empty Array\n  } else {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  }\n\n  var result;\n  var minDistance;\n  datesArray.forEach(function (dirtyDate) {\n    var currentDate = toDate(dirtyDate);\n\n    if (isNaN(currentDate)) {\n      result = new Date(NaN);\n      minDistance = NaN;\n      return;\n    }\n\n    var distance = Math.abs(timeToCompare - currentDate.getTime());\n\n    if (result == null || distance < minDistance) {\n      result = currentDate;\n      minDistance = distance;\n    }\n  });\n  return result;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,SAASA,CAACC,kBAAkB,EAAEC,eAAe,EAAE;EACrEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,aAAa,GAAGN,MAAM,CAACG,kBAAkB,CAAC;EAE9C,IAAII,KAAK,CAACD,aAAa,CAAC,EAAE;IACxB,OAAO,IAAIE,IAAI,CAACC,GAAG,CAAC;EACtB;EAEA,IAAIC,aAAa,GAAGJ,aAAa,CAACK,OAAO,CAAC,CAAC;EAC3C,IAAIC,UAAU,CAAC,CAAC;;EAEhB,IAAIR,eAAe,IAAI,IAAI,EAAE;IAC3BQ,UAAU,GAAG,EAAE,CAAC,CAAC;EACnB,CAAC,MAAM,IAAI,OAAOR,eAAe,CAACS,OAAO,KAAK,UAAU,EAAE;IACxDD,UAAU,GAAGR,eAAe,CAAC,CAAC;EAChC,CAAC,MAAM;IACLQ,UAAU,GAAGE,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACb,eAAe,CAAC;EAC1D;EAEA,IAAIc,MAAM;EACV,IAAIC,WAAW;EACfP,UAAU,CAACC,OAAO,CAAC,UAAUO,SAAS,EAAE;IACtC,IAAIC,WAAW,GAAGrB,MAAM,CAACoB,SAAS,CAAC;IAEnC,IAAIb,KAAK,CAACc,WAAW,CAAC,EAAE;MACtBH,MAAM,GAAG,IAAIV,IAAI,CAACC,GAAG,CAAC;MACtBU,WAAW,GAAGV,GAAG;MACjB;IACF;IAEA,IAAIa,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACd,aAAa,GAAGW,WAAW,CAACV,OAAO,CAAC,CAAC,CAAC;IAE9D,IAAIO,MAAM,IAAI,IAAI,IAAII,QAAQ,GAAGH,WAAW,EAAE;MAC5CD,MAAM,GAAGG,WAAW;MACpBF,WAAW,GAAGG,QAAQ;IACxB;EACF,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module"}