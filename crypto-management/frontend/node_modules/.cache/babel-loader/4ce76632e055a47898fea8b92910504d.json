{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachMonthOfInterval\n * @category Interval Helpers\n * @summary Return the array of months within the specified time interval.\n *\n * @description\n * Return the array of months within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} the array with starts of months from the month of the interval start to the month of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each month between 6 February 2014 and 10 August 2014:\n * var result = eachMonthOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Sat Feb 01 2014 00:00:00,\n * //   Sat Mar 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Thu May 01 2014 00:00:00,\n * //   Sun Jun 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * //   Fri Aug 01 2014 00:00:00\n * // ]\n */\n\nexport default function eachMonthOfInterval(dirtyInterval) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n  var dates = []; // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  var currentDate = startDate;\n  currentDate.setHours(0, 0, 0, 0);\n  currentDate.setDate(1);\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setMonth(currentDate.getMonth() + 1);\n  }\n  return dates;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "eachMonthOfInterval", "dirtyInterval", "arguments", "interval", "startDate", "start", "endDate", "end", "endTime", "getTime", "dates", "RangeError", "currentDate", "setHours", "setDate", "push", "setMonth", "getMonth"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/eachMonthOfInterval/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachMonthOfInterval\n * @category Interval Helpers\n * @summary Return the array of months within the specified time interval.\n *\n * @description\n * Return the array of months within the specified time interval.\n *\n * @param {Interval} interval - the interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} the array with starts of months from the month of the interval start to the month of the interval end\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Each month between 6 February 2014 and 10 August 2014:\n * var result = eachMonthOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Sat Feb 01 2014 00:00:00,\n * //   Sat Mar 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Thu May 01 2014 00:00:00,\n * //   Sun Jun 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * //   Fri Aug 01 2014 00:00:00\n * // ]\n */\n\nexport default function eachMonthOfInterval(dirtyInterval) {\n  requiredArgs(1, arguments);\n  var interval = dirtyInterval || {};\n  var startDate = toDate(interval.start);\n  var endDate = toDate(interval.end);\n  var endTime = endDate.getTime();\n  var dates = []; // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(startDate.getTime() <= endTime)) {\n    throw new RangeError('Invalid interval');\n  }\n\n  var currentDate = startDate;\n  currentDate.setHours(0, 0, 0, 0);\n  currentDate.setDate(1);\n\n  while (currentDate.getTime() <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setMonth(currentDate.getMonth() + 1);\n  }\n\n  return dates;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,mBAAmBA,CAACC,aAAa,EAAE;EACzDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGF,aAAa,IAAI,CAAC,CAAC;EAClC,IAAIG,SAAS,GAAGN,MAAM,CAACK,QAAQ,CAACE,KAAK,CAAC;EACtC,IAAIC,OAAO,GAAGR,MAAM,CAACK,QAAQ,CAACI,GAAG,CAAC;EAClC,IAAIC,OAAO,GAAGF,OAAO,CAACG,OAAO,CAAC,CAAC;EAC/B,IAAIC,KAAK,GAAG,EAAE,CAAC,CAAC;;EAEhB,IAAI,EAAEN,SAAS,CAACK,OAAO,CAAC,CAAC,IAAID,OAAO,CAAC,EAAE;IACrC,MAAM,IAAIG,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EAEA,IAAIC,WAAW,GAAGR,SAAS;EAC3BQ,WAAW,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChCD,WAAW,CAACE,OAAO,CAAC,CAAC,CAAC;EAEtB,OAAOF,WAAW,CAACH,OAAO,CAAC,CAAC,IAAID,OAAO,EAAE;IACvCE,KAAK,CAACK,IAAI,CAACjB,MAAM,CAACc,WAAW,CAAC,CAAC;IAC/BA,WAAW,CAACI,QAAQ,CAACJ,WAAW,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAClD;EAEA,OAAOP,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}