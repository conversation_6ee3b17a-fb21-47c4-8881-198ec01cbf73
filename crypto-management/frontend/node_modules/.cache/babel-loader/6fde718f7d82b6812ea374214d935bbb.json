{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { dotCase } from \"dot-case\";\nexport function paramCase(input, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return dotCase(input, __assign({\n    delimiter: \"-\"\n  }, options));\n}", "map": {"version": 3, "names": ["dotCase", "paramCase", "input", "options", "__assign", "delimiter"], "sources": ["../src/index.ts"], "sourcesContent": ["import { dotCase, Options } from \"dot-case\";\n\nexport { Options };\n\nexport function paramCase(input: string, options: Options = {}) {\n  return dotCase(input, {\n    delimiter: \"-\",\n    ...options,\n  });\n}\n"], "mappings": ";AAAA,SAASA,OAAO,QAAiB,UAAU;AAI3C,OAAM,SAAUC,SAASA,CAACC,KAAa,EAAEC,OAAqB;EAArB,IAAAA,OAAA;IAAAA,OAAA,KAAqB;EAAA;EAC5D,OAAOH,OAAO,CAACE,KAAK,EAAAE,QAAA;IAClBC,SAAS,EAAE;EAAG,GACXF,OAAO,EACV;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}