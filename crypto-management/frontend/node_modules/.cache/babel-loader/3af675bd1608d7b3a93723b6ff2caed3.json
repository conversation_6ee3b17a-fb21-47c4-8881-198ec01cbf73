{"ast": null, "code": "import compareAsc from \"../compareAsc/index.js\";\nimport differenceInYears from \"../differenceInYears/index.js\";\nimport differenceInMonths from \"../differenceInMonths/index.js\";\nimport differenceInDays from \"../differenceInDays/index.js\";\nimport differenceInHours from \"../differenceInHours/index.js\";\nimport differenceInMinutes from \"../differenceInMinutes/index.js\";\nimport differenceInSeconds from \"../differenceInSeconds/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport sub from \"../sub/index.js\";\n/**\n * @name intervalToDuration\n * @category Common Helpers\n * @summary Convert interval to duration\n *\n * @description\n * Convert a interval object to a duration object.\n *\n * @param {Interval} interval - the interval to convert to duration\n *\n * @returns {Duration} The duration Object\n * @throws {TypeError} Requires 2 arguments\n * @throws {RangeError} `start` must not be Invalid Date\n * @throws {RangeError} `end` must not be Invalid Date\n *\n * @example\n * // Get the duration between January 15, 1929 and April 4, 1968.\n * intervalToDuration({\n *   start: new Date(1929, 0, 15, 12, 0, 0),\n *   end: new Date(1968, 3, 4, 19, 5, 0)\n * })\n * // => { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }\n */\n\nexport default function intervalToDuration(_ref) {\n  var start = _ref.start,\n    end = _ref.end;\n  requiredArgs(1, arguments);\n  var dateLeft = toDate(start);\n  var dateRight = toDate(end);\n  if (!isValid(dateLeft)) {\n    throw new RangeError('Start Date is invalid');\n  }\n  if (!isValid(dateRight)) {\n    throw new RangeError('End Date is invalid');\n  }\n  var duration = {\n    years: 0,\n    months: 0,\n    days: 0,\n    hours: 0,\n    minutes: 0,\n    seconds: 0\n  };\n  var sign = compareAsc(dateLeft, dateRight);\n  duration.years = Math.abs(differenceInYears(dateLeft, dateRight));\n  var remainingMonths = sub(dateLeft, {\n    years: sign * duration.years\n  });\n  duration.months = Math.abs(differenceInMonths(remainingMonths, dateRight));\n  var remainingDays = sub(remainingMonths, {\n    months: sign * duration.months\n  });\n  duration.days = Math.abs(differenceInDays(remainingDays, dateRight));\n  var remainingHours = sub(remainingDays, {\n    days: sign * duration.days\n  });\n  duration.hours = Math.abs(differenceInHours(remainingHours, dateRight));\n  var remainingMinutes = sub(remainingHours, {\n    hours: sign * duration.hours\n  });\n  duration.minutes = Math.abs(differenceInMinutes(remainingMinutes, dateRight));\n  var remainingSeconds = sub(remainingMinutes, {\n    minutes: sign * duration.minutes\n  });\n  duration.seconds = Math.abs(differenceInSeconds(remainingSeconds, dateRight));\n  return duration;\n}", "map": {"version": 3, "names": ["compareAsc", "differenceInYears", "differenceInMonths", "differenceInDays", "differenceInHours", "differenceInMinutes", "differenceInSeconds", "<PERSON><PERSON><PERSON><PERSON>", "requiredArgs", "toDate", "sub", "intervalToDuration", "_ref", "start", "end", "arguments", "dateLeft", "dateRight", "RangeError", "duration", "years", "months", "days", "hours", "minutes", "seconds", "sign", "Math", "abs", "remainingMonths", "remainingDays", "remainingHours", "remainingMinutes", "remainingSeconds"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/intervalToDuration/index.js"], "sourcesContent": ["import compareAsc from \"../compareAsc/index.js\";\nimport differenceInYears from \"../differenceInYears/index.js\";\nimport differenceInMonths from \"../differenceInMonths/index.js\";\nimport differenceInDays from \"../differenceInDays/index.js\";\nimport differenceInHours from \"../differenceInHours/index.js\";\nimport differenceInMinutes from \"../differenceInMinutes/index.js\";\nimport differenceInSeconds from \"../differenceInSeconds/index.js\";\nimport isValid from \"../isValid/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport sub from \"../sub/index.js\";\n/**\n * @name intervalToDuration\n * @category Common Helpers\n * @summary Convert interval to duration\n *\n * @description\n * Convert a interval object to a duration object.\n *\n * @param {Interval} interval - the interval to convert to duration\n *\n * @returns {Duration} The duration Object\n * @throws {TypeError} Requires 2 arguments\n * @throws {RangeError} `start` must not be Invalid Date\n * @throws {RangeError} `end` must not be Invalid Date\n *\n * @example\n * // Get the duration between January 15, 1929 and April 4, 1968.\n * intervalToDuration({\n *   start: new Date(1929, 0, 15, 12, 0, 0),\n *   end: new Date(1968, 3, 4, 19, 5, 0)\n * })\n * // => { years: 39, months: 2, days: 20, hours: 7, minutes: 5, seconds: 0 }\n */\n\nexport default function intervalToDuration(_ref) {\n  var start = _ref.start,\n      end = _ref.end;\n  requiredArgs(1, arguments);\n  var dateLeft = toDate(start);\n  var dateRight = toDate(end);\n\n  if (!isValid(dateLeft)) {\n    throw new RangeError('Start Date is invalid');\n  }\n\n  if (!isValid(dateRight)) {\n    throw new RangeError('End Date is invalid');\n  }\n\n  var duration = {\n    years: 0,\n    months: 0,\n    days: 0,\n    hours: 0,\n    minutes: 0,\n    seconds: 0\n  };\n  var sign = compareAsc(dateLeft, dateRight);\n  duration.years = Math.abs(differenceInYears(dateLeft, dateRight));\n  var remainingMonths = sub(dateLeft, {\n    years: sign * duration.years\n  });\n  duration.months = Math.abs(differenceInMonths(remainingMonths, dateRight));\n  var remainingDays = sub(remainingMonths, {\n    months: sign * duration.months\n  });\n  duration.days = Math.abs(differenceInDays(remainingDays, dateRight));\n  var remainingHours = sub(remainingDays, {\n    days: sign * duration.days\n  });\n  duration.hours = Math.abs(differenceInHours(remainingHours, dateRight));\n  var remainingMinutes = sub(remainingHours, {\n    hours: sign * duration.hours\n  });\n  duration.minutes = Math.abs(differenceInMinutes(remainingMinutes, dateRight));\n  var remainingSeconds = sub(remainingMinutes, {\n    minutes: sign * duration.minutes\n  });\n  duration.seconds = Math.abs(differenceInSeconds(remainingSeconds, dateRight));\n  return duration;\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,GAAG,MAAM,iBAAiB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EAC/C,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,GAAG,GAAGF,IAAI,CAACE,GAAG;EAClBN,YAAY,CAAC,CAAC,EAAEO,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGP,MAAM,CAACI,KAAK,CAAC;EAC5B,IAAII,SAAS,GAAGR,MAAM,CAACK,GAAG,CAAC;EAE3B,IAAI,CAACP,OAAO,CAACS,QAAQ,CAAC,EAAE;IACtB,MAAM,IAAIE,UAAU,CAAC,uBAAuB,CAAC;EAC/C;EAEA,IAAI,CAACX,OAAO,CAACU,SAAS,CAAC,EAAE;IACvB,MAAM,IAAIC,UAAU,CAAC,qBAAqB,CAAC;EAC7C;EAEA,IAAIC,QAAQ,GAAG;IACbC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,IAAI,GAAG1B,UAAU,CAACgB,QAAQ,EAAEC,SAAS,CAAC;EAC1CE,QAAQ,CAACC,KAAK,GAAGO,IAAI,CAACC,GAAG,CAAC3B,iBAAiB,CAACe,QAAQ,EAAEC,SAAS,CAAC,CAAC;EACjE,IAAIY,eAAe,GAAGnB,GAAG,CAACM,QAAQ,EAAE;IAClCI,KAAK,EAAEM,IAAI,GAAGP,QAAQ,CAACC;EACzB,CAAC,CAAC;EACFD,QAAQ,CAACE,MAAM,GAAGM,IAAI,CAACC,GAAG,CAAC1B,kBAAkB,CAAC2B,eAAe,EAAEZ,SAAS,CAAC,CAAC;EAC1E,IAAIa,aAAa,GAAGpB,GAAG,CAACmB,eAAe,EAAE;IACvCR,MAAM,EAAEK,IAAI,GAAGP,QAAQ,CAACE;EAC1B,CAAC,CAAC;EACFF,QAAQ,CAACG,IAAI,GAAGK,IAAI,CAACC,GAAG,CAACzB,gBAAgB,CAAC2B,aAAa,EAAEb,SAAS,CAAC,CAAC;EACpE,IAAIc,cAAc,GAAGrB,GAAG,CAACoB,aAAa,EAAE;IACtCR,IAAI,EAAEI,IAAI,GAAGP,QAAQ,CAACG;EACxB,CAAC,CAAC;EACFH,QAAQ,CAACI,KAAK,GAAGI,IAAI,CAACC,GAAG,CAACxB,iBAAiB,CAAC2B,cAAc,EAAEd,SAAS,CAAC,CAAC;EACvE,IAAIe,gBAAgB,GAAGtB,GAAG,CAACqB,cAAc,EAAE;IACzCR,KAAK,EAAEG,IAAI,GAAGP,QAAQ,CAACI;EACzB,CAAC,CAAC;EACFJ,QAAQ,CAACK,OAAO,GAAGG,IAAI,CAACC,GAAG,CAACvB,mBAAmB,CAAC2B,gBAAgB,EAAEf,SAAS,CAAC,CAAC;EAC7E,IAAIgB,gBAAgB,GAAGvB,GAAG,CAACsB,gBAAgB,EAAE;IAC3CR,OAAO,EAAEE,IAAI,GAAGP,QAAQ,CAACK;EAC3B,CAAC,CAAC;EACFL,QAAQ,CAACM,OAAO,GAAGE,IAAI,CAACC,GAAG,CAACtB,mBAAmB,CAAC2B,gBAAgB,EAAEhB,SAAS,CAAC,CAAC;EAC7E,OAAOE,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}