{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport nextDay from \"../nextDay/index.js\";\nimport toDate from \"../toDate/index.js\";\n/**\n * @name nextWednesday\n * @category Weekday Helpers\n * @summary When is the next Wednesday?\n *\n * @description\n * When is the next Wednesday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Wednesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Wednesday after Mar, 22, 2020?\n * const result = nextWednesday(new Date(2020, 2, 22))\n * //=> Wed Mar 25 2020 00:00:00\n */\n\nexport default function nextWednesday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(toDate(date), 3);\n}", "map": {"version": 3, "names": ["requiredArgs", "nextDay", "toDate", "nextWednesday", "date", "arguments"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/nextWednesday/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport nextDay from \"../nextDay/index.js\";\nimport toDate from \"../toDate/index.js\";\n/**\n * @name nextWednesday\n * @category Weekday Helpers\n * @summary When is the next Wednesday?\n *\n * @description\n * When is the next Wednesday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Wednesday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Wednesday after Mar, 22, 2020?\n * const result = nextWednesday(new Date(2020, 2, 22))\n * //=> Wed Mar 25 2020 00:00:00\n */\n\nexport default function nextWednesday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(toDate(date), 3);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,MAAM,MAAM,oBAAoB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC1CJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,OAAOJ,OAAO,CAACC,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}