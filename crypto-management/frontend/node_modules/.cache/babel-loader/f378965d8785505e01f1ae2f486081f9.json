{"ast": null, "code": "import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function setUTCISODay(dirtyDate, dirtyDay) {\n  requiredArgs(2, arguments);\n  var day = toInteger(dirtyDay);\n  if (day % 7 === 0) {\n    day = day - 7;\n  }\n  var weekStartsOn = 1;\n  var date = toDate(dirtyDate);\n  var currentDay = date.getUTCDay();\n  var remainder = day % 7;\n  var dayIndex = (remainder + 7) % 7;\n  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "requiredArgs", "setUTCISODay", "dirtyDate", "dirtyDay", "arguments", "day", "weekStartsOn", "date", "currentDay", "getUTCDay", "remainder", "dayIndex", "diff", "setUTCDate", "getUTCDate"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/_lib/setUTCISODay/index.js"], "sourcesContent": ["import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function setUTCISODay(dirtyDate, dirtyDay) {\n  requiredArgs(2, arguments);\n  var day = toInteger(dirtyDay);\n\n  if (day % 7 === 0) {\n    day = day - 7;\n  }\n\n  var weekStartsOn = 1;\n  var date = toDate(dirtyDate);\n  var currentDay = date.getUTCDay();\n  var remainder = day % 7;\n  var dayIndex = (remainder + 7) % 7;\n  var diff = (dayIndex < weekStartsOn ? 7 : 0) + day - currentDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,YAAY,MAAM,0BAA0B,CAAC,CAAC;AACrD;;AAEA,eAAe,SAASC,YAAYA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EACxDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,GAAG,GAAGP,SAAS,CAACK,QAAQ,CAAC;EAE7B,IAAIE,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;IACjBA,GAAG,GAAGA,GAAG,GAAG,CAAC;EACf;EAEA,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,IAAI,GAAGR,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIM,UAAU,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;EACjC,IAAIC,SAAS,GAAGL,GAAG,GAAG,CAAC;EACvB,IAAIM,QAAQ,GAAG,CAACD,SAAS,GAAG,CAAC,IAAI,CAAC;EAClC,IAAIE,IAAI,GAAG,CAACD,QAAQ,GAAGL,YAAY,GAAG,CAAC,GAAG,CAAC,IAAID,GAAG,GAAGG,UAAU;EAC/DD,IAAI,CAACM,UAAU,CAACN,IAAI,CAACO,UAAU,CAAC,CAAC,GAAGF,IAAI,CAAC;EACzC,OAAOL,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}