{"ast": null, "code": "import startOfWeek from \"../startOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week?\n *\n * @description\n * Are the given dates in the same week?\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Boolean} the dates are in the same week\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * var result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * var result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n */\nexport default function isSameWeek(dirtyDateLeft, dirtyDateRight, dirtyOptions) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfWeek = startOfWeek(dirtyDateLeft, dirtyOptions);\n  var dateRightStartOfWeek = startOfWeek(dirtyDateRight, dirtyOptions);\n  return dateLeftStartOfWeek.getTime() === dateRightStartOfWeek.getTime();\n}", "map": {"version": 3, "names": ["startOfWeek", "requiredArgs", "isSameWeek", "dirtyDateLeft", "dirtyDateRight", "dirtyOptions", "arguments", "dateLeftStartOfWeek", "dateRightStartOfWeek", "getTime"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/isSameWeek/index.js"], "sourcesContent": ["import startOfWeek from \"../startOfWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n\n/**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week?\n *\n * @description\n * Are the given dates in the same week?\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @returns {Boolean} the dates are in the same week\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * var result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * var result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n */\nexport default function isSameWeek(dirtyDateLeft, dirtyDateRight, dirtyOptions) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfWeek = startOfWeek(dirtyDateLeft, dirtyOptions);\n  var dateRightStartOfWeek = startOfWeek(dirtyDateRight, dirtyOptions);\n  return dateLeftStartOfWeek.getTime() === dateRightStartOfWeek.getTime();\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,yBAAyB;AACjD,OAAOC,YAAY,MAAM,+BAA+B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,aAAa,EAAEC,cAAc,EAAEC,YAAY,EAAE;EAC9EJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAIC,mBAAmB,GAAGP,WAAW,CAACG,aAAa,EAAEE,YAAY,CAAC;EAClE,IAAIG,oBAAoB,GAAGR,WAAW,CAACI,cAAc,EAAEC,YAAY,CAAC;EACpE,OAAOE,mBAAmB,CAACE,OAAO,CAAC,CAAC,KAAKD,oBAAoB,CAACC,OAAO,CAAC,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module"}