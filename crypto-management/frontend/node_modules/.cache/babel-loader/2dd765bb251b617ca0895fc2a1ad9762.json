{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\nvar _constant = require(\"../constant\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\n\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\nvar createSensor = function createSensor(element) {\n  var sensor = undefined; // callback\n\n  var listeners = [];\n  /**\n   * create object DOM of sensor\n   * @returns {HTMLObjectElement}\n   */\n\n  var newSensor = function newSensor() {\n    // adjust style\n    if (getComputedStyle(element).position === 'static') {\n      element.style.position = 'relative';\n    }\n    var obj = document.createElement('object');\n    obj.onload = function () {\n      obj.contentDocument.defaultView.addEventListener('resize', resizeListener); // 直接触发一次 resize\n\n      resizeListener();\n    };\n    obj.style.display = 'block';\n    obj.style.position = 'absolute';\n    obj.style.top = '0';\n    obj.style.left = '0';\n    obj.style.height = '100%';\n    obj.style.width = '100%';\n    obj.style.overflow = 'hidden';\n    obj.style.pointerEvents = 'none';\n    obj.style.zIndex = '-1';\n    obj.style.opacity = '0';\n    obj.setAttribute('class', _constant.SensorClassName);\n    obj.setAttribute('tabindex', _constant.SensorTabIndex);\n    obj.type = 'text/html'; // append into dom\n\n    element.appendChild(obj); // for ie, should set data attribute delay, or will be white screen\n\n    obj.data = 'about:blank';\n    return obj;\n  };\n  /**\n   * trigger listeners\n   */\n\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all listener\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n  /**\n   * listen with one callback function\n   * @param cb\n   */\n\n  var bind = function bind(cb) {\n    // if not exist sensor, then create one\n    if (!sensor) {\n      sensor = newSensor();\n    }\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n  /**\n   * destroy all\n   */\n\n  var destroy = function destroy() {\n    if (sensor && sensor.parentNode) {\n      if (sensor.contentDocument) {\n        // remote event\n        sensor.contentDocument.defaultView.removeEventListener('resize', resizeListener);\n      } // remove dom\n\n      sensor.parentNode.removeChild(sensor); // initial variable\n\n      sensor = undefined;\n      listeners = [];\n    }\n  };\n  /**\n   * cancel listener bind\n   * @param cb\n   */\n\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    } // no listener, and sensor is exist\n    // then destroy the sensor\n\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\nexports.createSensor = createSensor;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "createSensor", "_debounce", "_interopRequireDefault", "require", "_constant", "obj", "__esModule", "element", "sensor", "undefined", "listeners", "newSensor", "getComputedStyle", "position", "style", "document", "createElement", "onload", "contentDocument", "defaultView", "addEventListener", "resizeListener", "display", "top", "left", "height", "width", "overflow", "pointerEvents", "zIndex", "opacity", "setAttribute", "SensorClassName", "SensorTabIndex", "type", "append<PERSON><PERSON><PERSON>", "data", "for<PERSON>ach", "listener", "bind", "cb", "indexOf", "push", "destroy", "parentNode", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "unbind", "idx", "splice", "length"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/size-sensor/lib/sensors/object.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createSensor = void 0;\n\nvar _debounce = _interopRequireDefault(require(\"../debounce\"));\n\nvar _constant = require(\"../constant\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\nvar createSensor = function createSensor(element) {\n  var sensor = undefined; // callback\n\n  var listeners = [];\n  /**\n   * create object DOM of sensor\n   * @returns {HTMLObjectElement}\n   */\n\n  var newSensor = function newSensor() {\n    // adjust style\n    if (getComputedStyle(element).position === 'static') {\n      element.style.position = 'relative';\n    }\n\n    var obj = document.createElement('object');\n\n    obj.onload = function () {\n      obj.contentDocument.defaultView.addEventListener('resize', resizeListener); // 直接触发一次 resize\n\n      resizeListener();\n    };\n\n    obj.style.display = 'block';\n    obj.style.position = 'absolute';\n    obj.style.top = '0';\n    obj.style.left = '0';\n    obj.style.height = '100%';\n    obj.style.width = '100%';\n    obj.style.overflow = 'hidden';\n    obj.style.pointerEvents = 'none';\n    obj.style.zIndex = '-1';\n    obj.style.opacity = '0';\n    obj.setAttribute('class', _constant.SensorClassName);\n    obj.setAttribute('tabindex', _constant.SensorTabIndex);\n    obj.type = 'text/html'; // append into dom\n\n    element.appendChild(obj); // for ie, should set data attribute delay, or will be white screen\n\n    obj.data = 'about:blank';\n    return obj;\n  };\n  /**\n   * trigger listeners\n   */\n\n\n  var resizeListener = (0, _debounce[\"default\"])(function () {\n    // trigger all listener\n    listeners.forEach(function (listener) {\n      listener(element);\n    });\n  });\n  /**\n   * listen with one callback function\n   * @param cb\n   */\n\n  var bind = function bind(cb) {\n    // if not exist sensor, then create one\n    if (!sensor) {\n      sensor = newSensor();\n    }\n\n    if (listeners.indexOf(cb) === -1) {\n      listeners.push(cb);\n    }\n  };\n  /**\n   * destroy all\n   */\n\n\n  var destroy = function destroy() {\n    if (sensor && sensor.parentNode) {\n      if (sensor.contentDocument) {\n        // remote event\n        sensor.contentDocument.defaultView.removeEventListener('resize', resizeListener);\n      } // remove dom\n\n\n      sensor.parentNode.removeChild(sensor); // initial variable\n\n      sensor = undefined;\n      listeners = [];\n    }\n  };\n  /**\n   * cancel listener bind\n   * @param cb\n   */\n\n\n  var unbind = function unbind(cb) {\n    var idx = listeners.indexOf(cb);\n\n    if (idx !== -1) {\n      listeners.splice(idx, 1);\n    } // no listener, and sensor is exist\n    // then destroy the sensor\n\n\n    if (listeners.length === 0 && sensor) {\n      destroy();\n    }\n  };\n\n  return {\n    element: element,\n    bind: bind,\n    destroy: destroy,\n    unbind: unbind\n  };\n};\n\nexports.createSensor = createSensor;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,YAAY,GAAG,KAAK,CAAC;AAE7B,IAAIC,SAAS,GAAGC,sBAAsB,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;AAE9D,IAAIC,SAAS,GAAGD,OAAO,CAAC,aAAa,CAAC;AAEtC,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;;AAEhG;AACA;AACA;AACA;AACA,IAAIL,YAAY,GAAG,SAASA,YAAYA,CAACO,OAAO,EAAE;EAChD,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC;;EAExB,IAAIC,SAAS,GAAG,EAAE;EAClB;AACF;AACA;AACA;;EAEE,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC;IACA,IAAIC,gBAAgB,CAACL,OAAO,CAAC,CAACM,QAAQ,KAAK,QAAQ,EAAE;MACnDN,OAAO,CAACO,KAAK,CAACD,QAAQ,GAAG,UAAU;IACrC;IAEA,IAAIR,GAAG,GAAGU,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAE1CX,GAAG,CAACY,MAAM,GAAG,YAAY;MACvBZ,GAAG,CAACa,eAAe,CAACC,WAAW,CAACC,gBAAgB,CAAC,QAAQ,EAAEC,cAAc,CAAC,CAAC,CAAC;;MAE5EA,cAAc,CAAC,CAAC;IAClB,CAAC;IAEDhB,GAAG,CAACS,KAAK,CAACQ,OAAO,GAAG,OAAO;IAC3BjB,GAAG,CAACS,KAAK,CAACD,QAAQ,GAAG,UAAU;IAC/BR,GAAG,CAACS,KAAK,CAACS,GAAG,GAAG,GAAG;IACnBlB,GAAG,CAACS,KAAK,CAACU,IAAI,GAAG,GAAG;IACpBnB,GAAG,CAACS,KAAK,CAACW,MAAM,GAAG,MAAM;IACzBpB,GAAG,CAACS,KAAK,CAACY,KAAK,GAAG,MAAM;IACxBrB,GAAG,CAACS,KAAK,CAACa,QAAQ,GAAG,QAAQ;IAC7BtB,GAAG,CAACS,KAAK,CAACc,aAAa,GAAG,MAAM;IAChCvB,GAAG,CAACS,KAAK,CAACe,MAAM,GAAG,IAAI;IACvBxB,GAAG,CAACS,KAAK,CAACgB,OAAO,GAAG,GAAG;IACvBzB,GAAG,CAAC0B,YAAY,CAAC,OAAO,EAAE3B,SAAS,CAAC4B,eAAe,CAAC;IACpD3B,GAAG,CAAC0B,YAAY,CAAC,UAAU,EAAE3B,SAAS,CAAC6B,cAAc,CAAC;IACtD5B,GAAG,CAAC6B,IAAI,GAAG,WAAW,CAAC,CAAC;;IAExB3B,OAAO,CAAC4B,WAAW,CAAC9B,GAAG,CAAC,CAAC,CAAC;;IAE1BA,GAAG,CAAC+B,IAAI,GAAG,aAAa;IACxB,OAAO/B,GAAG;EACZ,CAAC;EACD;AACF;AACA;;EAGE,IAAIgB,cAAc,GAAG,CAAC,CAAC,EAAEpB,SAAS,CAAC,SAAS,CAAC,EAAE,YAAY;IACzD;IACAS,SAAS,CAAC2B,OAAO,CAAC,UAAUC,QAAQ,EAAE;MACpCA,QAAQ,CAAC/B,OAAO,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;AACF;AACA;AACA;;EAEE,IAAIgC,IAAI,GAAG,SAASA,IAAIA,CAACC,EAAE,EAAE;IAC3B;IACA,IAAI,CAAChC,MAAM,EAAE;MACXA,MAAM,GAAGG,SAAS,CAAC,CAAC;IACtB;IAEA,IAAID,SAAS,CAAC+B,OAAO,CAACD,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;MAChC9B,SAAS,CAACgC,IAAI,CAACF,EAAE,CAAC;IACpB;EACF,CAAC;EACD;AACF;AACA;;EAGE,IAAIG,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/B,IAAInC,MAAM,IAAIA,MAAM,CAACoC,UAAU,EAAE;MAC/B,IAAIpC,MAAM,CAACU,eAAe,EAAE;QAC1B;QACAV,MAAM,CAACU,eAAe,CAACC,WAAW,CAAC0B,mBAAmB,CAAC,QAAQ,EAAExB,cAAc,CAAC;MAClF,CAAC,CAAC;;MAGFb,MAAM,CAACoC,UAAU,CAACE,WAAW,CAACtC,MAAM,CAAC,CAAC,CAAC;;MAEvCA,MAAM,GAAGC,SAAS;MAClBC,SAAS,GAAG,EAAE;IAChB;EACF,CAAC;EACD;AACF;AACA;AACA;;EAGE,IAAIqC,MAAM,GAAG,SAASA,MAAMA,CAACP,EAAE,EAAE;IAC/B,IAAIQ,GAAG,GAAGtC,SAAS,CAAC+B,OAAO,CAACD,EAAE,CAAC;IAE/B,IAAIQ,GAAG,KAAK,CAAC,CAAC,EAAE;MACdtC,SAAS,CAACuC,MAAM,CAACD,GAAG,EAAE,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF;;IAGA,IAAItC,SAAS,CAACwC,MAAM,KAAK,CAAC,IAAI1C,MAAM,EAAE;MACpCmC,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,OAAO;IACLpC,OAAO,EAAEA,OAAO;IAChBgC,IAAI,EAAEA,IAAI;IACVI,OAAO,EAAEA,OAAO;IAChBI,MAAM,EAAEA;EACV,CAAC;AACH,CAAC;AAEDjD,OAAO,CAACE,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script"}