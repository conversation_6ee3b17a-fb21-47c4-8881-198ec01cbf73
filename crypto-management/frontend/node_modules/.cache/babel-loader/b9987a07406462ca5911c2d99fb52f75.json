{"ast": null, "code": "function dateLongFormatter(pattern, formatLong) {\n  switch (pattern) {\n    case 'P':\n      return formatLong.date({\n        width: 'short'\n      });\n    case 'PP':\n      return formatLong.date({\n        width: 'medium'\n      });\n    case 'PPP':\n      return formatLong.date({\n        width: 'long'\n      });\n    case 'PPPP':\n    default:\n      return formatLong.date({\n        width: 'full'\n      });\n  }\n}\nfunction timeLongFormatter(pattern, formatLong) {\n  switch (pattern) {\n    case 'p':\n      return formatLong.time({\n        width: 'short'\n      });\n    case 'pp':\n      return formatLong.time({\n        width: 'medium'\n      });\n    case 'ppp':\n      return formatLong.time({\n        width: 'long'\n      });\n    case 'pppp':\n    default:\n      return formatLong.time({\n        width: 'full'\n      });\n  }\n}\nfunction dateTimeLongFormatter(pattern, formatLong) {\n  var matchResult = pattern.match(/(P+)(p+)?/);\n  var datePattern = matchResult[1];\n  var timePattern = matchResult[2];\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n  var dateTimeFormat;\n  switch (datePattern) {\n    case 'P':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'short'\n      });\n      break;\n    case 'PP':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'medium'\n      });\n      break;\n    case 'PPP':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'long'\n      });\n      break;\n    case 'PPPP':\n    default:\n      dateTimeFormat = formatLong.dateTime({\n        width: 'full'\n      });\n      break;\n  }\n  return dateTimeFormat.replace('{{date}}', dateLongFormatter(datePattern, formatLong)).replace('{{time}}', timeLongFormatter(timePattern, formatLong));\n}\nvar longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter\n};\nexport default longFormatters;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pattern", "formatLong", "date", "width", "time<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "time", "dateTimeLongFormatter", "matchResult", "match", "datePattern", "timePattern", "dateTimeFormat", "dateTime", "replace", "longFormatters", "p", "P"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/_lib/format/longFormatters/index.js"], "sourcesContent": ["function dateLongFormatter(pattern, formatLong) {\n  switch (pattern) {\n    case 'P':\n      return formatLong.date({\n        width: 'short'\n      });\n\n    case 'PP':\n      return formatLong.date({\n        width: 'medium'\n      });\n\n    case 'PPP':\n      return formatLong.date({\n        width: 'long'\n      });\n\n    case 'PPPP':\n    default:\n      return formatLong.date({\n        width: 'full'\n      });\n  }\n}\n\nfunction timeLongFormatter(pattern, formatLong) {\n  switch (pattern) {\n    case 'p':\n      return formatLong.time({\n        width: 'short'\n      });\n\n    case 'pp':\n      return formatLong.time({\n        width: 'medium'\n      });\n\n    case 'ppp':\n      return formatLong.time({\n        width: 'long'\n      });\n\n    case 'pppp':\n    default:\n      return formatLong.time({\n        width: 'full'\n      });\n  }\n}\n\nfunction dateTimeLongFormatter(pattern, formatLong) {\n  var matchResult = pattern.match(/(P+)(p+)?/);\n  var datePattern = matchResult[1];\n  var timePattern = matchResult[2];\n\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n\n  var dateTimeFormat;\n\n  switch (datePattern) {\n    case 'P':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'short'\n      });\n      break;\n\n    case 'PP':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'medium'\n      });\n      break;\n\n    case 'PPP':\n      dateTimeFormat = formatLong.dateTime({\n        width: 'long'\n      });\n      break;\n\n    case 'PPPP':\n    default:\n      dateTimeFormat = formatLong.dateTime({\n        width: 'full'\n      });\n      break;\n  }\n\n  return dateTimeFormat.replace('{{date}}', dateLongFormatter(datePattern, formatLong)).replace('{{time}}', timeLongFormatter(timePattern, formatLong));\n}\n\nvar longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter\n};\nexport default longFormatters;"], "mappings": "AAAA,SAASA,iBAAiBA,CAACC,OAAO,EAAEC,UAAU,EAAE;EAC9C,QAAQD,OAAO;IACb,KAAK,GAAG;MACN,OAAOC,UAAU,CAACC,IAAI,CAAC;QACrBC,KAAK,EAAE;MACT,CAAC,CAAC;IAEJ,KAAK,IAAI;MACP,OAAOF,UAAU,CAACC,IAAI,CAAC;QACrBC,KAAK,EAAE;MACT,CAAC,CAAC;IAEJ,KAAK,KAAK;MACR,OAAOF,UAAU,CAACC,IAAI,CAAC;QACrBC,KAAK,EAAE;MACT,CAAC,CAAC;IAEJ,KAAK,MAAM;IACX;MACE,OAAOF,UAAU,CAACC,IAAI,CAAC;QACrBC,KAAK,EAAE;MACT,CAAC,CAAC;EACN;AACF;AAEA,SAASC,iBAAiBA,CAACJ,OAAO,EAAEC,UAAU,EAAE;EAC9C,QAAQD,OAAO;IACb,KAAK,GAAG;MACN,OAAOC,UAAU,CAACI,IAAI,CAAC;QACrBF,KAAK,EAAE;MACT,CAAC,CAAC;IAEJ,KAAK,IAAI;MACP,OAAOF,UAAU,CAACI,IAAI,CAAC;QACrBF,KAAK,EAAE;MACT,CAAC,CAAC;IAEJ,KAAK,KAAK;MACR,OAAOF,UAAU,CAACI,IAAI,CAAC;QACrBF,KAAK,EAAE;MACT,CAAC,CAAC;IAEJ,KAAK,MAAM;IACX;MACE,OAAOF,UAAU,CAACI,IAAI,CAAC;QACrBF,KAAK,EAAE;MACT,CAAC,CAAC;EACN;AACF;AAEA,SAASG,qBAAqBA,CAACN,OAAO,EAAEC,UAAU,EAAE;EAClD,IAAIM,WAAW,GAAGP,OAAO,CAACQ,KAAK,CAAC,WAAW,CAAC;EAC5C,IAAIC,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;EAChC,IAAIG,WAAW,GAAGH,WAAW,CAAC,CAAC,CAAC;EAEhC,IAAI,CAACG,WAAW,EAAE;IAChB,OAAOX,iBAAiB,CAACC,OAAO,EAAEC,UAAU,CAAC;EAC/C;EAEA,IAAIU,cAAc;EAElB,QAAQF,WAAW;IACjB,KAAK,GAAG;MACNE,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QACnCT,KAAK,EAAE;MACT,CAAC,CAAC;MACF;IAEF,KAAK,IAAI;MACPQ,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QACnCT,KAAK,EAAE;MACT,CAAC,CAAC;MACF;IAEF,KAAK,KAAK;MACRQ,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QACnCT,KAAK,EAAE;MACT,CAAC,CAAC;MACF;IAEF,KAAK,MAAM;IACX;MACEQ,cAAc,GAAGV,UAAU,CAACW,QAAQ,CAAC;QACnCT,KAAK,EAAE;MACT,CAAC,CAAC;MACF;EACJ;EAEA,OAAOQ,cAAc,CAACE,OAAO,CAAC,UAAU,EAAEd,iBAAiB,CAACU,WAAW,EAAER,UAAU,CAAC,CAAC,CAACY,OAAO,CAAC,UAAU,EAAET,iBAAiB,CAACM,WAAW,EAAET,UAAU,CAAC,CAAC;AACvJ;AAEA,IAAIa,cAAc,GAAG;EACnBC,CAAC,EAAEX,iBAAiB;EACpBY,CAAC,EAAEV;AACL,CAAC;AACD,eAAeQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}