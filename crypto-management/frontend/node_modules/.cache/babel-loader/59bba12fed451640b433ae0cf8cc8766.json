{"ast": null, "code": "import getTime from \"../getTime/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getUnixTime\n * @category Timestamp Helpers\n * @summary Get the seconds timestamp of the given date.\n *\n * @description\n * Get the seconds timestamp of the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the timestamp\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Get the timestamp of 29 February 2012 11:45:05 CET:\n * const result = getUnixTime(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 1330512305\n */\n\nexport default function getUnixTime(dirtyDate) {\n  requiredArgs(1, arguments);\n  return Math.floor(getTime(dirtyDate) / 1000);\n}", "map": {"version": 3, "names": ["getTime", "requiredArgs", "getUnixTime", "dirtyDate", "arguments", "Math", "floor"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/getUnixTime/index.js"], "sourcesContent": ["import getTime from \"../getTime/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getUnixTime\n * @category Timestamp Helpers\n * @summary Get the seconds timestamp of the given date.\n *\n * @description\n * Get the seconds timestamp of the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the timestamp\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Get the timestamp of 29 February 2012 11:45:05 CET:\n * const result = getUnixTime(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 1330512305\n */\n\nexport default function getUnixTime(dirtyDate) {\n  requiredArgs(1, arguments);\n  return Math.floor(getTime(dirtyDate) / 1000);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,qBAAqB;AACzC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,WAAWA,CAACC,SAAS,EAAE;EAC7CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOC,IAAI,CAACC,KAAK,CAACN,OAAO,CAACG,SAAS,CAAC,GAAG,IAAI,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module"}