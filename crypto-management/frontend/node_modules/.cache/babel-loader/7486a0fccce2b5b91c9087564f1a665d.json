{"ast": null, "code": "export default function buildLocalizeFn(args) {\n  return function (dirtyIndex, dirtyOptions) {\n    var options = dirtyOptions || {};\n    var context = options.context ? String(options.context) : 'standalone';\n    var valuesArray;\n    if (context === 'formatting' && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(dirtyIndex) : dirtyIndex; // @ts-ignore: For some reason TypeScript just don't want to match it, no matter how hard we try. I challange you to try to remove it!\n\n    return valuesArray[index];\n  };\n}", "map": {"version": 3, "names": ["buildLocalizeFn", "args", "dirtyIndex", "dirtyOptions", "options", "context", "String", "valuesArray", "formattingValues", "defaultWidth", "defaultFormattingWidth", "width", "_defaultWidth", "_width", "values", "index", "argument<PERSON>allback"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/locale/_lib/buildLocalizeFn/index.js"], "sourcesContent": ["export default function buildLocalizeFn(args) {\n  return function (dirtyIndex, dirtyOptions) {\n    var options = dirtyOptions || {};\n    var context = options.context ? String(options.context) : 'standalone';\n    var valuesArray;\n\n    if (context === 'formatting' && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n\n      var _width = options.width ? String(options.width) : args.defaultWidth;\n\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n\n    var index = args.argumentCallback ? args.argumentCallback(dirtyIndex) : dirtyIndex; // @ts-ignore: For some reason TypeScript just don't want to match it, no matter how hard we try. I challange you to try to remove it!\n\n    return valuesArray[index];\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,eAAeA,CAACC,IAAI,EAAE;EAC5C,OAAO,UAAUC,UAAU,EAAEC,YAAY,EAAE;IACzC,IAAIC,OAAO,GAAGD,YAAY,IAAI,CAAC,CAAC;IAChC,IAAIE,OAAO,GAAGD,OAAO,CAACC,OAAO,GAAGC,MAAM,CAACF,OAAO,CAACC,OAAO,CAAC,GAAG,YAAY;IACtE,IAAIE,WAAW;IAEf,IAAIF,OAAO,KAAK,YAAY,IAAIJ,IAAI,CAACO,gBAAgB,EAAE;MACrD,IAAIC,YAAY,GAAGR,IAAI,CAACS,sBAAsB,IAAIT,IAAI,CAACQ,YAAY;MACnE,IAAIE,KAAK,GAAGP,OAAO,CAACO,KAAK,GAAGL,MAAM,CAACF,OAAO,CAACO,KAAK,CAAC,GAAGF,YAAY;MAChEF,WAAW,GAAGN,IAAI,CAACO,gBAAgB,CAACG,KAAK,CAAC,IAAIV,IAAI,CAACO,gBAAgB,CAACC,YAAY,CAAC;IACnF,CAAC,MAAM;MACL,IAAIG,aAAa,GAAGX,IAAI,CAACQ,YAAY;MAErC,IAAII,MAAM,GAAGT,OAAO,CAACO,KAAK,GAAGL,MAAM,CAACF,OAAO,CAACO,KAAK,CAAC,GAAGV,IAAI,CAACQ,YAAY;MAEtEF,WAAW,GAAGN,IAAI,CAACa,MAAM,CAACD,MAAM,CAAC,IAAIZ,IAAI,CAACa,MAAM,CAACF,aAAa,CAAC;IACjE;IAEA,IAAIG,KAAK,GAAGd,IAAI,CAACe,gBAAgB,GAAGf,IAAI,CAACe,gBAAgB,CAACd,UAAU,CAAC,GAAGA,UAAU,CAAC,CAAC;;IAEpF,OAAOK,WAAW,CAACQ,KAAK,CAAC;EAC3B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module"}