{"ast": null, "code": "import isValid from \"../isValid/index.js\";\nimport isWeekend from \"../isWeekend/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport addDays from \"../addDays/index.js\";\nimport isSameDay from \"../isSameDay/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInBusinessDays\n * @category Day Helpers\n * @summary Get the number of business days between the given dates.\n *\n * @description\n * Get the number of business day periods between the given dates.\n * Business days being days that arent in the weekend.\n * Like `differenceInCalendarDays`, the function removes the times from\n * the dates before calculating the difference.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of business days\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many business days are between\n * // 10 January 2014 and 20 July 2014?\n * var result = differenceInBusinessDays(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 0, 10)\n * )\n * //=> 136\n */\n\nexport default function differenceInBusinessDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  if (!isValid(dateLeft) || !isValid(dateRight)) return NaN;\n  var calendarDifference = differenceInCalendarDays(dateLeft, dateRight);\n  var sign = calendarDifference < 0 ? -1 : 1;\n  var weeks = toInteger(calendarDifference / 7);\n  var result = weeks * 5;\n  dateRight = addDays(dateRight, weeks * 7); // the loop below will run at most 6 times to account for the remaining days that don't makeup a full week\n\n  while (!isSameDay(dateLeft, dateRight)) {\n    // sign is used to account for both negative and positive differences\n    result += isWeekend(dateRight) ? 0 : sign;\n    dateRight = addDays(dateRight, sign);\n  }\n  return result === 0 ? 0 : result;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isWeekend", "toDate", "differenceInCalendarDays", "addDays", "isSameDay", "toInteger", "requiredArgs", "differenceInBusinessDays", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeft", "dateRight", "NaN", "calendarDifference", "sign", "weeks", "result"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/differenceInBusinessDays/index.js"], "sourcesContent": ["import isValid from \"../isValid/index.js\";\nimport isWeekend from \"../isWeekend/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport differenceInCalendarDays from \"../differenceInCalendarDays/index.js\";\nimport addDays from \"../addDays/index.js\";\nimport isSameDay from \"../isSameDay/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name differenceInBusinessDays\n * @category Day Helpers\n * @summary Get the number of business days between the given dates.\n *\n * @description\n * Get the number of business day periods between the given dates.\n * Business days being days that arent in the weekend.\n * Like `differenceInCalendarDays`, the function removes the times from\n * the dates before calculating the difference.\n *\n * @param {Date|Number} dateLeft - the later date\n * @param {Date|Number} dateRight - the earlier date\n * @returns {Number} the number of business days\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // How many business days are between\n * // 10 January 2014 and 20 July 2014?\n * var result = differenceInBusinessDays(\n *   new Date(2014, 6, 20),\n *   new Date(2014, 0, 10)\n * )\n * //=> 136\n */\n\nexport default function differenceInBusinessDays(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyDateLeft);\n  var dateRight = toDate(dirtyDateRight);\n  if (!isValid(dateLeft) || !isValid(dateRight)) return NaN;\n  var calendarDifference = differenceInCalendarDays(dateLeft, dateRight);\n  var sign = calendarDifference < 0 ? -1 : 1;\n  var weeks = toInteger(calendarDifference / 7);\n  var result = weeks * 5;\n  dateRight = addDays(dateRight, weeks * 7); // the loop below will run at most 6 times to account for the remaining days that don't makeup a full week\n\n  while (!isSameDay(dateLeft, dateRight)) {\n    // sign is used to account for both negative and positive differences\n    result += isWeekend(dateRight) ? 0 : sign;\n    dateRight = addDays(dateRight, sign);\n  }\n\n  return result === 0 ? 0 : result;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,qBAAqB;AACzC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,wBAAwBA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAC9EH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGV,MAAM,CAACO,aAAa,CAAC;EACpC,IAAII,SAAS,GAAGX,MAAM,CAACQ,cAAc,CAAC;EACtC,IAAI,CAACV,OAAO,CAACY,QAAQ,CAAC,IAAI,CAACZ,OAAO,CAACa,SAAS,CAAC,EAAE,OAAOC,GAAG;EACzD,IAAIC,kBAAkB,GAAGZ,wBAAwB,CAACS,QAAQ,EAAEC,SAAS,CAAC;EACtE,IAAIG,IAAI,GAAGD,kBAAkB,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC1C,IAAIE,KAAK,GAAGX,SAAS,CAACS,kBAAkB,GAAG,CAAC,CAAC;EAC7C,IAAIG,MAAM,GAAGD,KAAK,GAAG,CAAC;EACtBJ,SAAS,GAAGT,OAAO,CAACS,SAAS,EAAEI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;;EAE3C,OAAO,CAACZ,SAAS,CAACO,QAAQ,EAAEC,SAAS,CAAC,EAAE;IACtC;IACAK,MAAM,IAAIjB,SAAS,CAACY,SAAS,CAAC,GAAG,CAAC,GAAGG,IAAI;IACzCH,SAAS,GAAGT,OAAO,CAACS,SAAS,EAAEG,IAAI,CAAC;EACtC;EAEA,OAAOE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}