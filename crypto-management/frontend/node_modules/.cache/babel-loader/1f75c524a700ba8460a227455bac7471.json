{"ast": null, "code": "import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCWeekYear(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate, dirtyOptions);\n  var year = date.getUTCFullYear();\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeFirstWeekContainsDate = locale && locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate); // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n  var firstWeekOfNextYear = new Date(0);\n  firstWeekOfNextYear.setUTCFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCWeek(firstWeekOfNextYear, dirtyOptions);\n  var firstWeekOfThisYear = new Date(0);\n  firstWeekOfThisYear.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCWeek(firstWeekOfThisYear, dirtyOptions);\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "startOfUTCWeek", "requiredArgs", "getUTCWeekYear", "dirtyDate", "dirtyOptions", "arguments", "date", "year", "getUTCFullYear", "options", "locale", "localeFirstWeekContainsDate", "firstWeekContainsDate", "defaultFirstWeekContainsDate", "RangeError", "firstWeekOfNextYear", "Date", "setUTCFullYear", "setUTCHours", "startOfNextYear", "firstWeekOfThisYear", "startOfThisYear", "getTime"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/_lib/getUTCWeekYear/index.js"], "sourcesContent": ["import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function getUTCWeekYear(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate, dirtyOptions);\n  var year = date.getUTCFullYear();\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeFirstWeekContainsDate = locale && locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate); // Test if weekStartsOn is between 1 and 7 _and_ is not NaN\n\n  if (!(firstWeekContainsDate >= 1 && firstWeekContainsDate <= 7)) {\n    throw new RangeError('firstWeekContainsDate must be between 1 and 7 inclusively');\n  }\n\n  var firstWeekOfNextYear = new Date(0);\n  firstWeekOfNextYear.setUTCFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setUTCHours(0, 0, 0, 0);\n  var startOfNextYear = startOfUTCWeek(firstWeekOfNextYear, dirtyOptions);\n  var firstWeekOfThisYear = new Date(0);\n  firstWeekOfThisYear.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setUTCHours(0, 0, 0, 0);\n  var startOfThisYear = startOfUTCWeek(firstWeekOfThisYear, dirtyOptions);\n\n  if (date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,YAAY,MAAM,0BAA0B,CAAC,CAAC;AACrD;;AAEA,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC9DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGP,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC;EAC1C,IAAIG,IAAI,GAAGD,IAAI,CAACE,cAAc,CAAC,CAAC;EAChC,IAAIC,OAAO,GAAGL,YAAY,IAAI,CAAC,CAAC;EAChC,IAAIM,MAAM,GAAGD,OAAO,CAACC,MAAM;EAC3B,IAAIC,2BAA2B,GAAGD,MAAM,IAAIA,MAAM,CAACD,OAAO,IAAIC,MAAM,CAACD,OAAO,CAACG,qBAAqB;EAClG,IAAIC,4BAA4B,GAAGF,2BAA2B,IAAI,IAAI,GAAG,CAAC,GAAGb,SAAS,CAACa,2BAA2B,CAAC;EACnH,IAAIC,qBAAqB,GAAGH,OAAO,CAACG,qBAAqB,IAAI,IAAI,GAAGC,4BAA4B,GAAGf,SAAS,CAACW,OAAO,CAACG,qBAAqB,CAAC,CAAC,CAAC;;EAE7I,IAAI,EAAEA,qBAAqB,IAAI,CAAC,IAAIA,qBAAqB,IAAI,CAAC,CAAC,EAAE;IAC/D,MAAM,IAAIE,UAAU,CAAC,2DAA2D,CAAC;EACnF;EAEA,IAAIC,mBAAmB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EACrCD,mBAAmB,CAACE,cAAc,CAACV,IAAI,GAAG,CAAC,EAAE,CAAC,EAAEK,qBAAqB,CAAC;EACtEG,mBAAmB,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3C,IAAIC,eAAe,GAAGnB,cAAc,CAACe,mBAAmB,EAAEX,YAAY,CAAC;EACvE,IAAIgB,mBAAmB,GAAG,IAAIJ,IAAI,CAAC,CAAC,CAAC;EACrCI,mBAAmB,CAACH,cAAc,CAACV,IAAI,EAAE,CAAC,EAAEK,qBAAqB,CAAC;EAClEQ,mBAAmB,CAACF,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3C,IAAIG,eAAe,GAAGrB,cAAc,CAACoB,mBAAmB,EAAEhB,YAAY,CAAC;EAEvE,IAAIE,IAAI,CAACgB,OAAO,CAAC,CAAC,IAAIH,eAAe,CAACG,OAAO,CAAC,CAAC,EAAE;IAC/C,OAAOf,IAAI,GAAG,CAAC;EACjB,CAAC,MAAM,IAAID,IAAI,CAACgB,OAAO,CAAC,CAAC,IAAID,eAAe,CAACC,OAAO,CAAC,CAAC,EAAE;IACtD,OAAOf,IAAI;EACb,CAAC,MAAM;IACL,OAAOA,IAAI,GAAG,CAAC;EACjB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module"}