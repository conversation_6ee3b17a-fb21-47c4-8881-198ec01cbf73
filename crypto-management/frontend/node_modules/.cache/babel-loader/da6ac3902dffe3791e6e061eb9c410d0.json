{"ast": null, "code": "import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCWeek(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;\n  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);\n  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "requiredArgs", "startOfUTCWeek", "dirtyDate", "dirtyOptions", "arguments", "options", "locale", "localeWeekStartsOn", "weekStartsOn", "defaultWeekStartsOn", "RangeError", "date", "day", "getUTCDay", "diff", "setUTCDate", "getUTCDate", "setUTCHours"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/_lib/startOfUTCWeek/index.js"], "sourcesContent": ["import toInteger from \"../toInteger/index.js\";\nimport toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCWeek(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeWeekStartsOn = locale && locale.options && locale.options.weekStartsOn;\n  var defaultWeekStartsOn = localeWeekStartsOn == null ? 0 : toInteger(localeWeekStartsOn);\n  var weekStartsOn = options.weekStartsOn == null ? defaultWeekStartsOn : toInteger(options.weekStartsOn); // Test if weekStartsOn is between 0 and 6 _and_ is not NaN\n\n  if (!(weekStartsOn >= 0 && weekStartsOn <= 6)) {\n    throw new RangeError('weekStartsOn must be between 0 and 6 inclusively');\n  }\n\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,YAAY,MAAM,0BAA0B,CAAC,CAAC;AACrD;;AAEA,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC9DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,OAAO,GAAGF,YAAY,IAAI,CAAC,CAAC;EAChC,IAAIG,MAAM,GAAGD,OAAO,CAACC,MAAM;EAC3B,IAAIC,kBAAkB,GAAGD,MAAM,IAAIA,MAAM,CAACD,OAAO,IAAIC,MAAM,CAACD,OAAO,CAACG,YAAY;EAChF,IAAIC,mBAAmB,GAAGF,kBAAkB,IAAI,IAAI,GAAG,CAAC,GAAGT,SAAS,CAACS,kBAAkB,CAAC;EACxF,IAAIC,YAAY,GAAGH,OAAO,CAACG,YAAY,IAAI,IAAI,GAAGC,mBAAmB,GAAGX,SAAS,CAACO,OAAO,CAACG,YAAY,CAAC,CAAC,CAAC;;EAEzG,IAAI,EAAEA,YAAY,IAAI,CAAC,IAAIA,YAAY,IAAI,CAAC,CAAC,EAAE;IAC7C,MAAM,IAAIE,UAAU,CAAC,kDAAkD,CAAC;EAC1E;EAEA,IAAIC,IAAI,GAAGZ,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIU,GAAG,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;EAC1B,IAAIC,IAAI,GAAG,CAACF,GAAG,GAAGJ,YAAY,GAAG,CAAC,GAAG,CAAC,IAAII,GAAG,GAAGJ,YAAY;EAC5DG,IAAI,CAACI,UAAU,CAACJ,IAAI,CAACK,UAAU,CAAC,CAAC,GAAGF,IAAI,CAAC;EACzCH,IAAI,CAACM,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5B,OAAON,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}