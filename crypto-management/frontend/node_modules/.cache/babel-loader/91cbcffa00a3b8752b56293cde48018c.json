{"ast": null, "code": "import toInteger from \"../toInteger/index.js\";\nimport getUTCWeekYear from \"../getUTCWeekYear/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCWeekYear(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeFirstWeekContainsDate = locale && locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate);\n  var year = getUTCWeekYear(dirtyDate, dirtyOptions);\n  var firstWeek = new Date(0);\n  firstWeek.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCWeek(firstWeek, dirtyOptions);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "getUTCWeekYear", "startOfUTCWeek", "requiredArgs", "startOfUTCWeekYear", "dirtyDate", "dirtyOptions", "arguments", "options", "locale", "localeFirstWeekContainsDate", "firstWeekContainsDate", "defaultFirstWeekContainsDate", "year", "firstWeek", "Date", "setUTCFullYear", "setUTCHours", "date"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/_lib/startOfUTCWeekYear/index.js"], "sourcesContent": ["import toInteger from \"../toInteger/index.js\";\nimport getUTCWeekYear from \"../getUTCWeekYear/index.js\";\nimport startOfUTCWeek from \"../startOfUTCWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\"; // This function will be a part of public API when UTC function will be implemented.\n// See issue: https://github.com/date-fns/date-fns/issues/376\n\nexport default function startOfUTCWeekYear(dirtyDate, dirtyOptions) {\n  requiredArgs(1, arguments);\n  var options = dirtyOptions || {};\n  var locale = options.locale;\n  var localeFirstWeekContainsDate = locale && locale.options && locale.options.firstWeekContainsDate;\n  var defaultFirstWeekContainsDate = localeFirstWeekContainsDate == null ? 1 : toInteger(localeFirstWeekContainsDate);\n  var firstWeekContainsDate = options.firstWeekContainsDate == null ? defaultFirstWeekContainsDate : toInteger(options.firstWeekContainsDate);\n  var year = getUTCWeekYear(dirtyDate, dirtyOptions);\n  var firstWeek = new Date(0);\n  firstWeek.setUTCFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCWeek(firstWeek, dirtyOptions);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,YAAY,MAAM,0BAA0B,CAAC,CAAC;AACrD;;AAEA,eAAe,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAClEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,OAAO,GAAGF,YAAY,IAAI,CAAC,CAAC;EAChC,IAAIG,MAAM,GAAGD,OAAO,CAACC,MAAM;EAC3B,IAAIC,2BAA2B,GAAGD,MAAM,IAAIA,MAAM,CAACD,OAAO,IAAIC,MAAM,CAACD,OAAO,CAACG,qBAAqB;EAClG,IAAIC,4BAA4B,GAAGF,2BAA2B,IAAI,IAAI,GAAG,CAAC,GAAGV,SAAS,CAACU,2BAA2B,CAAC;EACnH,IAAIC,qBAAqB,GAAGH,OAAO,CAACG,qBAAqB,IAAI,IAAI,GAAGC,4BAA4B,GAAGZ,SAAS,CAACQ,OAAO,CAACG,qBAAqB,CAAC;EAC3I,IAAIE,IAAI,GAAGZ,cAAc,CAACI,SAAS,EAAEC,YAAY,CAAC;EAClD,IAAIQ,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC3BD,SAAS,CAACE,cAAc,CAACH,IAAI,EAAE,CAAC,EAAEF,qBAAqB,CAAC;EACxDG,SAAS,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACjC,IAAIC,IAAI,GAAGhB,cAAc,CAACY,SAAS,EAAER,YAAY,CAAC;EAClD,OAAOY,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}