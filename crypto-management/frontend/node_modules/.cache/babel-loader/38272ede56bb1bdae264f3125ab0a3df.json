{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { noCase } from \"no-case\";\nimport { upperCase } from \"upper-case\";\nexport function constantCase(input, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return noCase(input, __assign({\n    delimiter: \"_\",\n    transform: upperCase\n  }, options));\n}", "map": {"version": 3, "names": ["noCase", "upperCase", "constantCase", "input", "options", "__assign", "delimiter", "transform"], "sources": ["../src/index.ts"], "sourcesContent": ["import { noCase, Options } from \"no-case\";\nimport { upperCase } from \"upper-case\";\n\nexport { Options };\n\nexport function constantCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \"_\",\n    transform: upperCase,\n    ...options,\n  });\n}\n"], "mappings": ";AAAA,SAASA,MAAM,QAAiB,SAAS;AACzC,SAASC,SAAS,QAAQ,YAAY;AAItC,OAAM,SAAUC,YAAYA,CAACC,KAAa,EAAEC,OAAqB;EAArB,IAAAA,OAAA;IAAAA,OAAA,KAAqB;EAAA;EAC/D,OAAOJ,MAAM,CAACG,KAAK,EAAAE,QAAA;IACjBC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAEN;EAAS,GACjBG,OAAO,EACV;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}