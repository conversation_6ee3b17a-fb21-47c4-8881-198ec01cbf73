{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getDaysInMonth from \"../getDaysInMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} month - the month of the new date\n * @returns {Date} the new date with the month set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\n\nexport default function setMonth(dirtyDate, dirtyMonth) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var month = toInteger(dirtyMonth);\n  var year = date.getFullYear();\n  var day = date.getDate();\n  var dateWithDesiredMonth = new Date(0);\n  dateWithDesiredMonth.setFullYear(year, month, 15);\n  dateWithDesiredMonth.setHours(0, 0, 0, 0);\n  var daysInMonth = getDaysInMonth(dateWithDesiredMonth); // Set the last day of the new month\n  // if the original date was the last day of the longer month\n\n  date.setMonth(month, Math.min(day, daysInMonth));\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "getDaysInMonth", "requiredArgs", "setMonth", "dirtyDate", "<PERSON><PERSON><PERSON><PERSON>", "arguments", "date", "month", "year", "getFullYear", "day", "getDate", "date<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Date", "setFullYear", "setHours", "daysInMonth", "Math", "min"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/setMonth/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport getDaysInMonth from \"../getDaysInMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} month - the month of the new date\n * @returns {Date} the new date with the month set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\n\nexport default function setMonth(dirtyDate, dirtyMonth) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var month = toInteger(dirtyMonth);\n  var year = date.getFullYear();\n  var day = date.getDate();\n  var dateWithDesiredMonth = new Date(0);\n  dateWithDesiredMonth.setFullYear(year, month, 15);\n  dateWithDesiredMonth.setHours(0, 0, 0, 0);\n  var daysInMonth = getDaysInMonth(dateWithDesiredMonth); // Set the last day of the new month\n  // if the original date was the last day of the longer month\n\n  date.setMonth(month, Math.min(day, daysInMonth));\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,QAAQA,CAACC,SAAS,EAAEC,UAAU,EAAE;EACtDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGP,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAII,KAAK,GAAGT,SAAS,CAACM,UAAU,CAAC;EACjC,IAAII,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;EAC7B,IAAIC,GAAG,GAAGJ,IAAI,CAACK,OAAO,CAAC,CAAC;EACxB,IAAIC,oBAAoB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EACtCD,oBAAoB,CAACE,WAAW,CAACN,IAAI,EAAED,KAAK,EAAE,EAAE,CAAC;EACjDK,oBAAoB,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,IAAIC,WAAW,GAAGhB,cAAc,CAACY,oBAAoB,CAAC,CAAC,CAAC;EACxD;;EAEAN,IAAI,CAACJ,QAAQ,CAACK,KAAK,EAAEU,IAAI,CAACC,GAAG,CAACR,GAAG,EAAEM,WAAW,CAAC,CAAC;EAChD,OAAOV,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module"}