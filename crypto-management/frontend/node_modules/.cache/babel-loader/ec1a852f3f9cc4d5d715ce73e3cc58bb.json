{"ast": null, "code": "/**\n * Upper case the first character of an input string.\n */\nexport function upperCaseFirst(input) {\n  return input.charAt(0).toUpperCase() + input.substr(1);\n}", "map": {"version": 3, "names": ["upperCaseFirst", "input", "char<PERSON>t", "toUpperCase", "substr"], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * Upper case the first character of an input string.\n */\nexport function upperCaseFirst(input: string) {\n  return input.charAt(0).toUpperCase() + input.substr(1);\n}\n"], "mappings": "AAAA;;;AAGA,OAAM,SAAUA,cAAcA,CAACC,KAAa;EAC1C,OAAOA,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGF,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module"}