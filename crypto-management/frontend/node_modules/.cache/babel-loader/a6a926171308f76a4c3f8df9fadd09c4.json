{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { isArray } from 'zrender/lib/core/util.js';\nexport default function checkMarkerInSeries(seriesOpts, markerType) {\n  if (!seriesOpts) {\n    return false;\n  }\n  var seriesOptArr = isArray(seriesOpts) ? seriesOpts : [seriesOpts];\n  for (var idx = 0; idx < seriesOptArr.length; idx++) {\n    if (seriesOptArr[idx] && seriesOptArr[idx][markerType]) {\n      return true;\n    }\n  }\n  return false;\n}", "map": {"version": 3, "names": ["isArray", "checkMarkerInSeries", "seriesOpts", "markerType", "seriesOptArr", "idx", "length"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/echarts/lib/component/marker/checkMarkerInSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { isArray } from 'zrender/lib/core/util.js';\nexport default function checkMarkerInSeries(seriesOpts, markerType) {\n  if (!seriesOpts) {\n    return false;\n  }\n\n  var seriesOptArr = isArray(seriesOpts) ? seriesOpts : [seriesOpts];\n\n  for (var idx = 0; idx < seriesOptArr.length; idx++) {\n    if (seriesOptArr[idx] && seriesOptArr[idx][markerType]) {\n      return true;\n    }\n  }\n\n  return false;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,QAAQ,0BAA0B;AAClD,eAAe,SAASC,mBAAmBA,CAACC,UAAU,EAAEC,UAAU,EAAE;EAClE,IAAI,CAACD,UAAU,EAAE;IACf,OAAO,KAAK;EACd;EAEA,IAAIE,YAAY,GAAGJ,OAAO,CAACE,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;EAElE,KAAK,IAAIG,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGD,YAAY,CAACE,MAAM,EAAED,GAAG,EAAE,EAAE;IAClD,IAAID,YAAY,CAACC,GAAG,CAAC,IAAID,YAAY,CAACC,GAAG,CAAC,CAACF,UAAU,CAAC,EAAE;MACtD,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module"}