{"ast": null, "code": "/*\n\tJavaScript BigInteger library version 0.9.1\n\thttp://silentmatt.com/biginteger/\n\tCopyright (c) 2009 <PERSON> <<EMAIL>>\n\tCopyright (c) 2010,2011 by <PERSON> <<EMAIL>>\n\tLicensed under the MIT license.\n\tSupport for arbitrary internal representation base was added by\n\t<PERSON><PERSON>.\n*/\n\n/*\n\tFile: biginteger.js\n\tExports:\n\t\t<BigInteger>\n*/\n(function (exports) {\n  \"use strict\";\n\n  /*\n      Class: BigInteger\n      An arbitrarily-large integer.\n      <BigInteger> objects should be considered immutable. None of the \"built-in\"\n      methods modify *this* or their arguments. All properties should be\n      considered private.\n      All the methods of <BigInteger> instances can be called \"statically\". The\n      static versions are convenient if you don't already have a <BigInteger>\n      object.\n      As an example, these calls are equivalent.\n      > BigInteger(4).multiply(5); // returns BigInteger(20);\n      > BigInteger.multiply(4, 5); // returns BigInteger(20);\n      > var a = 42;\n      > var a = BigInteger.toJSValue(\"0b101010\"); // Not completely useless...\n  */\n  var CONSTRUCT = {}; // Unique token to call \"private\" version of constructor\n\n  /*\n      Constructor: BigInteger()\n      Convert a value to a <BigInteger>.\n      Although <BigInteger()> is the constructor for <BigInteger> objects, it is\n      best not to call it as a constructor. If *n* is a <BigInteger> object, it is\n      simply returned as-is. Otherwise, <BigInteger()> is equivalent to <parse>\n      without a radix argument.\n      > var n0 = BigInteger();      // Same as <BigInteger.ZERO>\n      > var n1 = BigInteger(\"123\"); // Create a new <BigInteger> with value 123\n      > var n2 = BigInteger(123);   // Create a new <BigInteger> with value 123\n      > var n3 = BigInteger(n2);    // Return n2, unchanged\n      The constructor form only takes an array and a sign. *n* must be an\n      array of numbers in little-endian order, where each digit is between 0\n      and BigInteger.base.  The second parameter sets the sign: -1 for\n      negative, +1 for positive, or 0 for zero. The array is *not copied and\n      may be modified*. If the array contains only zeros, the sign parameter\n      is ignored and is forced to zero.\n      > new BigInteger([5], -1): create a new BigInteger with value -5\n      Parameters:\n          n - Value to convert to a <BigInteger>.\n      Returns:\n          A <BigInteger> value.\n      See Also:\n          <parse>, <BigInteger>\n  */\n  function BigInteger(n, s, token) {\n    if (token !== CONSTRUCT) {\n      if (n instanceof BigInteger) {\n        return n;\n      } else if (typeof n === \"undefined\") {\n        return ZERO;\n      }\n      return BigInteger.parse(n);\n    }\n    n = n || []; // Provide the nullary constructor for subclasses.\n    while (n.length && !n[n.length - 1]) {\n      --n.length;\n    }\n    this._d = n;\n    this._s = n.length ? s || 1 : 0;\n  }\n  BigInteger._construct = function (n, s) {\n    return new BigInteger(n, s, CONSTRUCT);\n  };\n\n  // Base-10 speedup hacks in parse, toString, exp10 and log functions\n  // require base to be a power of 10. 10^7 is the largest such power\n  // that won't cause a precision loss when digits are multiplied.\n  var BigInteger_base = 10000000;\n  var BigInteger_base_log10 = 7;\n  BigInteger.base = BigInteger_base;\n  BigInteger.base_log10 = BigInteger_base_log10;\n  var ZERO = new BigInteger([], 0, CONSTRUCT);\n  // Constant: ZERO\n  // <BigInteger> 0.\n  BigInteger.ZERO = ZERO;\n  var ONE = new BigInteger([1], 1, CONSTRUCT);\n  // Constant: ONE\n  // <BigInteger> 1.\n  BigInteger.ONE = ONE;\n  var M_ONE = new BigInteger(ONE._d, -1, CONSTRUCT);\n  // Constant: M_ONE\n  // <BigInteger> -1.\n  BigInteger.M_ONE = M_ONE;\n\n  // Constant: _0\n  // Shortcut for <ZERO>.\n  BigInteger._0 = ZERO;\n\n  // Constant: _1\n  // Shortcut for <ONE>.\n  BigInteger._1 = ONE;\n\n  /*\n      Constant: small\n      Array of <BigIntegers> from 0 to 36.\n      These are used internally for parsing, but useful when you need a \"small\"\n      <BigInteger>.\n      See Also:\n          <ZERO>, <ONE>, <_0>, <_1>\n  */\n  BigInteger.small = [ZERO, ONE, /* Assuming BigInteger_base > 36 */\n  new BigInteger([2], 1, CONSTRUCT), new BigInteger([3], 1, CONSTRUCT), new BigInteger([4], 1, CONSTRUCT), new BigInteger([5], 1, CONSTRUCT), new BigInteger([6], 1, CONSTRUCT), new BigInteger([7], 1, CONSTRUCT), new BigInteger([8], 1, CONSTRUCT), new BigInteger([9], 1, CONSTRUCT), new BigInteger([10], 1, CONSTRUCT), new BigInteger([11], 1, CONSTRUCT), new BigInteger([12], 1, CONSTRUCT), new BigInteger([13], 1, CONSTRUCT), new BigInteger([14], 1, CONSTRUCT), new BigInteger([15], 1, CONSTRUCT), new BigInteger([16], 1, CONSTRUCT), new BigInteger([17], 1, CONSTRUCT), new BigInteger([18], 1, CONSTRUCT), new BigInteger([19], 1, CONSTRUCT), new BigInteger([20], 1, CONSTRUCT), new BigInteger([21], 1, CONSTRUCT), new BigInteger([22], 1, CONSTRUCT), new BigInteger([23], 1, CONSTRUCT), new BigInteger([24], 1, CONSTRUCT), new BigInteger([25], 1, CONSTRUCT), new BigInteger([26], 1, CONSTRUCT), new BigInteger([27], 1, CONSTRUCT), new BigInteger([28], 1, CONSTRUCT), new BigInteger([29], 1, CONSTRUCT), new BigInteger([30], 1, CONSTRUCT), new BigInteger([31], 1, CONSTRUCT), new BigInteger([32], 1, CONSTRUCT), new BigInteger([33], 1, CONSTRUCT), new BigInteger([34], 1, CONSTRUCT), new BigInteger([35], 1, CONSTRUCT), new BigInteger([36], 1, CONSTRUCT)];\n\n  // Used for parsing/radix conversion\n  BigInteger.digits = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ\".split(\"\");\n\n  /*\n      Method: toString\n      Convert a <BigInteger> to a string.\n      When *base* is greater than 10, letters are upper case.\n      Parameters:\n          base - Optional base to represent the number in (default is base 10).\n                 Must be between 2 and 36 inclusive, or an Error will be thrown.\n      Returns:\n          The string representation of the <BigInteger>.\n  */\n  BigInteger.prototype.toString = function (base) {\n    base = +base || 10;\n    if (base < 2 || base > 36) {\n      throw new Error(\"illegal radix \" + base + \".\");\n    }\n    if (this._s === 0) {\n      return \"0\";\n    }\n    if (base === 10) {\n      var str = this._s < 0 ? \"-\" : \"\";\n      str += this._d[this._d.length - 1].toString();\n      for (var i = this._d.length - 2; i >= 0; i--) {\n        var group = this._d[i].toString();\n        while (group.length < BigInteger_base_log10) group = '0' + group;\n        str += group;\n      }\n      return str;\n    } else {\n      var numerals = BigInteger.digits;\n      base = BigInteger.small[base];\n      var sign = this._s;\n      var n = this.abs();\n      var digits = [];\n      var digit;\n      while (n._s !== 0) {\n        var divmod = n.divRem(base);\n        n = divmod[0];\n        digit = divmod[1];\n        // TODO: This could be changed to unshift instead of reversing at the end.\n        // Benchmark both to compare speeds.\n        digits.push(numerals[digit.valueOf()]);\n      }\n      return (sign < 0 ? \"-\" : \"\") + digits.reverse().join(\"\");\n    }\n  };\n\n  // Verify strings for parsing\n  BigInteger.radixRegex = [/^$/, /^$/, /^[01]*$/, /^[012]*$/, /^[0-3]*$/, /^[0-4]*$/, /^[0-5]*$/, /^[0-6]*$/, /^[0-7]*$/, /^[0-8]*$/, /^[0-9]*$/, /^[0-9aA]*$/, /^[0-9abAB]*$/, /^[0-9abcABC]*$/, /^[0-9a-dA-D]*$/, /^[0-9a-eA-E]*$/, /^[0-9a-fA-F]*$/, /^[0-9a-gA-G]*$/, /^[0-9a-hA-H]*$/, /^[0-9a-iA-I]*$/, /^[0-9a-jA-J]*$/, /^[0-9a-kA-K]*$/, /^[0-9a-lA-L]*$/, /^[0-9a-mA-M]*$/, /^[0-9a-nA-N]*$/, /^[0-9a-oA-O]*$/, /^[0-9a-pA-P]*$/, /^[0-9a-qA-Q]*$/, /^[0-9a-rA-R]*$/, /^[0-9a-sA-S]*$/, /^[0-9a-tA-T]*$/, /^[0-9a-uA-U]*$/, /^[0-9a-vA-V]*$/, /^[0-9a-wA-W]*$/, /^[0-9a-xA-X]*$/, /^[0-9a-yA-Y]*$/, /^[0-9a-zA-Z]*$/];\n\n  /*\n      Function: parse\n      Parse a string into a <BigInteger>.\n      *base* is optional but, if provided, must be from 2 to 36 inclusive. If\n      *base* is not provided, it will be guessed based on the leading characters\n      of *s* as follows:\n      - \"0x\" or \"0X\": *base* = 16\n      - \"0c\" or \"0C\": *base* = 8\n      - \"0b\" or \"0B\": *base* = 2\n      - else: *base* = 10\n      If no base is provided, or *base* is 10, the number can be in exponential\n      form. For example, these are all valid:\n      > BigInteger.parse(\"1e9\");              // Same as \"1000000000\"\n      > BigInteger.parse(\"1.234*10^3\");       // Same as 1234\n      > BigInteger.parse(\"56789 * 10 ** -2\"); // Same as 567\n      If any characters fall outside the range defined by the radix, an exception\n      will be thrown.\n      Parameters:\n          s - The string to parse.\n          base - Optional radix (default is to guess based on *s*).\n      Returns:\n          a <BigInteger> instance.\n  */\n  BigInteger.parse = function (s, base) {\n    // Expands a number in exponential form to decimal form.\n    // expandExponential(\"-13.441*10^5\") === \"1344100\";\n    // expandExponential(\"1.12300e-1\") === \"0.112300\";\n    // expandExponential(1000000000000000000000000000000) === \"1000000000000000000000000000000\";\n    function expandExponential(str) {\n      str = str.replace(/\\s*[*xX]\\s*10\\s*(\\^|\\*\\*)\\s*/, \"e\");\n      return str.replace(/^([+\\-])?(\\d+)\\.?(\\d*)[eE]([+\\-]?\\d+)$/, function (x, s, n, f, c) {\n        c = +c;\n        var l = c < 0;\n        var i = n.length + c;\n        x = (l ? n : f).length;\n        c = (c = Math.abs(c)) >= x ? c - x + l : 0;\n        var z = new Array(c + 1).join(\"0\");\n        var r = n + f;\n        return (s || \"\") + (l ? r = z + r : r += z).substr(0, i += l ? z.length : 0) + (i < r.length ? \".\" + r.substr(i) : \"\");\n      });\n    }\n    s = s.toString();\n    if (typeof base === \"undefined\" || +base === 10) {\n      s = expandExponential(s);\n    }\n    var prefixRE;\n    if (typeof base === \"undefined\") {\n      prefixRE = '0[xcb]';\n    } else if (base == 16) {\n      prefixRE = '0x';\n    } else if (base == 8) {\n      prefixRE = '0c';\n    } else if (base == 2) {\n      prefixRE = '0b';\n    } else {\n      prefixRE = '';\n    }\n    var parts = new RegExp('^([+\\\\-]?)(' + prefixRE + ')?([0-9a-z]*)(?:\\\\.\\\\d*)?$', 'i').exec(s);\n    if (parts) {\n      var sign = parts[1] || \"+\";\n      var baseSection = parts[2] || \"\";\n      var digits = parts[3] || \"\";\n      if (typeof base === \"undefined\") {\n        // Guess base\n        if (baseSection === \"0x\" || baseSection === \"0X\") {\n          // Hex\n          base = 16;\n        } else if (baseSection === \"0c\" || baseSection === \"0C\") {\n          // Octal\n          base = 8;\n        } else if (baseSection === \"0b\" || baseSection === \"0B\") {\n          // Binary\n          base = 2;\n        } else {\n          base = 10;\n        }\n      } else if (base < 2 || base > 36) {\n        throw new Error(\"Illegal radix \" + base + \".\");\n      }\n      base = +base;\n\n      // Check for digits outside the range\n      if (!BigInteger.radixRegex[base].test(digits)) {\n        throw new Error(\"Bad digit for radix \" + base);\n      }\n\n      // Strip leading zeros, and convert to array\n      digits = digits.replace(/^0+/, \"\").split(\"\");\n      if (digits.length === 0) {\n        return ZERO;\n      }\n\n      // Get the sign (we know it's not zero)\n      sign = sign === \"-\" ? -1 : 1;\n\n      // Optimize 10\n      if (base == 10) {\n        var d = [];\n        while (digits.length >= BigInteger_base_log10) {\n          d.push(parseInt(digits.splice(digits.length - BigInteger.base_log10, BigInteger.base_log10).join(''), 10));\n        }\n        d.push(parseInt(digits.join(''), 10));\n        return new BigInteger(d, sign, CONSTRUCT);\n      }\n\n      // Do the conversion\n      var d = ZERO;\n      base = BigInteger.small[base];\n      var small = BigInteger.small;\n      for (var i = 0; i < digits.length; i++) {\n        d = d.multiply(base).add(small[parseInt(digits[i], 36)]);\n      }\n      return new BigInteger(d._d, sign, CONSTRUCT);\n    } else {\n      throw new Error(\"Invalid BigInteger format: \" + s);\n    }\n  };\n\n  /*\n      Function: add\n      Add two <BigIntegers>.\n      Parameters:\n          n - The number to add to *this*. Will be converted to a <BigInteger>.\n      Returns:\n          The numbers added together.\n      See Also:\n          <subtract>, <multiply>, <quotient>, <next>\n  */\n  BigInteger.prototype.add = function (n) {\n    if (this._s === 0) {\n      return BigInteger(n);\n    }\n    n = BigInteger(n);\n    if (n._s === 0) {\n      return this;\n    }\n    if (this._s !== n._s) {\n      n = n.negate();\n      return this.subtract(n);\n    }\n    var a = this._d;\n    var b = n._d;\n    var al = a.length;\n    var bl = b.length;\n    var sum = new Array(Math.max(al, bl) + 1);\n    var size = Math.min(al, bl);\n    var carry = 0;\n    var digit;\n    for (var i = 0; i < size; i++) {\n      digit = a[i] + b[i] + carry;\n      sum[i] = digit % BigInteger_base;\n      carry = digit / BigInteger_base | 0;\n    }\n    if (bl > al) {\n      a = b;\n      al = bl;\n    }\n    for (i = size; carry && i < al; i++) {\n      digit = a[i] + carry;\n      sum[i] = digit % BigInteger_base;\n      carry = digit / BigInteger_base | 0;\n    }\n    if (carry) {\n      sum[i] = carry;\n    }\n    for (; i < al; i++) {\n      sum[i] = a[i];\n    }\n    return new BigInteger(sum, this._s, CONSTRUCT);\n  };\n\n  /*\n      Function: negate\n      Get the additive inverse of a <BigInteger>.\n      Returns:\n          A <BigInteger> with the same magnatude, but with the opposite sign.\n      See Also:\n          <abs>\n  */\n  BigInteger.prototype.negate = function () {\n    return new BigInteger(this._d, -this._s | 0, CONSTRUCT);\n  };\n\n  /*\n      Function: abs\n      Get the absolute value of a <BigInteger>.\n      Returns:\n          A <BigInteger> with the same magnatude, but always positive (or zero).\n      See Also:\n          <negate>\n  */\n  BigInteger.prototype.abs = function () {\n    return this._s < 0 ? this.negate() : this;\n  };\n\n  /*\n      Function: subtract\n      Subtract two <BigIntegers>.\n      Parameters:\n          n - The number to subtract from *this*. Will be converted to a <BigInteger>.\n      Returns:\n          The *n* subtracted from *this*.\n      See Also:\n          <add>, <multiply>, <quotient>, <prev>\n  */\n  BigInteger.prototype.subtract = function (n) {\n    if (this._s === 0) {\n      return BigInteger(n).negate();\n    }\n    n = BigInteger(n);\n    if (n._s === 0) {\n      return this;\n    }\n    if (this._s !== n._s) {\n      n = n.negate();\n      return this.add(n);\n    }\n    var m = this;\n    // negative - negative => -|a| - -|b| => -|a| + |b| => |b| - |a|\n    if (this._s < 0) {\n      m = new BigInteger(n._d, 1, CONSTRUCT);\n      n = new BigInteger(this._d, 1, CONSTRUCT);\n    }\n\n    // Both are positive => a - b\n    var sign = m.compareAbs(n);\n    if (sign === 0) {\n      return ZERO;\n    } else if (sign < 0) {\n      // swap m and n\n      var t = n;\n      n = m;\n      m = t;\n    }\n\n    // a > b\n    var a = m._d;\n    var b = n._d;\n    var al = a.length;\n    var bl = b.length;\n    var diff = new Array(al); // al >= bl since a > b\n    var borrow = 0;\n    var i;\n    var digit;\n    for (i = 0; i < bl; i++) {\n      digit = a[i] - borrow - b[i];\n      if (digit < 0) {\n        digit += BigInteger_base;\n        borrow = 1;\n      } else {\n        borrow = 0;\n      }\n      diff[i] = digit;\n    }\n    for (i = bl; i < al; i++) {\n      digit = a[i] - borrow;\n      if (digit < 0) {\n        digit += BigInteger_base;\n      } else {\n        diff[i++] = digit;\n        break;\n      }\n      diff[i] = digit;\n    }\n    for (; i < al; i++) {\n      diff[i] = a[i];\n    }\n    return new BigInteger(diff, sign, CONSTRUCT);\n  };\n  (function () {\n    function addOne(n, sign) {\n      var a = n._d;\n      var sum = a.slice();\n      var carry = true;\n      var i = 0;\n      while (true) {\n        var digit = (a[i] || 0) + 1;\n        sum[i] = digit % BigInteger_base;\n        if (digit <= BigInteger_base - 1) {\n          break;\n        }\n        ++i;\n      }\n      return new BigInteger(sum, sign, CONSTRUCT);\n    }\n    function subtractOne(n, sign) {\n      var a = n._d;\n      var sum = a.slice();\n      var borrow = true;\n      var i = 0;\n      while (true) {\n        var digit = (a[i] || 0) - 1;\n        if (digit < 0) {\n          sum[i] = digit + BigInteger_base;\n        } else {\n          sum[i] = digit;\n          break;\n        }\n        ++i;\n      }\n      return new BigInteger(sum, sign, CONSTRUCT);\n    }\n\n    /*\n        Function: next\n        Get the next <BigInteger> (add one).\n        Returns:\n            *this* + 1.\n        See Also:\n            <add>, <prev>\n    */\n    BigInteger.prototype.next = function () {\n      switch (this._s) {\n        case 0:\n          return ONE;\n        case -1:\n          return subtractOne(this, -1);\n        // case 1:\n        default:\n          return addOne(this, 1);\n      }\n    };\n\n    /*\n        Function: prev\n        Get the previous <BigInteger> (subtract one).\n        Returns:\n            *this* - 1.\n        See Also:\n            <next>, <subtract>\n    */\n    BigInteger.prototype.prev = function () {\n      switch (this._s) {\n        case 0:\n          return M_ONE;\n        case -1:\n          return addOne(this, -1);\n        // case 1:\n        default:\n          return subtractOne(this, 1);\n      }\n    };\n  })();\n\n  /*\n      Function: compareAbs\n      Compare the absolute value of two <BigIntegers>.\n      Calling <compareAbs> is faster than calling <abs> twice, then <compare>.\n      Parameters:\n          n - The number to compare to *this*. Will be converted to a <BigInteger>.\n      Returns:\n          -1, 0, or +1 if *|this|* is less than, equal to, or greater than *|n|*.\n      See Also:\n          <compare>, <abs>\n  */\n  BigInteger.prototype.compareAbs = function (n) {\n    if (this === n) {\n      return 0;\n    }\n    if (!(n instanceof BigInteger)) {\n      if (!isFinite(n)) {\n        return isNaN(n) ? n : -1;\n      }\n      n = BigInteger(n);\n    }\n    if (this._s === 0) {\n      return n._s !== 0 ? -1 : 0;\n    }\n    if (n._s === 0) {\n      return 1;\n    }\n    var l = this._d.length;\n    var nl = n._d.length;\n    if (l < nl) {\n      return -1;\n    } else if (l > nl) {\n      return 1;\n    }\n    var a = this._d;\n    var b = n._d;\n    for (var i = l - 1; i >= 0; i--) {\n      if (a[i] !== b[i]) {\n        return a[i] < b[i] ? -1 : 1;\n      }\n    }\n    return 0;\n  };\n\n  /*\n      Function: compare\n      Compare two <BigIntegers>.\n      Parameters:\n          n - The number to compare to *this*. Will be converted to a <BigInteger>.\n      Returns:\n          -1, 0, or +1 if *this* is less than, equal to, or greater than *n*.\n      See Also:\n          <compareAbs>, <isPositive>, <isNegative>, <isUnit>\n  */\n  BigInteger.prototype.compare = function (n) {\n    if (this === n) {\n      return 0;\n    }\n    n = BigInteger(n);\n    if (this._s === 0) {\n      return -n._s;\n    }\n    if (this._s === n._s) {\n      // both positive or both negative\n      var cmp = this.compareAbs(n);\n      return cmp * this._s;\n    } else {\n      return this._s;\n    }\n  };\n\n  /*\n      Function: isUnit\n      Return true iff *this* is either 1 or -1.\n      Returns:\n          true if *this* compares equal to <BigInteger.ONE> or <BigInteger.M_ONE>.\n      See Also:\n          <isZero>, <isNegative>, <isPositive>, <compareAbs>, <compare>,\n          <BigInteger.ONE>, <BigInteger.M_ONE>\n  */\n  BigInteger.prototype.isUnit = function () {\n    return this === ONE || this === M_ONE || this._d.length === 1 && this._d[0] === 1;\n  };\n\n  /*\n      Function: multiply\n      Multiply two <BigIntegers>.\n      Parameters:\n          n - The number to multiply *this* by. Will be converted to a\n          <BigInteger>.\n      Returns:\n          The numbers multiplied together.\n      See Also:\n          <add>, <subtract>, <quotient>, <square>\n  */\n  BigInteger.prototype.multiply = function (n) {\n    // TODO: Consider adding Karatsuba multiplication for large numbers\n    if (this._s === 0) {\n      return ZERO;\n    }\n    n = BigInteger(n);\n    if (n._s === 0) {\n      return ZERO;\n    }\n    if (this.isUnit()) {\n      if (this._s < 0) {\n        return n.negate();\n      }\n      return n;\n    }\n    if (n.isUnit()) {\n      if (n._s < 0) {\n        return this.negate();\n      }\n      return this;\n    }\n    if (this === n) {\n      return this.square();\n    }\n    var r = this._d.length >= n._d.length;\n    var a = (r ? this : n)._d; // a will be longer than b\n    var b = (r ? n : this)._d;\n    var al = a.length;\n    var bl = b.length;\n    var pl = al + bl;\n    var partial = new Array(pl);\n    var i;\n    for (i = 0; i < pl; i++) {\n      partial[i] = 0;\n    }\n    for (i = 0; i < bl; i++) {\n      var carry = 0;\n      var bi = b[i];\n      var jlimit = al + i;\n      var digit;\n      for (var j = i; j < jlimit; j++) {\n        digit = partial[j] + bi * a[j - i] + carry;\n        carry = digit / BigInteger_base | 0;\n        partial[j] = digit % BigInteger_base | 0;\n      }\n      if (carry) {\n        digit = partial[j] + carry;\n        carry = digit / BigInteger_base | 0;\n        partial[j] = digit % BigInteger_base;\n      }\n    }\n    return new BigInteger(partial, this._s * n._s, CONSTRUCT);\n  };\n\n  // Multiply a BigInteger by a single-digit native number\n  // Assumes that this and n are >= 0\n  // This is not really intended to be used outside the library itself\n  BigInteger.prototype.multiplySingleDigit = function (n) {\n    if (n === 0 || this._s === 0) {\n      return ZERO;\n    }\n    if (n === 1) {\n      return this;\n    }\n    var digit;\n    if (this._d.length === 1) {\n      digit = this._d[0] * n;\n      if (digit >= BigInteger_base) {\n        return new BigInteger([digit % BigInteger_base | 0, digit / BigInteger_base | 0], 1, CONSTRUCT);\n      }\n      return new BigInteger([digit], 1, CONSTRUCT);\n    }\n    if (n === 2) {\n      return this.add(this);\n    }\n    if (this.isUnit()) {\n      return new BigInteger([n], 1, CONSTRUCT);\n    }\n    var a = this._d;\n    var al = a.length;\n    var pl = al + 1;\n    var partial = new Array(pl);\n    for (var i = 0; i < pl; i++) {\n      partial[i] = 0;\n    }\n    var carry = 0;\n    for (var j = 0; j < al; j++) {\n      digit = n * a[j] + carry;\n      carry = digit / BigInteger_base | 0;\n      partial[j] = digit % BigInteger_base | 0;\n    }\n    if (carry) {\n      partial[j] = carry;\n    }\n    return new BigInteger(partial, 1, CONSTRUCT);\n  };\n\n  /*\n      Function: square\n      Multiply a <BigInteger> by itself.\n      This is slightly faster than regular multiplication, since it removes the\n      duplicated multiplcations.\n      Returns:\n          > this.multiply(this)\n      See Also:\n          <multiply>\n  */\n  BigInteger.prototype.square = function () {\n    // Normally, squaring a 10-digit number would take 100 multiplications.\n    // Of these 10 are unique diagonals, of the remaining 90 (100-10), 45 are repeated.\n    // This procedure saves (N*(N-1))/2 multiplications, (e.g., 45 of 100 multiplies).\n    // Based on code by Gary Darby, Intellitech Systems Inc., www.DelphiForFun.org\n\n    if (this._s === 0) {\n      return ZERO;\n    }\n    if (this.isUnit()) {\n      return ONE;\n    }\n    var digits = this._d;\n    var length = digits.length;\n    var imult1 = new Array(length + length + 1);\n    var product, carry, k;\n    var i;\n\n    // Calculate diagonal\n    for (i = 0; i < length; i++) {\n      k = i * 2;\n      product = digits[i] * digits[i];\n      carry = product / BigInteger_base | 0;\n      imult1[k] = product % BigInteger_base;\n      imult1[k + 1] = carry;\n    }\n\n    // Calculate repeating part\n    for (i = 0; i < length; i++) {\n      carry = 0;\n      k = i * 2 + 1;\n      for (var j = i + 1; j < length; j++, k++) {\n        product = digits[j] * digits[i] * 2 + imult1[k] + carry;\n        carry = product / BigInteger_base | 0;\n        imult1[k] = product % BigInteger_base;\n      }\n      k = length + i;\n      var digit = carry + imult1[k];\n      carry = digit / BigInteger_base | 0;\n      imult1[k] = digit % BigInteger_base;\n      imult1[k + 1] += carry;\n    }\n    return new BigInteger(imult1, 1, CONSTRUCT);\n  };\n\n  /*\n      Function: quotient\n      Divide two <BigIntegers> and truncate towards zero.\n      <quotient> throws an exception if *n* is zero.\n      Parameters:\n          n - The number to divide *this* by. Will be converted to a <BigInteger>.\n      Returns:\n          The *this* / *n*, truncated to an integer.\n      See Also:\n          <add>, <subtract>, <multiply>, <divRem>, <remainder>\n  */\n  BigInteger.prototype.quotient = function (n) {\n    return this.divRem(n)[0];\n  };\n\n  /*\n      Function: divide\n      Deprecated synonym for <quotient>.\n  */\n  BigInteger.prototype.divide = BigInteger.prototype.quotient;\n\n  /*\n      Function: remainder\n      Calculate the remainder of two <BigIntegers>.\n      <remainder> throws an exception if *n* is zero.\n      Parameters:\n          n - The remainder after *this* is divided *this* by *n*. Will be\n              converted to a <BigInteger>.\n      Returns:\n          *this* % *n*.\n      See Also:\n          <divRem>, <quotient>\n  */\n  BigInteger.prototype.remainder = function (n) {\n    return this.divRem(n)[1];\n  };\n\n  /*\n      Function: divRem\n      Calculate the integer quotient and remainder of two <BigIntegers>.\n      <divRem> throws an exception if *n* is zero.\n      Parameters:\n          n - The number to divide *this* by. Will be converted to a <BigInteger>.\n      Returns:\n          A two-element array containing the quotient and the remainder.\n          > a.divRem(b)\n          is exactly equivalent to\n          > [a.quotient(b), a.remainder(b)]\n          except it is faster, because they are calculated at the same time.\n      See Also:\n          <quotient>, <remainder>\n  */\n  BigInteger.prototype.divRem = function (n) {\n    n = BigInteger(n);\n    if (n._s === 0) {\n      throw new Error(\"Divide by zero\");\n    }\n    if (this._s === 0) {\n      return [ZERO, ZERO];\n    }\n    if (n._d.length === 1) {\n      return this.divRemSmall(n._s * n._d[0]);\n    }\n\n    // Test for easy cases -- |n1| <= |n2|\n    switch (this.compareAbs(n)) {\n      case 0:\n        // n1 == n2\n        return [this._s === n._s ? ONE : M_ONE, ZERO];\n      case -1:\n        // |n1| < |n2|\n        return [ZERO, this];\n    }\n    var sign = this._s * n._s;\n    var a = n.abs();\n    var b_digits = this._d;\n    var b_index = b_digits.length;\n    var digits = n._d.length;\n    var quot = [];\n    var guess;\n    var part = new BigInteger([], 0, CONSTRUCT);\n    while (b_index) {\n      part._d.unshift(b_digits[--b_index]);\n      part = new BigInteger(part._d, 1, CONSTRUCT);\n      if (part.compareAbs(n) < 0) {\n        quot.push(0);\n        continue;\n      }\n      if (part._s === 0) {\n        guess = 0;\n      } else {\n        var xlen = part._d.length,\n          ylen = a._d.length;\n        var highx = part._d[xlen - 1] * BigInteger_base + part._d[xlen - 2];\n        var highy = a._d[ylen - 1] * BigInteger_base + a._d[ylen - 2];\n        if (part._d.length > a._d.length) {\n          // The length of part._d can either match a._d length,\n          // or exceed it by one.\n          highx = (highx + 1) * BigInteger_base;\n        }\n        guess = Math.ceil(highx / highy);\n      }\n      do {\n        var check = a.multiplySingleDigit(guess);\n        if (check.compareAbs(part) <= 0) {\n          break;\n        }\n        guess--;\n      } while (guess);\n      quot.push(guess);\n      if (!guess) {\n        continue;\n      }\n      var diff = part.subtract(check);\n      part._d = diff._d.slice();\n    }\n    return [new BigInteger(quot.reverse(), sign, CONSTRUCT), new BigInteger(part._d, this._s, CONSTRUCT)];\n  };\n\n  // Throws an exception if n is outside of (-BigInteger.base, -1] or\n  // [1, BigInteger.base).  It's not necessary to call this, since the\n  // other division functions will call it if they are able to.\n  BigInteger.prototype.divRemSmall = function (n) {\n    var r;\n    n = +n;\n    if (n === 0) {\n      throw new Error(\"Divide by zero\");\n    }\n    var n_s = n < 0 ? -1 : 1;\n    var sign = this._s * n_s;\n    n = Math.abs(n);\n    if (n < 1 || n >= BigInteger_base) {\n      throw new Error(\"Argument out of range\");\n    }\n    if (this._s === 0) {\n      return [ZERO, ZERO];\n    }\n    if (n === 1 || n === -1) {\n      return [sign === 1 ? this.abs() : new BigInteger(this._d, sign, CONSTRUCT), ZERO];\n    }\n\n    // 2 <= n < BigInteger_base\n\n    // divide a single digit by a single digit\n    if (this._d.length === 1) {\n      var q = new BigInteger([this._d[0] / n | 0], 1, CONSTRUCT);\n      r = new BigInteger([this._d[0] % n | 0], 1, CONSTRUCT);\n      if (sign < 0) {\n        q = q.negate();\n      }\n      if (this._s < 0) {\n        r = r.negate();\n      }\n      return [q, r];\n    }\n    var digits = this._d.slice();\n    var quot = new Array(digits.length);\n    var part = 0;\n    var diff = 0;\n    var i = 0;\n    var guess;\n    while (digits.length) {\n      part = part * BigInteger_base + digits[digits.length - 1];\n      if (part < n) {\n        quot[i++] = 0;\n        digits.pop();\n        diff = BigInteger_base * diff + part;\n        continue;\n      }\n      if (part === 0) {\n        guess = 0;\n      } else {\n        guess = part / n | 0;\n      }\n      var check = n * guess;\n      diff = part - check;\n      quot[i++] = guess;\n      if (!guess) {\n        digits.pop();\n        continue;\n      }\n      digits.pop();\n      part = diff;\n    }\n    r = new BigInteger([diff], 1, CONSTRUCT);\n    if (this._s < 0) {\n      r = r.negate();\n    }\n    return [new BigInteger(quot.reverse(), sign, CONSTRUCT), r];\n  };\n\n  /*\n      Function: isEven\n      Return true iff *this* is divisible by two.\n      Note that <BigInteger.ZERO> is even.\n      Returns:\n          true if *this* is even, false otherwise.\n      See Also:\n          <isOdd>\n  */\n  BigInteger.prototype.isEven = function () {\n    var digits = this._d;\n    return this._s === 0 || digits.length === 0 || digits[0] % 2 === 0;\n  };\n\n  /*\n      Function: isOdd\n      Return true iff *this* is not divisible by two.\n      Returns:\n          true if *this* is odd, false otherwise.\n      See Also:\n          <isEven>\n  */\n  BigInteger.prototype.isOdd = function () {\n    return !this.isEven();\n  };\n\n  /*\n      Function: sign\n      Get the sign of a <BigInteger>.\n      Returns:\n          * -1 if *this* < 0\n          * 0 if *this* == 0\n          * +1 if *this* > 0\n      See Also:\n          <isZero>, <isPositive>, <isNegative>, <compare>, <BigInteger.ZERO>\n  */\n  BigInteger.prototype.sign = function () {\n    return this._s;\n  };\n\n  /*\n      Function: isPositive\n      Return true iff *this* > 0.\n      Returns:\n          true if *this*.compare(<BigInteger.ZERO>) == 1.\n      See Also:\n          <sign>, <isZero>, <isNegative>, <isUnit>, <compare>, <BigInteger.ZERO>\n  */\n  BigInteger.prototype.isPositive = function () {\n    return this._s > 0;\n  };\n\n  /*\n      Function: isNegative\n      Return true iff *this* < 0.\n      Returns:\n          true if *this*.compare(<BigInteger.ZERO>) == -1.\n      See Also:\n          <sign>, <isPositive>, <isZero>, <isUnit>, <compare>, <BigInteger.ZERO>\n  */\n  BigInteger.prototype.isNegative = function () {\n    return this._s < 0;\n  };\n\n  /*\n      Function: isZero\n      Return true iff *this* == 0.\n      Returns:\n          true if *this*.compare(<BigInteger.ZERO>) == 0.\n      See Also:\n          <sign>, <isPositive>, <isNegative>, <isUnit>, <BigInteger.ZERO>\n  */\n  BigInteger.prototype.isZero = function () {\n    return this._s === 0;\n  };\n\n  /*\n      Function: exp10\n      Multiply a <BigInteger> by a power of 10.\n      This is equivalent to, but faster than\n      > if (n >= 0) {\n      >     return this.multiply(BigInteger(\"1e\" + n));\n      > }\n      > else { // n <= 0\n      >     return this.quotient(BigInteger(\"1e\" + -n));\n      > }\n      Parameters:\n          n - The power of 10 to multiply *this* by. *n* is converted to a\n          javascipt number and must be no greater than <BigInteger.MAX_EXP>\n          (0x7FFFFFFF), or an exception will be thrown.\n      Returns:\n          *this* * (10 ** *n*), truncated to an integer if necessary.\n      See Also:\n          <pow>, <multiply>\n  */\n  BigInteger.prototype.exp10 = function (n) {\n    n = +n;\n    if (n === 0) {\n      return this;\n    }\n    if (Math.abs(n) > Number(MAX_EXP)) {\n      throw new Error(\"exponent too large in BigInteger.exp10\");\n    }\n    // Optimization for this == 0. This also keeps us from having to trim zeros in the positive n case\n    if (this._s === 0) {\n      return ZERO;\n    }\n    if (n > 0) {\n      var k = new BigInteger(this._d.slice(), this._s, CONSTRUCT);\n      for (; n >= BigInteger_base_log10; n -= BigInteger_base_log10) {\n        k._d.unshift(0);\n      }\n      if (n == 0) return k;\n      k._s = 1;\n      k = k.multiplySingleDigit(Math.pow(10, n));\n      return this._s < 0 ? k.negate() : k;\n    } else if (-n >= this._d.length * BigInteger_base_log10) {\n      return ZERO;\n    } else {\n      var k = new BigInteger(this._d.slice(), this._s, CONSTRUCT);\n      for (n = -n; n >= BigInteger_base_log10; n -= BigInteger_base_log10) {\n        k._d.shift();\n      }\n      return n == 0 ? k : k.divRemSmall(Math.pow(10, n))[0];\n    }\n  };\n\n  /*\n      Function: pow\n      Raise a <BigInteger> to a power.\n      In this implementation, 0**0 is 1.\n      Parameters:\n          n - The exponent to raise *this* by. *n* must be no greater than\n          <BigInteger.MAX_EXP> (0x7FFFFFFF), or an exception will be thrown.\n      Returns:\n          *this* raised to the *nth* power.\n      See Also:\n          <modPow>\n  */\n  BigInteger.prototype.pow = function (n) {\n    if (this.isUnit()) {\n      if (this._s > 0) {\n        return this;\n      } else {\n        return BigInteger(n).isOdd() ? this : this.negate();\n      }\n    }\n    n = BigInteger(n);\n    if (n._s === 0) {\n      return ONE;\n    } else if (n._s < 0) {\n      if (this._s === 0) {\n        throw new Error(\"Divide by zero\");\n      } else {\n        return ZERO;\n      }\n    }\n    if (this._s === 0) {\n      return ZERO;\n    }\n    if (n.isUnit()) {\n      return this;\n    }\n    if (n.compareAbs(MAX_EXP) > 0) {\n      throw new Error(\"exponent too large in BigInteger.pow\");\n    }\n    var x = this;\n    var aux = ONE;\n    var two = BigInteger.small[2];\n    while (n.isPositive()) {\n      if (n.isOdd()) {\n        aux = aux.multiply(x);\n        if (n.isUnit()) {\n          return aux;\n        }\n      }\n      x = x.square();\n      n = n.quotient(two);\n    }\n    return aux;\n  };\n\n  /*\n      Function: modPow\n      Raise a <BigInteger> to a power (mod m).\n      Because it is reduced by a modulus, <modPow> is not limited by\n      <BigInteger.MAX_EXP> like <pow>.\n      Parameters:\n          exponent - The exponent to raise *this* by. Must be positive.\n          modulus - The modulus.\n      Returns:\n          *this* ^ *exponent* (mod *modulus*).\n      See Also:\n          <pow>, <mod>\n  */\n  BigInteger.prototype.modPow = function (exponent, modulus) {\n    var result = ONE;\n    var base = this;\n    while (exponent.isPositive()) {\n      if (exponent.isOdd()) {\n        result = result.multiply(base).remainder(modulus);\n      }\n      exponent = exponent.quotient(BigInteger.small[2]);\n      if (exponent.isPositive()) {\n        base = base.square().remainder(modulus);\n      }\n    }\n    return result;\n  };\n\n  /*\n      Function: log\n      Get the natural logarithm of a <BigInteger> as a native JavaScript number.\n      This is equivalent to\n      > Math.log(this.toJSValue())\n      but handles values outside of the native number range.\n      Returns:\n          log( *this* )\n      See Also:\n          <toJSValue>\n  */\n  BigInteger.prototype.log = function () {\n    switch (this._s) {\n      case 0:\n        return -Infinity;\n      case -1:\n        return NaN;\n      default: // Fall through.\n    }\n    var l = this._d.length;\n    if (l * BigInteger_base_log10 < 30) {\n      return Math.log(this.valueOf());\n    }\n    var N = Math.ceil(30 / BigInteger_base_log10);\n    var firstNdigits = this._d.slice(l - N);\n    return Math.log(new BigInteger(firstNdigits, 1, CONSTRUCT).valueOf()) + (l - N) * Math.log(BigInteger_base);\n  };\n\n  /*\n      Function: valueOf\n      Convert a <BigInteger> to a native JavaScript integer.\n      This is called automatically by JavaScipt to convert a <BigInteger> to a\n      native value.\n      Returns:\n          > parseInt(this.toString(), 10)\n      See Also:\n          <toString>, <toJSValue>\n  */\n  BigInteger.prototype.valueOf = function () {\n    return parseInt(this.toString(), 10);\n  };\n\n  /*\n      Function: toJSValue\n      Convert a <BigInteger> to a native JavaScript integer.\n      This is the same as valueOf, but more explicitly named.\n      Returns:\n          > parseInt(this.toString(), 10)\n      See Also:\n          <toString>, <valueOf>\n  */\n  BigInteger.prototype.toJSValue = function () {\n    return parseInt(this.toString(), 10);\n  };\n\n  /*\n   Function: lowVal\n   Author: Lucas Jones\n   */\n  BigInteger.prototype.lowVal = function () {\n    return this._d[0] || 0;\n  };\n  var MAX_EXP = BigInteger(0x7FFFFFFF);\n  // Constant: MAX_EXP\n  // The largest exponent allowed in <pow> and <exp10> (0x7FFFFFFF or 2147483647).\n  BigInteger.MAX_EXP = MAX_EXP;\n  (function () {\n    function makeUnary(fn) {\n      return function (a) {\n        return fn.call(BigInteger(a));\n      };\n    }\n    function makeBinary(fn) {\n      return function (a, b) {\n        return fn.call(BigInteger(a), BigInteger(b));\n      };\n    }\n    function makeTrinary(fn) {\n      return function (a, b, c) {\n        return fn.call(BigInteger(a), BigInteger(b), BigInteger(c));\n      };\n    }\n    (function () {\n      var i, fn;\n      var unary = \"toJSValue,isEven,isOdd,sign,isZero,isNegative,abs,isUnit,square,negate,isPositive,toString,next,prev,log\".split(\",\");\n      var binary = \"compare,remainder,divRem,subtract,add,quotient,divide,multiply,pow,compareAbs\".split(\",\");\n      var trinary = [\"modPow\"];\n      for (i = 0; i < unary.length; i++) {\n        fn = unary[i];\n        BigInteger[fn] = makeUnary(BigInteger.prototype[fn]);\n      }\n      for (i = 0; i < binary.length; i++) {\n        fn = binary[i];\n        BigInteger[fn] = makeBinary(BigInteger.prototype[fn]);\n      }\n      for (i = 0; i < trinary.length; i++) {\n        fn = trinary[i];\n        BigInteger[fn] = makeTrinary(BigInteger.prototype[fn]);\n      }\n      BigInteger.exp10 = function (x, n) {\n        return BigInteger(x).exp10(n);\n      };\n    })();\n  })();\n  exports.JSBigInt = BigInteger; // exports.BigInteger changed to exports.JSBigInt\n})(typeof exports !== 'undefined' ? exports : this);", "map": {"version": 3, "names": ["exports", "CONSTRUCT", "BigInteger", "n", "s", "token", "ZERO", "parse", "length", "_d", "_s", "_construct", "BigInteger_base", "BigInteger_base_log10", "base", "base_log10", "ONE", "M_ONE", "_0", "_1", "small", "digits", "split", "prototype", "toString", "Error", "str", "i", "group", "numerals", "sign", "abs", "digit", "divmod", "divRem", "push", "valueOf", "reverse", "join", "radixRegex", "expandExponential", "replace", "x", "f", "c", "l", "Math", "z", "Array", "r", "substr", "prefixRE", "parts", "RegExp", "exec", "baseSection", "test", "d", "parseInt", "splice", "multiply", "add", "negate", "subtract", "a", "b", "al", "bl", "sum", "max", "size", "min", "carry", "m", "compareAbs", "t", "diff", "borrow", "addOne", "slice", "subtractOne", "next", "prev", "isFinite", "isNaN", "nl", "compare", "cmp", "isUnit", "square", "pl", "partial", "bi", "jlimit", "j", "multiplySingleDigit", "imult1", "product", "k", "quotient", "divide", "remainder", "divRemSmall", "b_digits", "b_index", "quot", "guess", "part", "unshift", "xlen", "ylen", "highx", "highy", "ceil", "check", "n_s", "q", "pop", "isEven", "isOdd", "isPositive", "isNegative", "isZero", "exp10", "Number", "MAX_EXP", "pow", "shift", "aux", "two", "modPow", "exponent", "modulus", "result", "log", "Infinity", "NaN", "N", "firstNdigits", "toJSValue", "lowVal", "makeUnary", "fn", "call", "makeBinary", "makeTrinary", "unary", "binary", "trinary", "JSBigInt"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/multicoin-address-validator/src/crypto/biginteger.js"], "sourcesContent": ["/*\n\tJavaScript BigInteger library version 0.9.1\n\thttp://silentmatt.com/biginteger/\n\tCopyright (c) 2009 <PERSON> <<EMAIL>>\n\tCopyright (c) 2010,2011 by <PERSON> <<EMAIL>>\n\tLicensed under the MIT license.\n\tSupport for arbitrary internal representation base was added by\n\t<PERSON><PERSON>.\n*/\n\n/*\n\tFile: biginteger.js\n\tExports:\n\t\t<BigInteger>\n*/\n(function(exports) {\n    \"use strict\";\n    /*\n        Class: BigInteger\n        An arbitrarily-large integer.\n        <BigInteger> objects should be considered immutable. None of the \"built-in\"\n        methods modify *this* or their arguments. All properties should be\n        considered private.\n        All the methods of <BigInteger> instances can be called \"statically\". The\n        static versions are convenient if you don't already have a <BigInteger>\n        object.\n        As an example, these calls are equivalent.\n        > BigInteger(4).multiply(5); // returns BigInteger(20);\n        > BigInteger.multiply(4, 5); // returns BigInteger(20);\n        > var a = 42;\n        > var a = BigInteger.toJSValue(\"0b101010\"); // Not completely useless...\n    */\n    \n    var CONSTRUCT = {}; // Unique token to call \"private\" version of constructor\n    \n    /*\n        Constructor: BigInteger()\n        Convert a value to a <BigInteger>.\n        Although <BigInteger()> is the constructor for <BigInteger> objects, it is\n        best not to call it as a constructor. If *n* is a <BigInteger> object, it is\n        simply returned as-is. Otherwise, <BigInteger()> is equivalent to <parse>\n        without a radix argument.\n        > var n0 = BigInteger();      // Same as <BigInteger.ZERO>\n        > var n1 = BigInteger(\"123\"); // Create a new <BigInteger> with value 123\n        > var n2 = BigInteger(123);   // Create a new <BigInteger> with value 123\n        > var n3 = BigInteger(n2);    // Return n2, unchanged\n        The constructor form only takes an array and a sign. *n* must be an\n        array of numbers in little-endian order, where each digit is between 0\n        and BigInteger.base.  The second parameter sets the sign: -1 for\n        negative, +1 for positive, or 0 for zero. The array is *not copied and\n        may be modified*. If the array contains only zeros, the sign parameter\n        is ignored and is forced to zero.\n        > new BigInteger([5], -1): create a new BigInteger with value -5\n        Parameters:\n            n - Value to convert to a <BigInteger>.\n        Returns:\n            A <BigInteger> value.\n        See Also:\n            <parse>, <BigInteger>\n    */\n    function BigInteger(n, s, token) {\n        \n        if (token !== CONSTRUCT) {\n            if (n instanceof BigInteger) {\n                return n;\n            }\n            else if (typeof n === \"undefined\") {\n                return ZERO;\n            }\n            return BigInteger.parse(n);\n        }\n    \n        n = n || [];  // Provide the nullary constructor for subclasses.\n        while (n.length && !n[n.length - 1]) {\n            --n.length;\n        }\n        this._d = n;\n        this._s = n.length ? (s || 1) : 0;\n    }\n    \n    BigInteger._construct = function(n, s) {\n        return new BigInteger(n, s, CONSTRUCT);\n    };\n    \n    // Base-10 speedup hacks in parse, toString, exp10 and log functions\n    // require base to be a power of 10. 10^7 is the largest such power\n    // that won't cause a precision loss when digits are multiplied.\n    var BigInteger_base = 10000000;\n    var BigInteger_base_log10 = 7;\n    \n    BigInteger.base = BigInteger_base;\n    BigInteger.base_log10 = BigInteger_base_log10;\n    \n    var ZERO = new BigInteger([], 0, CONSTRUCT);\n    // Constant: ZERO\n    // <BigInteger> 0.\n    BigInteger.ZERO = ZERO;\n    \n    var ONE = new BigInteger([1], 1, CONSTRUCT);\n    // Constant: ONE\n    // <BigInteger> 1.\n    BigInteger.ONE = ONE;\n    \n    var M_ONE = new BigInteger(ONE._d, -1, CONSTRUCT);\n    // Constant: M_ONE\n    // <BigInteger> -1.\n    BigInteger.M_ONE = M_ONE;\n    \n    // Constant: _0\n    // Shortcut for <ZERO>.\n    BigInteger._0 = ZERO;\n    \n    // Constant: _1\n    // Shortcut for <ONE>.\n    BigInteger._1 = ONE;\n    \n    /*\n        Constant: small\n        Array of <BigIntegers> from 0 to 36.\n        These are used internally for parsing, but useful when you need a \"small\"\n        <BigInteger>.\n        See Also:\n            <ZERO>, <ONE>, <_0>, <_1>\n    */\n    BigInteger.small = [\n        ZERO,\n        ONE,\n        /* Assuming BigInteger_base > 36 */\n        new BigInteger( [2], 1, CONSTRUCT),\n        new BigInteger( [3], 1, CONSTRUCT),\n        new BigInteger( [4], 1, CONSTRUCT),\n        new BigInteger( [5], 1, CONSTRUCT),\n        new BigInteger( [6], 1, CONSTRUCT),\n        new BigInteger( [7], 1, CONSTRUCT),\n        new BigInteger( [8], 1, CONSTRUCT),\n        new BigInteger( [9], 1, CONSTRUCT),\n        new BigInteger([10], 1, CONSTRUCT),\n        new BigInteger([11], 1, CONSTRUCT),\n        new BigInteger([12], 1, CONSTRUCT),\n        new BigInteger([13], 1, CONSTRUCT),\n        new BigInteger([14], 1, CONSTRUCT),\n        new BigInteger([15], 1, CONSTRUCT),\n        new BigInteger([16], 1, CONSTRUCT),\n        new BigInteger([17], 1, CONSTRUCT),\n        new BigInteger([18], 1, CONSTRUCT),\n        new BigInteger([19], 1, CONSTRUCT),\n        new BigInteger([20], 1, CONSTRUCT),\n        new BigInteger([21], 1, CONSTRUCT),\n        new BigInteger([22], 1, CONSTRUCT),\n        new BigInteger([23], 1, CONSTRUCT),\n        new BigInteger([24], 1, CONSTRUCT),\n        new BigInteger([25], 1, CONSTRUCT),\n        new BigInteger([26], 1, CONSTRUCT),\n        new BigInteger([27], 1, CONSTRUCT),\n        new BigInteger([28], 1, CONSTRUCT),\n        new BigInteger([29], 1, CONSTRUCT),\n        new BigInteger([30], 1, CONSTRUCT),\n        new BigInteger([31], 1, CONSTRUCT),\n        new BigInteger([32], 1, CONSTRUCT),\n        new BigInteger([33], 1, CONSTRUCT),\n        new BigInteger([34], 1, CONSTRUCT),\n        new BigInteger([35], 1, CONSTRUCT),\n        new BigInteger([36], 1, CONSTRUCT)\n    ];\n    \n    // Used for parsing/radix conversion\n    BigInteger.digits = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ\".split(\"\");\n    \n    /*\n        Method: toString\n        Convert a <BigInteger> to a string.\n        When *base* is greater than 10, letters are upper case.\n        Parameters:\n            base - Optional base to represent the number in (default is base 10).\n                   Must be between 2 and 36 inclusive, or an Error will be thrown.\n        Returns:\n            The string representation of the <BigInteger>.\n    */\n    BigInteger.prototype.toString = function(base) {\n        base = +base || 10;\n        if (base < 2 || base > 36) {\n            throw new Error(\"illegal radix \" + base + \".\");\n        }\n        if (this._s === 0) {\n            return \"0\";\n        }\n        if (base === 10) {\n            var str = this._s < 0 ? \"-\" : \"\";\n            str += this._d[this._d.length - 1].toString();\n            for (var i = this._d.length - 2; i >= 0; i--) {\n                var group = this._d[i].toString();\n                while (group.length < BigInteger_base_log10) group = '0' + group;\n                str += group;\n            }\n            return str;\n        }\n        else {\n            var numerals = BigInteger.digits;\n            base = BigInteger.small[base];\n            var sign = this._s;\n    \n            var n = this.abs();\n            var digits = [];\n            var digit;\n    \n            while (n._s !== 0) {\n                var divmod = n.divRem(base);\n                n = divmod[0];\n                digit = divmod[1];\n                // TODO: This could be changed to unshift instead of reversing at the end.\n                // Benchmark both to compare speeds.\n                digits.push(numerals[digit.valueOf()]);\n            }\n            return (sign < 0 ? \"-\" : \"\") + digits.reverse().join(\"\");\n        }\n    };\n    \n    // Verify strings for parsing\n    BigInteger.radixRegex = [\n        /^$/,\n        /^$/,\n        /^[01]*$/,\n        /^[012]*$/,\n        /^[0-3]*$/,\n        /^[0-4]*$/,\n        /^[0-5]*$/,\n        /^[0-6]*$/,\n        /^[0-7]*$/,\n        /^[0-8]*$/,\n        /^[0-9]*$/,\n        /^[0-9aA]*$/,\n        /^[0-9abAB]*$/,\n        /^[0-9abcABC]*$/,\n        /^[0-9a-dA-D]*$/,\n        /^[0-9a-eA-E]*$/,\n        /^[0-9a-fA-F]*$/,\n        /^[0-9a-gA-G]*$/,\n        /^[0-9a-hA-H]*$/,\n        /^[0-9a-iA-I]*$/,\n        /^[0-9a-jA-J]*$/,\n        /^[0-9a-kA-K]*$/,\n        /^[0-9a-lA-L]*$/,\n        /^[0-9a-mA-M]*$/,\n        /^[0-9a-nA-N]*$/,\n        /^[0-9a-oA-O]*$/,\n        /^[0-9a-pA-P]*$/,\n        /^[0-9a-qA-Q]*$/,\n        /^[0-9a-rA-R]*$/,\n        /^[0-9a-sA-S]*$/,\n        /^[0-9a-tA-T]*$/,\n        /^[0-9a-uA-U]*$/,\n        /^[0-9a-vA-V]*$/,\n        /^[0-9a-wA-W]*$/,\n        /^[0-9a-xA-X]*$/,\n        /^[0-9a-yA-Y]*$/,\n        /^[0-9a-zA-Z]*$/\n    ];\n    \n    /*\n        Function: parse\n        Parse a string into a <BigInteger>.\n        *base* is optional but, if provided, must be from 2 to 36 inclusive. If\n        *base* is not provided, it will be guessed based on the leading characters\n        of *s* as follows:\n        - \"0x\" or \"0X\": *base* = 16\n        - \"0c\" or \"0C\": *base* = 8\n        - \"0b\" or \"0B\": *base* = 2\n        - else: *base* = 10\n        If no base is provided, or *base* is 10, the number can be in exponential\n        form. For example, these are all valid:\n        > BigInteger.parse(\"1e9\");              // Same as \"1000000000\"\n        > BigInteger.parse(\"1.234*10^3\");       // Same as 1234\n        > BigInteger.parse(\"56789 * 10 ** -2\"); // Same as 567\n        If any characters fall outside the range defined by the radix, an exception\n        will be thrown.\n        Parameters:\n            s - The string to parse.\n            base - Optional radix (default is to guess based on *s*).\n        Returns:\n            a <BigInteger> instance.\n    */\n    BigInteger.parse = function(s, base) {\n        // Expands a number in exponential form to decimal form.\n        // expandExponential(\"-13.441*10^5\") === \"1344100\";\n        // expandExponential(\"1.12300e-1\") === \"0.112300\";\n        // expandExponential(1000000000000000000000000000000) === \"1000000000000000000000000000000\";\n        function expandExponential(str) {\n            str = str.replace(/\\s*[*xX]\\s*10\\s*(\\^|\\*\\*)\\s*/, \"e\");\n    \n            return str.replace(/^([+\\-])?(\\d+)\\.?(\\d*)[eE]([+\\-]?\\d+)$/, function(x, s, n, f, c) {\n                c = +c;\n                var l = c < 0;\n                var i = n.length + c;\n                x = (l ? n : f).length;\n                c = ((c = Math.abs(c)) >= x ? c - x + l : 0);\n                var z = (new Array(c + 1)).join(\"0\");\n                var r = n + f;\n                return (s || \"\") + (l ? r = z + r : r += z).substr(0, i += l ? z.length : 0) + (i < r.length ? \".\" + r.substr(i) : \"\");\n            });\n        }\n    \n        s = s.toString();\n        if (typeof base === \"undefined\" || +base === 10) {\n            s = expandExponential(s);\n        }\n    \n        var prefixRE;\n        if (typeof base === \"undefined\") {\n            prefixRE = '0[xcb]';\n        }\n        else if (base == 16) {\n            prefixRE = '0x';\n        }\n        else if (base == 8) {\n            prefixRE = '0c';\n        }\n        else if (base == 2) {\n            prefixRE = '0b';\n        }\n        else {\n            prefixRE = '';\n        }\n        var parts = new RegExp('^([+\\\\-]?)(' + prefixRE + ')?([0-9a-z]*)(?:\\\\.\\\\d*)?$', 'i').exec(s);\n        if (parts) {\n            var sign = parts[1] || \"+\";\n            var baseSection = parts[2] || \"\";\n            var digits = parts[3] || \"\";\n    \n            if (typeof base === \"undefined\") {\n                // Guess base\n                if (baseSection === \"0x\" || baseSection === \"0X\") { // Hex\n                    base = 16;\n                }\n                else if (baseSection === \"0c\" || baseSection === \"0C\") { // Octal\n                    base = 8;\n                }\n                else if (baseSection === \"0b\" || baseSection === \"0B\") { // Binary\n                    base = 2;\n                }\n                else {\n                    base = 10;\n                }\n            }\n            else if (base < 2 || base > 36) {\n                throw new Error(\"Illegal radix \" + base + \".\");\n            }\n    \n            base = +base;\n    \n            // Check for digits outside the range\n            if (!(BigInteger.radixRegex[base].test(digits))) {\n                throw new Error(\"Bad digit for radix \" + base);\n            }\n    \n            // Strip leading zeros, and convert to array\n            digits = digits.replace(/^0+/, \"\").split(\"\");\n            if (digits.length === 0) {\n                return ZERO;\n            }\n    \n            // Get the sign (we know it's not zero)\n            sign = (sign === \"-\") ? -1 : 1;\n    \n            // Optimize 10\n            if (base == 10) {\n                var d = [];\n                while (digits.length >= BigInteger_base_log10) {\n                    d.push(parseInt(digits.splice(digits.length-BigInteger.base_log10, BigInteger.base_log10).join(''), 10));\n                }\n                d.push(parseInt(digits.join(''), 10));\n                return new BigInteger(d, sign, CONSTRUCT);\n            }\n    \n            // Do the conversion\n            var d = ZERO;\n            base = BigInteger.small[base];\n            var small = BigInteger.small;\n            for (var i = 0; i < digits.length; i++) {\n                d = d.multiply(base).add(small[parseInt(digits[i], 36)]);\n            }\n            return new BigInteger(d._d, sign, CONSTRUCT);\n        }\n        else {\n            throw new Error(\"Invalid BigInteger format: \" + s);\n        }\n    };\n    \n    /*\n        Function: add\n        Add two <BigIntegers>.\n        Parameters:\n            n - The number to add to *this*. Will be converted to a <BigInteger>.\n        Returns:\n            The numbers added together.\n        See Also:\n            <subtract>, <multiply>, <quotient>, <next>\n    */\n    BigInteger.prototype.add = function(n) {\n        if (this._s === 0) {\n            return BigInteger(n);\n        }\n    \n        n = BigInteger(n);\n        if (n._s === 0) {\n            return this;\n        }\n        if (this._s !== n._s) {\n            n = n.negate();\n            return this.subtract(n);\n        }\n    \n        var a = this._d;\n        var b = n._d;\n        var al = a.length;\n        var bl = b.length;\n        var sum = new Array(Math.max(al, bl) + 1);\n        var size = Math.min(al, bl);\n        var carry = 0;\n        var digit;\n    \n        for (var i = 0; i < size; i++) {\n            digit = a[i] + b[i] + carry;\n            sum[i] = digit % BigInteger_base;\n            carry = (digit / BigInteger_base) | 0;\n        }\n        if (bl > al) {\n            a = b;\n            al = bl;\n        }\n        for (i = size; carry && i < al; i++) {\n            digit = a[i] + carry;\n            sum[i] = digit % BigInteger_base;\n            carry = (digit / BigInteger_base) | 0;\n        }\n        if (carry) {\n            sum[i] = carry;\n        }\n    \n        for ( ; i < al; i++) {\n            sum[i] = a[i];\n        }\n    \n        return new BigInteger(sum, this._s, CONSTRUCT);\n    };\n    \n    /*\n        Function: negate\n        Get the additive inverse of a <BigInteger>.\n        Returns:\n            A <BigInteger> with the same magnatude, but with the opposite sign.\n        See Also:\n            <abs>\n    */\n    BigInteger.prototype.negate = function() {\n        return new BigInteger(this._d, (-this._s) | 0, CONSTRUCT);\n    };\n    \n    /*\n        Function: abs\n        Get the absolute value of a <BigInteger>.\n        Returns:\n            A <BigInteger> with the same magnatude, but always positive (or zero).\n        See Also:\n            <negate>\n    */\n    BigInteger.prototype.abs = function() {\n        return (this._s < 0) ? this.negate() : this;\n    };\n    \n    /*\n        Function: subtract\n        Subtract two <BigIntegers>.\n        Parameters:\n            n - The number to subtract from *this*. Will be converted to a <BigInteger>.\n        Returns:\n            The *n* subtracted from *this*.\n        See Also:\n            <add>, <multiply>, <quotient>, <prev>\n    */\n    BigInteger.prototype.subtract = function(n) {\n        if (this._s === 0) {\n            return BigInteger(n).negate();\n        }\n    \n        n = BigInteger(n);\n        if (n._s === 0) {\n            return this;\n        }\n        if (this._s !== n._s) {\n            n = n.negate();\n            return this.add(n);\n        }\n    \n        var m = this;\n        // negative - negative => -|a| - -|b| => -|a| + |b| => |b| - |a|\n        if (this._s < 0) {\n            m = new BigInteger(n._d, 1, CONSTRUCT);\n            n = new BigInteger(this._d, 1, CONSTRUCT);\n        }\n    \n        // Both are positive => a - b\n        var sign = m.compareAbs(n);\n        if (sign === 0) {\n            return ZERO;\n        }\n        else if (sign < 0) {\n            // swap m and n\n            var t = n;\n            n = m;\n            m = t;\n        }\n    \n        // a > b\n        var a = m._d;\n        var b = n._d;\n        var al = a.length;\n        var bl = b.length;\n        var diff = new Array(al); // al >= bl since a > b\n        var borrow = 0;\n        var i;\n        var digit;\n    \n        for (i = 0; i < bl; i++) {\n            digit = a[i] - borrow - b[i];\n            if (digit < 0) {\n                digit += BigInteger_base;\n                borrow = 1;\n            }\n            else {\n                borrow = 0;\n            }\n            diff[i] = digit;\n        }\n        for (i = bl; i < al; i++) {\n            digit = a[i] - borrow;\n            if (digit < 0) {\n                digit += BigInteger_base;\n            }\n            else {\n                diff[i++] = digit;\n                break;\n            }\n            diff[i] = digit;\n        }\n        for ( ; i < al; i++) {\n            diff[i] = a[i];\n        }\n    \n        return new BigInteger(diff, sign, CONSTRUCT);\n    };\n    \n    (function() {\n        function addOne(n, sign) {\n            var a = n._d;\n            var sum = a.slice();\n            var carry = true;\n            var i = 0;\n    \n            while (true) {\n                var digit = (a[i] || 0) + 1;\n                sum[i] = digit % BigInteger_base;\n                if (digit <= BigInteger_base - 1) {\n                    break;\n                }\n                ++i;\n            }\n    \n            return new BigInteger(sum, sign, CONSTRUCT);\n        }\n    \n        function subtractOne(n, sign) {\n            var a = n._d;\n            var sum = a.slice();\n            var borrow = true;\n            var i = 0;\n    \n            while (true) {\n                var digit = (a[i] || 0) - 1;\n                if (digit < 0) {\n                    sum[i] = digit + BigInteger_base;\n                }\n                else {\n                    sum[i] = digit;\n                    break;\n                }\n                ++i;\n            }\n    \n            return new BigInteger(sum, sign, CONSTRUCT);\n        }\n    \n        /*\n            Function: next\n            Get the next <BigInteger> (add one).\n            Returns:\n                *this* + 1.\n            See Also:\n                <add>, <prev>\n        */\n        BigInteger.prototype.next = function() {\n            switch (this._s) {\n            case 0:\n                return ONE;\n            case -1:\n                return subtractOne(this, -1);\n            // case 1:\n            default:\n                return addOne(this, 1);\n            }\n        };\n    \n        /*\n            Function: prev\n            Get the previous <BigInteger> (subtract one).\n            Returns:\n                *this* - 1.\n            See Also:\n                <next>, <subtract>\n        */\n        BigInteger.prototype.prev = function() {\n            switch (this._s) {\n            case 0:\n                return M_ONE;\n            case -1:\n                return addOne(this, -1);\n            // case 1:\n            default:\n                return subtractOne(this, 1);\n            }\n        };\n    })();\n    \n    /*\n        Function: compareAbs\n        Compare the absolute value of two <BigIntegers>.\n        Calling <compareAbs> is faster than calling <abs> twice, then <compare>.\n        Parameters:\n            n - The number to compare to *this*. Will be converted to a <BigInteger>.\n        Returns:\n            -1, 0, or +1 if *|this|* is less than, equal to, or greater than *|n|*.\n        See Also:\n            <compare>, <abs>\n    */\n    BigInteger.prototype.compareAbs = function(n) {\n        if (this === n) {\n            return 0;\n        }\n    \n        if (!(n instanceof BigInteger)) {\n            if (!isFinite(n)) {\n                return(isNaN(n) ? n : -1);\n            }\n            n = BigInteger(n);\n        }\n    \n        if (this._s === 0) {\n            return (n._s !== 0) ? -1 : 0;\n        }\n        if (n._s === 0) {\n            return 1;\n        }\n    \n        var l = this._d.length;\n        var nl = n._d.length;\n        if (l < nl) {\n            return -1;\n        }\n        else if (l > nl) {\n            return 1;\n        }\n    \n        var a = this._d;\n        var b = n._d;\n        for (var i = l-1; i >= 0; i--) {\n            if (a[i] !== b[i]) {\n                return a[i] < b[i] ? -1 : 1;\n            }\n        }\n    \n        return 0;\n    };\n    \n    /*\n        Function: compare\n        Compare two <BigIntegers>.\n        Parameters:\n            n - The number to compare to *this*. Will be converted to a <BigInteger>.\n        Returns:\n            -1, 0, or +1 if *this* is less than, equal to, or greater than *n*.\n        See Also:\n            <compareAbs>, <isPositive>, <isNegative>, <isUnit>\n    */\n    BigInteger.prototype.compare = function(n) {\n        if (this === n) {\n            return 0;\n        }\n    \n        n = BigInteger(n);\n    \n        if (this._s === 0) {\n            return -n._s;\n        }\n    \n        if (this._s === n._s) { // both positive or both negative\n            var cmp = this.compareAbs(n);\n            return cmp * this._s;\n        }\n        else {\n            return this._s;\n        }\n    };\n    \n    /*\n        Function: isUnit\n        Return true iff *this* is either 1 or -1.\n        Returns:\n            true if *this* compares equal to <BigInteger.ONE> or <BigInteger.M_ONE>.\n        See Also:\n            <isZero>, <isNegative>, <isPositive>, <compareAbs>, <compare>,\n            <BigInteger.ONE>, <BigInteger.M_ONE>\n    */\n    BigInteger.prototype.isUnit = function() {\n        return this === ONE ||\n            this === M_ONE ||\n            (this._d.length === 1 && this._d[0] === 1);\n    };\n    \n    /*\n        Function: multiply\n        Multiply two <BigIntegers>.\n        Parameters:\n            n - The number to multiply *this* by. Will be converted to a\n            <BigInteger>.\n        Returns:\n            The numbers multiplied together.\n        See Also:\n            <add>, <subtract>, <quotient>, <square>\n    */\n    BigInteger.prototype.multiply = function(n) {\n        // TODO: Consider adding Karatsuba multiplication for large numbers\n        if (this._s === 0) {\n            return ZERO;\n        }\n    \n        n = BigInteger(n);\n        if (n._s === 0) {\n            return ZERO;\n        }\n        if (this.isUnit()) {\n            if (this._s < 0) {\n                return n.negate();\n            }\n            return n;\n        }\n        if (n.isUnit()) {\n            if (n._s < 0) {\n                return this.negate();\n            }\n            return this;\n        }\n        if (this === n) {\n            return this.square();\n        }\n    \n        var r = (this._d.length >= n._d.length);\n        var a = (r ? this : n)._d; // a will be longer than b\n        var b = (r ? n : this)._d;\n        var al = a.length;\n        var bl = b.length;\n    \n        var pl = al + bl;\n        var partial = new Array(pl);\n        var i;\n        for (i = 0; i < pl; i++) {\n            partial[i] = 0;\n        }\n    \n        for (i = 0; i < bl; i++) {\n            var carry = 0;\n            var bi = b[i];\n            var jlimit = al + i;\n            var digit;\n            for (var j = i; j < jlimit; j++) {\n                digit = partial[j] + bi * a[j - i] + carry;\n                carry = (digit / BigInteger_base) | 0;\n                partial[j] = (digit % BigInteger_base) | 0;\n            }\n            if (carry) {\n                digit = partial[j] + carry;\n                carry = (digit / BigInteger_base) | 0;\n                partial[j] = digit % BigInteger_base;\n            }\n        }\n        return new BigInteger(partial, this._s * n._s, CONSTRUCT);\n    };\n    \n    // Multiply a BigInteger by a single-digit native number\n    // Assumes that this and n are >= 0\n    // This is not really intended to be used outside the library itself\n    BigInteger.prototype.multiplySingleDigit = function(n) {\n        if (n === 0 || this._s === 0) {\n            return ZERO;\n        }\n        if (n === 1) {\n            return this;\n        }\n    \n        var digit;\n        if (this._d.length === 1) {\n            digit = this._d[0] * n;\n            if (digit >= BigInteger_base) {\n                return new BigInteger([(digit % BigInteger_base)|0,\n                        (digit / BigInteger_base)|0], 1, CONSTRUCT);\n            }\n            return new BigInteger([digit], 1, CONSTRUCT);\n        }\n    \n        if (n === 2) {\n            return this.add(this);\n        }\n        if (this.isUnit()) {\n            return new BigInteger([n], 1, CONSTRUCT);\n        }\n    \n        var a = this._d;\n        var al = a.length;\n    \n        var pl = al + 1;\n        var partial = new Array(pl);\n        for (var i = 0; i < pl; i++) {\n            partial[i] = 0;\n        }\n    \n        var carry = 0;\n        for (var j = 0; j < al; j++) {\n            digit = n * a[j] + carry;\n            carry = (digit / BigInteger_base) | 0;\n            partial[j] = (digit % BigInteger_base) | 0;\n        }\n        if (carry) {\n            partial[j] = carry;\n        }\n    \n        return new BigInteger(partial, 1, CONSTRUCT);\n    };\n    \n    /*\n        Function: square\n        Multiply a <BigInteger> by itself.\n        This is slightly faster than regular multiplication, since it removes the\n        duplicated multiplcations.\n        Returns:\n            > this.multiply(this)\n        See Also:\n            <multiply>\n    */\n    BigInteger.prototype.square = function() {\n        // Normally, squaring a 10-digit number would take 100 multiplications.\n        // Of these 10 are unique diagonals, of the remaining 90 (100-10), 45 are repeated.\n        // This procedure saves (N*(N-1))/2 multiplications, (e.g., 45 of 100 multiplies).\n        // Based on code by Gary Darby, Intellitech Systems Inc., www.DelphiForFun.org\n    \n        if (this._s === 0) {\n            return ZERO;\n        }\n        if (this.isUnit()) {\n            return ONE;\n        }\n    \n        var digits = this._d;\n        var length = digits.length;\n        var imult1 = new Array(length + length + 1);\n        var product, carry, k;\n        var i;\n    \n        // Calculate diagonal\n        for (i = 0; i < length; i++) {\n            k = i * 2;\n            product = digits[i] * digits[i];\n            carry = (product / BigInteger_base) | 0;\n            imult1[k] = product % BigInteger_base;\n            imult1[k + 1] = carry;\n        }\n    \n        // Calculate repeating part\n        for (i = 0; i < length; i++) {\n            carry = 0;\n            k = i * 2 + 1;\n            for (var j = i + 1; j < length; j++, k++) {\n                product = digits[j] * digits[i] * 2 + imult1[k] + carry;\n                carry = (product / BigInteger_base) | 0;\n                imult1[k] = product % BigInteger_base;\n            }\n            k = length + i;\n            var digit = carry + imult1[k];\n            carry = (digit / BigInteger_base) | 0;\n            imult1[k] = digit % BigInteger_base;\n            imult1[k + 1] += carry;\n        }\n    \n        return new BigInteger(imult1, 1, CONSTRUCT);\n    };\n    \n    /*\n        Function: quotient\n        Divide two <BigIntegers> and truncate towards zero.\n        <quotient> throws an exception if *n* is zero.\n        Parameters:\n            n - The number to divide *this* by. Will be converted to a <BigInteger>.\n        Returns:\n            The *this* / *n*, truncated to an integer.\n        See Also:\n            <add>, <subtract>, <multiply>, <divRem>, <remainder>\n    */\n    BigInteger.prototype.quotient = function(n) {\n        return this.divRem(n)[0];\n    };\n    \n    /*\n        Function: divide\n        Deprecated synonym for <quotient>.\n    */\n    BigInteger.prototype.divide = BigInteger.prototype.quotient;\n    \n    /*\n        Function: remainder\n        Calculate the remainder of two <BigIntegers>.\n        <remainder> throws an exception if *n* is zero.\n        Parameters:\n            n - The remainder after *this* is divided *this* by *n*. Will be\n                converted to a <BigInteger>.\n        Returns:\n            *this* % *n*.\n        See Also:\n            <divRem>, <quotient>\n    */\n    BigInteger.prototype.remainder = function(n) {\n        return this.divRem(n)[1];\n    };\n    \n    /*\n        Function: divRem\n        Calculate the integer quotient and remainder of two <BigIntegers>.\n        <divRem> throws an exception if *n* is zero.\n        Parameters:\n            n - The number to divide *this* by. Will be converted to a <BigInteger>.\n        Returns:\n            A two-element array containing the quotient and the remainder.\n            > a.divRem(b)\n            is exactly equivalent to\n            > [a.quotient(b), a.remainder(b)]\n            except it is faster, because they are calculated at the same time.\n        See Also:\n            <quotient>, <remainder>\n    */\n    BigInteger.prototype.divRem = function(n) {\n        n = BigInteger(n);\n        if (n._s === 0) {\n            throw new Error(\"Divide by zero\");\n        }\n        if (this._s === 0) {\n            return [ZERO, ZERO];\n        }\n        if (n._d.length === 1) {\n            return this.divRemSmall(n._s * n._d[0]);\n        }\n    \n        // Test for easy cases -- |n1| <= |n2|\n        switch (this.compareAbs(n)) {\n        case 0: // n1 == n2\n            return [this._s === n._s ? ONE : M_ONE, ZERO];\n        case -1: // |n1| < |n2|\n            return [ZERO, this];\n        }\n    \n        var sign = this._s * n._s;\n        var a = n.abs();\n        var b_digits = this._d;\n        var b_index = b_digits.length;\n        var digits = n._d.length;\n        var quot = [];\n        var guess;\n    \n        var part = new BigInteger([], 0, CONSTRUCT);\n    \n        while (b_index) {\n            part._d.unshift(b_digits[--b_index]);\n            part = new BigInteger(part._d, 1, CONSTRUCT);\n    \n            if (part.compareAbs(n) < 0) {\n                quot.push(0);\n                continue;\n            }\n            if (part._s === 0) {\n                guess = 0;\n            }\n            else {\n                var xlen = part._d.length, ylen = a._d.length;\n                var highx = part._d[xlen-1]*BigInteger_base + part._d[xlen-2];\n                var highy = a._d[ylen-1]*BigInteger_base + a._d[ylen-2];\n                if (part._d.length > a._d.length) {\n                    // The length of part._d can either match a._d length,\n                    // or exceed it by one.\n                    highx = (highx+1)*BigInteger_base;\n                }\n                guess = Math.ceil(highx/highy);\n            }\n            do {\n                var check = a.multiplySingleDigit(guess);\n                if (check.compareAbs(part) <= 0) {\n                    break;\n                }\n                guess--;\n            } while (guess);\n    \n            quot.push(guess);\n            if (!guess) {\n                continue;\n            }\n            var diff = part.subtract(check);\n            part._d = diff._d.slice();\n        }\n    \n        return [new BigInteger(quot.reverse(), sign, CONSTRUCT),\n               new BigInteger(part._d, this._s, CONSTRUCT)];\n    };\n    \n    // Throws an exception if n is outside of (-BigInteger.base, -1] or\n    // [1, BigInteger.base).  It's not necessary to call this, since the\n    // other division functions will call it if they are able to.\n    BigInteger.prototype.divRemSmall = function(n) {\n        var r;\n        n = +n;\n        if (n === 0) {\n            throw new Error(\"Divide by zero\");\n        }\n    \n        var n_s = n < 0 ? -1 : 1;\n        var sign = this._s * n_s;\n        n = Math.abs(n);\n    \n        if (n < 1 || n >= BigInteger_base) {\n            throw new Error(\"Argument out of range\");\n        }\n    \n        if (this._s === 0) {\n            return [ZERO, ZERO];\n        }\n    \n        if (n === 1 || n === -1) {\n            return [(sign === 1) ? this.abs() : new BigInteger(this._d, sign, CONSTRUCT), ZERO];\n        }\n    \n        // 2 <= n < BigInteger_base\n    \n        // divide a single digit by a single digit\n        if (this._d.length === 1) {\n            var q = new BigInteger([(this._d[0] / n) | 0], 1, CONSTRUCT);\n            r = new BigInteger([(this._d[0] % n) | 0], 1, CONSTRUCT);\n            if (sign < 0) {\n                q = q.negate();\n            }\n            if (this._s < 0) {\n                r = r.negate();\n            }\n            return [q, r];\n        }\n    \n        var digits = this._d.slice();\n        var quot = new Array(digits.length);\n        var part = 0;\n        var diff = 0;\n        var i = 0;\n        var guess;\n    \n        while (digits.length) {\n            part = part * BigInteger_base + digits[digits.length - 1];\n            if (part < n) {\n                quot[i++] = 0;\n                digits.pop();\n                diff = BigInteger_base * diff + part;\n                continue;\n            }\n            if (part === 0) {\n                guess = 0;\n            }\n            else {\n                guess = (part / n) | 0;\n            }\n    \n            var check = n * guess;\n            diff = part - check;\n            quot[i++] = guess;\n            if (!guess) {\n                digits.pop();\n                continue;\n            }\n    \n            digits.pop();\n            part = diff;\n        }\n    \n        r = new BigInteger([diff], 1, CONSTRUCT);\n        if (this._s < 0) {\n            r = r.negate();\n        }\n        return [new BigInteger(quot.reverse(), sign, CONSTRUCT), r];\n    };\n    \n    /*\n        Function: isEven\n        Return true iff *this* is divisible by two.\n        Note that <BigInteger.ZERO> is even.\n        Returns:\n            true if *this* is even, false otherwise.\n        See Also:\n            <isOdd>\n    */\n    BigInteger.prototype.isEven = function() {\n        var digits = this._d;\n        return this._s === 0 || digits.length === 0 || (digits[0] % 2) === 0;\n    };\n    \n    /*\n        Function: isOdd\n        Return true iff *this* is not divisible by two.\n        Returns:\n            true if *this* is odd, false otherwise.\n        See Also:\n            <isEven>\n    */\n    BigInteger.prototype.isOdd = function() {\n        return !this.isEven();\n    };\n    \n    /*\n        Function: sign\n        Get the sign of a <BigInteger>.\n        Returns:\n            * -1 if *this* < 0\n            * 0 if *this* == 0\n            * +1 if *this* > 0\n        See Also:\n            <isZero>, <isPositive>, <isNegative>, <compare>, <BigInteger.ZERO>\n    */\n    BigInteger.prototype.sign = function() {\n        return this._s;\n    };\n    \n    /*\n        Function: isPositive\n        Return true iff *this* > 0.\n        Returns:\n            true if *this*.compare(<BigInteger.ZERO>) == 1.\n        See Also:\n            <sign>, <isZero>, <isNegative>, <isUnit>, <compare>, <BigInteger.ZERO>\n    */\n    BigInteger.prototype.isPositive = function() {\n        return this._s > 0;\n    };\n    \n    /*\n        Function: isNegative\n        Return true iff *this* < 0.\n        Returns:\n            true if *this*.compare(<BigInteger.ZERO>) == -1.\n        See Also:\n            <sign>, <isPositive>, <isZero>, <isUnit>, <compare>, <BigInteger.ZERO>\n    */\n    BigInteger.prototype.isNegative = function() {\n        return this._s < 0;\n    };\n    \n    /*\n        Function: isZero\n        Return true iff *this* == 0.\n        Returns:\n            true if *this*.compare(<BigInteger.ZERO>) == 0.\n        See Also:\n            <sign>, <isPositive>, <isNegative>, <isUnit>, <BigInteger.ZERO>\n    */\n    BigInteger.prototype.isZero = function() {\n        return this._s === 0;\n    };\n    \n    /*\n        Function: exp10\n        Multiply a <BigInteger> by a power of 10.\n        This is equivalent to, but faster than\n        > if (n >= 0) {\n        >     return this.multiply(BigInteger(\"1e\" + n));\n        > }\n        > else { // n <= 0\n        >     return this.quotient(BigInteger(\"1e\" + -n));\n        > }\n        Parameters:\n            n - The power of 10 to multiply *this* by. *n* is converted to a\n            javascipt number and must be no greater than <BigInteger.MAX_EXP>\n            (0x7FFFFFFF), or an exception will be thrown.\n        Returns:\n            *this* * (10 ** *n*), truncated to an integer if necessary.\n        See Also:\n            <pow>, <multiply>\n    */\n    BigInteger.prototype.exp10 = function(n) {\n        n = +n;\n        if (n === 0) {\n            return this;\n        }\n        if (Math.abs(n) > Number(MAX_EXP)) {\n            throw new Error(\"exponent too large in BigInteger.exp10\");\n        }\n        // Optimization for this == 0. This also keeps us from having to trim zeros in the positive n case\n        if (this._s === 0) {\n            return ZERO;\n        }\n        if (n > 0) {\n            var k = new BigInteger(this._d.slice(), this._s, CONSTRUCT);\n    \n            for (; n >= BigInteger_base_log10; n -= BigInteger_base_log10) {\n                k._d.unshift(0);\n            }\n            if (n == 0)\n                return k;\n            k._s = 1;\n            k = k.multiplySingleDigit(Math.pow(10, n));\n            return (this._s < 0 ? k.negate() : k);\n        } else if (-n >= this._d.length*BigInteger_base_log10) {\n            return ZERO;\n        } else {\n            var k = new BigInteger(this._d.slice(), this._s, CONSTRUCT);\n    \n            for (n = -n; n >= BigInteger_base_log10; n -= BigInteger_base_log10) {\n                k._d.shift();\n            }\n            return (n == 0) ? k : k.divRemSmall(Math.pow(10, n))[0];\n        }\n    };\n    \n    /*\n        Function: pow\n        Raise a <BigInteger> to a power.\n        In this implementation, 0**0 is 1.\n        Parameters:\n            n - The exponent to raise *this* by. *n* must be no greater than\n            <BigInteger.MAX_EXP> (0x7FFFFFFF), or an exception will be thrown.\n        Returns:\n            *this* raised to the *nth* power.\n        See Also:\n            <modPow>\n    */\n    BigInteger.prototype.pow = function(n) {\n        if (this.isUnit()) {\n            if (this._s > 0) {\n                return this;\n            }\n            else {\n                return BigInteger(n).isOdd() ? this : this.negate();\n            }\n        }\n    \n        n = BigInteger(n);\n        if (n._s === 0) {\n            return ONE;\n        }\n        else if (n._s < 0) {\n            if (this._s === 0) {\n                throw new Error(\"Divide by zero\");\n            }\n            else {\n                return ZERO;\n            }\n        }\n        if (this._s === 0) {\n            return ZERO;\n        }\n        if (n.isUnit()) {\n            return this;\n        }\n    \n        if (n.compareAbs(MAX_EXP) > 0) {\n            throw new Error(\"exponent too large in BigInteger.pow\");\n        }\n        var x = this;\n        var aux = ONE;\n        var two = BigInteger.small[2];\n    \n        while (n.isPositive()) {\n            if (n.isOdd()) {\n                aux = aux.multiply(x);\n                if (n.isUnit()) {\n                    return aux;\n                }\n            }\n            x = x.square();\n            n = n.quotient(two);\n        }\n    \n        return aux;\n    };\n    \n    /*\n        Function: modPow\n        Raise a <BigInteger> to a power (mod m).\n        Because it is reduced by a modulus, <modPow> is not limited by\n        <BigInteger.MAX_EXP> like <pow>.\n        Parameters:\n            exponent - The exponent to raise *this* by. Must be positive.\n            modulus - The modulus.\n        Returns:\n            *this* ^ *exponent* (mod *modulus*).\n        See Also:\n            <pow>, <mod>\n    */\n    BigInteger.prototype.modPow = function(exponent, modulus) {\n        var result = ONE;\n        var base = this;\n    \n        while (exponent.isPositive()) {\n            if (exponent.isOdd()) {\n                result = result.multiply(base).remainder(modulus);\n            }\n    \n            exponent = exponent.quotient(BigInteger.small[2]);\n            if (exponent.isPositive()) {\n                base = base.square().remainder(modulus);\n            }\n        }\n    \n        return result;\n    };\n    \n    /*\n        Function: log\n        Get the natural logarithm of a <BigInteger> as a native JavaScript number.\n        This is equivalent to\n        > Math.log(this.toJSValue())\n        but handles values outside of the native number range.\n        Returns:\n            log( *this* )\n        See Also:\n            <toJSValue>\n    */\n    BigInteger.prototype.log = function() {\n        switch (this._s) {\n        case 0:\t return -Infinity;\n        case -1: return NaN;\n        default: // Fall through.\n        }\n    \n        var l = this._d.length;\n    \n        if (l*BigInteger_base_log10 < 30) {\n            return Math.log(this.valueOf());\n        }\n    \n        var N = Math.ceil(30/BigInteger_base_log10);\n        var firstNdigits = this._d.slice(l - N);\n        return Math.log((new BigInteger(firstNdigits, 1, CONSTRUCT)).valueOf()) + (l - N) * Math.log(BigInteger_base);\n    };\n    \n    /*\n        Function: valueOf\n        Convert a <BigInteger> to a native JavaScript integer.\n        This is called automatically by JavaScipt to convert a <BigInteger> to a\n        native value.\n        Returns:\n            > parseInt(this.toString(), 10)\n        See Also:\n            <toString>, <toJSValue>\n    */\n    BigInteger.prototype.valueOf = function() {\n        return parseInt(this.toString(), 10);\n    };\n    \n    /*\n        Function: toJSValue\n        Convert a <BigInteger> to a native JavaScript integer.\n        This is the same as valueOf, but more explicitly named.\n        Returns:\n            > parseInt(this.toString(), 10)\n        See Also:\n            <toString>, <valueOf>\n    */\n    BigInteger.prototype.toJSValue = function() {\n        return parseInt(this.toString(), 10);\n    };\n    \n    \n    /*\n     Function: lowVal\n     Author: Lucas Jones\n     */\n    BigInteger.prototype.lowVal = function () {\n        return this._d[0] || 0;\n    };\n    \n    var MAX_EXP = BigInteger(0x7FFFFFFF);\n    // Constant: MAX_EXP\n    // The largest exponent allowed in <pow> and <exp10> (0x7FFFFFFF or 2147483647).\n    BigInteger.MAX_EXP = MAX_EXP;\n    \n    (function() {\n        function makeUnary(fn) {\n            return function(a) {\n                return fn.call(BigInteger(a));\n            };\n        }\n    \n        function makeBinary(fn) {\n            return function(a, b) {\n                return fn.call(BigInteger(a), BigInteger(b));\n            };\n        }\n    \n        function makeTrinary(fn) {\n            return function(a, b, c) {\n                return fn.call(BigInteger(a), BigInteger(b), BigInteger(c));\n            };\n        }\n    \n        (function() {\n            var i, fn;\n            var unary = \"toJSValue,isEven,isOdd,sign,isZero,isNegative,abs,isUnit,square,negate,isPositive,toString,next,prev,log\".split(\",\");\n            var binary = \"compare,remainder,divRem,subtract,add,quotient,divide,multiply,pow,compareAbs\".split(\",\");\n            var trinary = [\"modPow\"];\n    \n            for (i = 0; i < unary.length; i++) {\n                fn = unary[i];\n                BigInteger[fn] = makeUnary(BigInteger.prototype[fn]);\n            }\n    \n            for (i = 0; i < binary.length; i++) {\n                fn = binary[i];\n                BigInteger[fn] = makeBinary(BigInteger.prototype[fn]);\n            }\n    \n            for (i = 0; i < trinary.length; i++) {\n                fn = trinary[i];\n                BigInteger[fn] = makeTrinary(BigInteger.prototype[fn]);\n            }\n    \n            BigInteger.exp10 = function(x, n) {\n                return BigInteger(x).exp10(n);\n            };\n        })();\n    })();\n    \n    exports.JSBigInt = BigInteger; // exports.BigInteger changed to exports.JSBigInt\n    })(typeof exports !== 'undefined' ? exports : this);"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC,UAASA,OAAO,EAAE;EACf,YAAY;;EACZ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEI,IAAIC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASC,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAE;IAE7B,IAAIA,KAAK,KAAKJ,SAAS,EAAE;MACrB,IAAIE,CAAC,YAAYD,UAAU,EAAE;QACzB,OAAOC,CAAC;MACZ,CAAC,MACI,IAAI,OAAOA,CAAC,KAAK,WAAW,EAAE;QAC/B,OAAOG,IAAI;MACf;MACA,OAAOJ,UAAU,CAACK,KAAK,CAACJ,CAAC,CAAC;IAC9B;IAEAA,CAAC,GAAGA,CAAC,IAAI,EAAE,CAAC,CAAE;IACd,OAAOA,CAAC,CAACK,MAAM,IAAI,CAACL,CAAC,CAACA,CAAC,CAACK,MAAM,GAAG,CAAC,CAAC,EAAE;MACjC,EAAEL,CAAC,CAACK,MAAM;IACd;IACA,IAAI,CAACC,EAAE,GAAGN,CAAC;IACX,IAAI,CAACO,EAAE,GAAGP,CAAC,CAACK,MAAM,GAAIJ,CAAC,IAAI,CAAC,GAAI,CAAC;EACrC;EAEAF,UAAU,CAACS,UAAU,GAAG,UAASR,CAAC,EAAEC,CAAC,EAAE;IACnC,OAAO,IAAIF,UAAU,CAACC,CAAC,EAAEC,CAAC,EAAEH,SAAS,CAAC;EAC1C,CAAC;;EAED;EACA;EACA;EACA,IAAIW,eAAe,GAAG,QAAQ;EAC9B,IAAIC,qBAAqB,GAAG,CAAC;EAE7BX,UAAU,CAACY,IAAI,GAAGF,eAAe;EACjCV,UAAU,CAACa,UAAU,GAAGF,qBAAqB;EAE7C,IAAIP,IAAI,GAAG,IAAIJ,UAAU,CAAC,EAAE,EAAE,CAAC,EAAED,SAAS,CAAC;EAC3C;EACA;EACAC,UAAU,CAACI,IAAI,GAAGA,IAAI;EAEtB,IAAIU,GAAG,GAAG,IAAId,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC;EAC3C;EACA;EACAC,UAAU,CAACc,GAAG,GAAGA,GAAG;EAEpB,IAAIC,KAAK,GAAG,IAAIf,UAAU,CAACc,GAAG,CAACP,EAAE,EAAE,CAAC,CAAC,EAAER,SAAS,CAAC;EACjD;EACA;EACAC,UAAU,CAACe,KAAK,GAAGA,KAAK;;EAExB;EACA;EACAf,UAAU,CAACgB,EAAE,GAAGZ,IAAI;;EAEpB;EACA;EACAJ,UAAU,CAACiB,EAAE,GAAGH,GAAG;;EAEnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACId,UAAU,CAACkB,KAAK,GAAG,CACfd,IAAI,EACJU,GAAG,EACH;EACA,IAAId,UAAU,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,EAClC,IAAIC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,SAAS,CAAC,CACrC;;EAED;EACAC,UAAU,CAACmB,MAAM,GAAG,sCAAsC,CAACC,KAAK,CAAC,EAAE,CAAC;;EAEpE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpB,UAAU,CAACqB,SAAS,CAACC,QAAQ,GAAG,UAASV,IAAI,EAAE;IAC3CA,IAAI,GAAG,CAACA,IAAI,IAAI,EAAE;IAClB,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,EAAE,EAAE;MACvB,MAAM,IAAIW,KAAK,CAAC,gBAAgB,GAAGX,IAAI,GAAG,GAAG,CAAC;IAClD;IACA,IAAI,IAAI,CAACJ,EAAE,KAAK,CAAC,EAAE;MACf,OAAO,GAAG;IACd;IACA,IAAII,IAAI,KAAK,EAAE,EAAE;MACb,IAAIY,GAAG,GAAG,IAAI,CAAChB,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;MAChCgB,GAAG,IAAI,IAAI,CAACjB,EAAE,CAAC,IAAI,CAACA,EAAE,CAACD,MAAM,GAAG,CAAC,CAAC,CAACgB,QAAQ,CAAC,CAAC;MAC7C,KAAK,IAAIG,CAAC,GAAG,IAAI,CAAClB,EAAE,CAACD,MAAM,GAAG,CAAC,EAAEmB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1C,IAAIC,KAAK,GAAG,IAAI,CAACnB,EAAE,CAACkB,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC;QACjC,OAAOI,KAAK,CAACpB,MAAM,GAAGK,qBAAqB,EAAEe,KAAK,GAAG,GAAG,GAAGA,KAAK;QAChEF,GAAG,IAAIE,KAAK;MAChB;MACA,OAAOF,GAAG;IACd,CAAC,MACI;MACD,IAAIG,QAAQ,GAAG3B,UAAU,CAACmB,MAAM;MAChCP,IAAI,GAAGZ,UAAU,CAACkB,KAAK,CAACN,IAAI,CAAC;MAC7B,IAAIgB,IAAI,GAAG,IAAI,CAACpB,EAAE;MAElB,IAAIP,CAAC,GAAG,IAAI,CAAC4B,GAAG,CAAC,CAAC;MAClB,IAAIV,MAAM,GAAG,EAAE;MACf,IAAIW,KAAK;MAET,OAAO7B,CAAC,CAACO,EAAE,KAAK,CAAC,EAAE;QACf,IAAIuB,MAAM,GAAG9B,CAAC,CAAC+B,MAAM,CAACpB,IAAI,CAAC;QAC3BX,CAAC,GAAG8B,MAAM,CAAC,CAAC,CAAC;QACbD,KAAK,GAAGC,MAAM,CAAC,CAAC,CAAC;QACjB;QACA;QACAZ,MAAM,CAACc,IAAI,CAACN,QAAQ,CAACG,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;MAC1C;MACA,OAAO,CAACN,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,IAAIT,MAAM,CAACgB,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;IAC5D;EACJ,CAAC;;EAED;EACApC,UAAU,CAACqC,UAAU,GAAG,CACpB,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,YAAY,EACZ,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,CACnB;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrC,UAAU,CAACK,KAAK,GAAG,UAASH,CAAC,EAAEU,IAAI,EAAE;IACjC;IACA;IACA;IACA;IACA,SAAS0B,iBAAiBA,CAACd,GAAG,EAAE;MAC5BA,GAAG,GAAGA,GAAG,CAACe,OAAO,CAAC,8BAA8B,EAAE,GAAG,CAAC;MAEtD,OAAOf,GAAG,CAACe,OAAO,CAAC,wCAAwC,EAAE,UAASC,CAAC,EAAEtC,CAAC,EAAED,CAAC,EAAEwC,CAAC,EAAEC,CAAC,EAAE;QACjFA,CAAC,GAAG,CAACA,CAAC;QACN,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAC;QACb,IAAIjB,CAAC,GAAGxB,CAAC,CAACK,MAAM,GAAGoC,CAAC;QACpBF,CAAC,GAAG,CAACG,CAAC,GAAG1C,CAAC,GAAGwC,CAAC,EAAEnC,MAAM;QACtBoC,CAAC,GAAI,CAACA,CAAC,GAAGE,IAAI,CAACf,GAAG,CAACa,CAAC,CAAC,KAAKF,CAAC,GAAGE,CAAC,GAAGF,CAAC,GAAGG,CAAC,GAAG,CAAE;QAC5C,IAAIE,CAAC,GAAI,IAAIC,KAAK,CAACJ,CAAC,GAAG,CAAC,CAAC,CAAEN,IAAI,CAAC,GAAG,CAAC;QACpC,IAAIW,CAAC,GAAG9C,CAAC,GAAGwC,CAAC;QACb,OAAO,CAACvC,CAAC,IAAI,EAAE,IAAI,CAACyC,CAAC,GAAGI,CAAC,GAAGF,CAAC,GAAGE,CAAC,GAAGA,CAAC,IAAIF,CAAC,EAAEG,MAAM,CAAC,CAAC,EAAEvB,CAAC,IAAIkB,CAAC,GAAGE,CAAC,CAACvC,MAAM,GAAG,CAAC,CAAC,IAAImB,CAAC,GAAGsB,CAAC,CAACzC,MAAM,GAAG,GAAG,GAAGyC,CAAC,CAACC,MAAM,CAACvB,CAAC,CAAC,GAAG,EAAE,CAAC;MAC1H,CAAC,CAAC;IACN;IAEAvB,CAAC,GAAGA,CAAC,CAACoB,QAAQ,CAAC,CAAC;IAChB,IAAI,OAAOV,IAAI,KAAK,WAAW,IAAI,CAACA,IAAI,KAAK,EAAE,EAAE;MAC7CV,CAAC,GAAGoC,iBAAiB,CAACpC,CAAC,CAAC;IAC5B;IAEA,IAAI+C,QAAQ;IACZ,IAAI,OAAOrC,IAAI,KAAK,WAAW,EAAE;MAC7BqC,QAAQ,GAAG,QAAQ;IACvB,CAAC,MACI,IAAIrC,IAAI,IAAI,EAAE,EAAE;MACjBqC,QAAQ,GAAG,IAAI;IACnB,CAAC,MACI,IAAIrC,IAAI,IAAI,CAAC,EAAE;MAChBqC,QAAQ,GAAG,IAAI;IACnB,CAAC,MACI,IAAIrC,IAAI,IAAI,CAAC,EAAE;MAChBqC,QAAQ,GAAG,IAAI;IACnB,CAAC,MACI;MACDA,QAAQ,GAAG,EAAE;IACjB;IACA,IAAIC,KAAK,GAAG,IAAIC,MAAM,CAAC,aAAa,GAAGF,QAAQ,GAAG,4BAA4B,EAAE,GAAG,CAAC,CAACG,IAAI,CAAClD,CAAC,CAAC;IAC5F,IAAIgD,KAAK,EAAE;MACP,IAAItB,IAAI,GAAGsB,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG;MAC1B,IAAIG,WAAW,GAAGH,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MAChC,IAAI/B,MAAM,GAAG+B,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;MAE3B,IAAI,OAAOtC,IAAI,KAAK,WAAW,EAAE;QAC7B;QACA,IAAIyC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,IAAI,EAAE;UAAE;UAChDzC,IAAI,GAAG,EAAE;QACb,CAAC,MACI,IAAIyC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,IAAI,EAAE;UAAE;UACrDzC,IAAI,GAAG,CAAC;QACZ,CAAC,MACI,IAAIyC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,IAAI,EAAE;UAAE;UACrDzC,IAAI,GAAG,CAAC;QACZ,CAAC,MACI;UACDA,IAAI,GAAG,EAAE;QACb;MACJ,CAAC,MACI,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,EAAE,EAAE;QAC5B,MAAM,IAAIW,KAAK,CAAC,gBAAgB,GAAGX,IAAI,GAAG,GAAG,CAAC;MAClD;MAEAA,IAAI,GAAG,CAACA,IAAI;;MAEZ;MACA,IAAI,CAAEZ,UAAU,CAACqC,UAAU,CAACzB,IAAI,CAAC,CAAC0C,IAAI,CAACnC,MAAM,CAAE,EAAE;QAC7C,MAAM,IAAII,KAAK,CAAC,sBAAsB,GAAGX,IAAI,CAAC;MAClD;;MAEA;MACAO,MAAM,GAAGA,MAAM,CAACoB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACnB,KAAK,CAAC,EAAE,CAAC;MAC5C,IAAID,MAAM,CAACb,MAAM,KAAK,CAAC,EAAE;QACrB,OAAOF,IAAI;MACf;;MAEA;MACAwB,IAAI,GAAIA,IAAI,KAAK,GAAG,GAAI,CAAC,CAAC,GAAG,CAAC;;MAE9B;MACA,IAAIhB,IAAI,IAAI,EAAE,EAAE;QACZ,IAAI2C,CAAC,GAAG,EAAE;QACV,OAAOpC,MAAM,CAACb,MAAM,IAAIK,qBAAqB,EAAE;UAC3C4C,CAAC,CAACtB,IAAI,CAACuB,QAAQ,CAACrC,MAAM,CAACsC,MAAM,CAACtC,MAAM,CAACb,MAAM,GAACN,UAAU,CAACa,UAAU,EAAEb,UAAU,CAACa,UAAU,CAAC,CAACuB,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5G;QACAmB,CAAC,CAACtB,IAAI,CAACuB,QAAQ,CAACrC,MAAM,CAACiB,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACrC,OAAO,IAAIpC,UAAU,CAACuD,CAAC,EAAE3B,IAAI,EAAE7B,SAAS,CAAC;MAC7C;;MAEA;MACA,IAAIwD,CAAC,GAAGnD,IAAI;MACZQ,IAAI,GAAGZ,UAAU,CAACkB,KAAK,CAACN,IAAI,CAAC;MAC7B,IAAIM,KAAK,GAAGlB,UAAU,CAACkB,KAAK;MAC5B,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,MAAM,CAACb,MAAM,EAAEmB,CAAC,EAAE,EAAE;QACpC8B,CAAC,GAAGA,CAAC,CAACG,QAAQ,CAAC9C,IAAI,CAAC,CAAC+C,GAAG,CAACzC,KAAK,CAACsC,QAAQ,CAACrC,MAAM,CAACM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAC5D;MACA,OAAO,IAAIzB,UAAU,CAACuD,CAAC,CAAChD,EAAE,EAAEqB,IAAI,EAAE7B,SAAS,CAAC;IAChD,CAAC,MACI;MACD,MAAM,IAAIwB,KAAK,CAAC,6BAA6B,GAAGrB,CAAC,CAAC;IACtD;EACJ,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,UAAU,CAACqB,SAAS,CAACsC,GAAG,GAAG,UAAS1D,CAAC,EAAE;IACnC,IAAI,IAAI,CAACO,EAAE,KAAK,CAAC,EAAE;MACf,OAAOR,UAAU,CAACC,CAAC,CAAC;IACxB;IAEAA,CAAC,GAAGD,UAAU,CAACC,CAAC,CAAC;IACjB,IAAIA,CAAC,CAACO,EAAE,KAAK,CAAC,EAAE;MACZ,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACA,EAAE,KAAKP,CAAC,CAACO,EAAE,EAAE;MAClBP,CAAC,GAAGA,CAAC,CAAC2D,MAAM,CAAC,CAAC;MACd,OAAO,IAAI,CAACC,QAAQ,CAAC5D,CAAC,CAAC;IAC3B;IAEA,IAAI6D,CAAC,GAAG,IAAI,CAACvD,EAAE;IACf,IAAIwD,CAAC,GAAG9D,CAAC,CAACM,EAAE;IACZ,IAAIyD,EAAE,GAAGF,CAAC,CAACxD,MAAM;IACjB,IAAI2D,EAAE,GAAGF,CAAC,CAACzD,MAAM;IACjB,IAAI4D,GAAG,GAAG,IAAIpB,KAAK,CAACF,IAAI,CAACuB,GAAG,CAACH,EAAE,EAAEC,EAAE,CAAC,GAAG,CAAC,CAAC;IACzC,IAAIG,IAAI,GAAGxB,IAAI,CAACyB,GAAG,CAACL,EAAE,EAAEC,EAAE,CAAC;IAC3B,IAAIK,KAAK,GAAG,CAAC;IACb,IAAIxC,KAAK;IAET,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,IAAI,EAAE3C,CAAC,EAAE,EAAE;MAC3BK,KAAK,GAAGgC,CAAC,CAACrC,CAAC,CAAC,GAAGsC,CAAC,CAACtC,CAAC,CAAC,GAAG6C,KAAK;MAC3BJ,GAAG,CAACzC,CAAC,CAAC,GAAGK,KAAK,GAAGpB,eAAe;MAChC4D,KAAK,GAAIxC,KAAK,GAAGpB,eAAe,GAAI,CAAC;IACzC;IACA,IAAIuD,EAAE,GAAGD,EAAE,EAAE;MACTF,CAAC,GAAGC,CAAC;MACLC,EAAE,GAAGC,EAAE;IACX;IACA,KAAKxC,CAAC,GAAG2C,IAAI,EAAEE,KAAK,IAAI7C,CAAC,GAAGuC,EAAE,EAAEvC,CAAC,EAAE,EAAE;MACjCK,KAAK,GAAGgC,CAAC,CAACrC,CAAC,CAAC,GAAG6C,KAAK;MACpBJ,GAAG,CAACzC,CAAC,CAAC,GAAGK,KAAK,GAAGpB,eAAe;MAChC4D,KAAK,GAAIxC,KAAK,GAAGpB,eAAe,GAAI,CAAC;IACzC;IACA,IAAI4D,KAAK,EAAE;MACPJ,GAAG,CAACzC,CAAC,CAAC,GAAG6C,KAAK;IAClB;IAEA,OAAQ7C,CAAC,GAAGuC,EAAE,EAAEvC,CAAC,EAAE,EAAE;MACjByC,GAAG,CAACzC,CAAC,CAAC,GAAGqC,CAAC,CAACrC,CAAC,CAAC;IACjB;IAEA,OAAO,IAAIzB,UAAU,CAACkE,GAAG,EAAE,IAAI,CAAC1D,EAAE,EAAET,SAAS,CAAC;EAClD,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,UAAU,CAACqB,SAAS,CAACuC,MAAM,GAAG,YAAW;IACrC,OAAO,IAAI5D,UAAU,CAAC,IAAI,CAACO,EAAE,EAAG,CAAC,IAAI,CAACC,EAAE,GAAI,CAAC,EAAET,SAAS,CAAC;EAC7D,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,UAAU,CAACqB,SAAS,CAACQ,GAAG,GAAG,YAAW;IAClC,OAAQ,IAAI,CAACrB,EAAE,GAAG,CAAC,GAAI,IAAI,CAACoD,MAAM,CAAC,CAAC,GAAG,IAAI;EAC/C,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5D,UAAU,CAACqB,SAAS,CAACwC,QAAQ,GAAG,UAAS5D,CAAC,EAAE;IACxC,IAAI,IAAI,CAACO,EAAE,KAAK,CAAC,EAAE;MACf,OAAOR,UAAU,CAACC,CAAC,CAAC,CAAC2D,MAAM,CAAC,CAAC;IACjC;IAEA3D,CAAC,GAAGD,UAAU,CAACC,CAAC,CAAC;IACjB,IAAIA,CAAC,CAACO,EAAE,KAAK,CAAC,EAAE;MACZ,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACA,EAAE,KAAKP,CAAC,CAACO,EAAE,EAAE;MAClBP,CAAC,GAAGA,CAAC,CAAC2D,MAAM,CAAC,CAAC;MACd,OAAO,IAAI,CAACD,GAAG,CAAC1D,CAAC,CAAC;IACtB;IAEA,IAAIsE,CAAC,GAAG,IAAI;IACZ;IACA,IAAI,IAAI,CAAC/D,EAAE,GAAG,CAAC,EAAE;MACb+D,CAAC,GAAG,IAAIvE,UAAU,CAACC,CAAC,CAACM,EAAE,EAAE,CAAC,EAAER,SAAS,CAAC;MACtCE,CAAC,GAAG,IAAID,UAAU,CAAC,IAAI,CAACO,EAAE,EAAE,CAAC,EAAER,SAAS,CAAC;IAC7C;;IAEA;IACA,IAAI6B,IAAI,GAAG2C,CAAC,CAACC,UAAU,CAACvE,CAAC,CAAC;IAC1B,IAAI2B,IAAI,KAAK,CAAC,EAAE;MACZ,OAAOxB,IAAI;IACf,CAAC,MACI,IAAIwB,IAAI,GAAG,CAAC,EAAE;MACf;MACA,IAAI6C,CAAC,GAAGxE,CAAC;MACTA,CAAC,GAAGsE,CAAC;MACLA,CAAC,GAAGE,CAAC;IACT;;IAEA;IACA,IAAIX,CAAC,GAAGS,CAAC,CAAChE,EAAE;IACZ,IAAIwD,CAAC,GAAG9D,CAAC,CAACM,EAAE;IACZ,IAAIyD,EAAE,GAAGF,CAAC,CAACxD,MAAM;IACjB,IAAI2D,EAAE,GAAGF,CAAC,CAACzD,MAAM;IACjB,IAAIoE,IAAI,GAAG,IAAI5B,KAAK,CAACkB,EAAE,CAAC,CAAC,CAAC;IAC1B,IAAIW,MAAM,GAAG,CAAC;IACd,IAAIlD,CAAC;IACL,IAAIK,KAAK;IAET,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,EAAE,EAAExC,CAAC,EAAE,EAAE;MACrBK,KAAK,GAAGgC,CAAC,CAACrC,CAAC,CAAC,GAAGkD,MAAM,GAAGZ,CAAC,CAACtC,CAAC,CAAC;MAC5B,IAAIK,KAAK,GAAG,CAAC,EAAE;QACXA,KAAK,IAAIpB,eAAe;QACxBiE,MAAM,GAAG,CAAC;MACd,CAAC,MACI;QACDA,MAAM,GAAG,CAAC;MACd;MACAD,IAAI,CAACjD,CAAC,CAAC,GAAGK,KAAK;IACnB;IACA,KAAKL,CAAC,GAAGwC,EAAE,EAAExC,CAAC,GAAGuC,EAAE,EAAEvC,CAAC,EAAE,EAAE;MACtBK,KAAK,GAAGgC,CAAC,CAACrC,CAAC,CAAC,GAAGkD,MAAM;MACrB,IAAI7C,KAAK,GAAG,CAAC,EAAE;QACXA,KAAK,IAAIpB,eAAe;MAC5B,CAAC,MACI;QACDgE,IAAI,CAACjD,CAAC,EAAE,CAAC,GAAGK,KAAK;QACjB;MACJ;MACA4C,IAAI,CAACjD,CAAC,CAAC,GAAGK,KAAK;IACnB;IACA,OAAQL,CAAC,GAAGuC,EAAE,EAAEvC,CAAC,EAAE,EAAE;MACjBiD,IAAI,CAACjD,CAAC,CAAC,GAAGqC,CAAC,CAACrC,CAAC,CAAC;IAClB;IAEA,OAAO,IAAIzB,UAAU,CAAC0E,IAAI,EAAE9C,IAAI,EAAE7B,SAAS,CAAC;EAChD,CAAC;EAED,CAAC,YAAW;IACR,SAAS6E,MAAMA,CAAC3E,CAAC,EAAE2B,IAAI,EAAE;MACrB,IAAIkC,CAAC,GAAG7D,CAAC,CAACM,EAAE;MACZ,IAAI2D,GAAG,GAAGJ,CAAC,CAACe,KAAK,CAAC,CAAC;MACnB,IAAIP,KAAK,GAAG,IAAI;MAChB,IAAI7C,CAAC,GAAG,CAAC;MAET,OAAO,IAAI,EAAE;QACT,IAAIK,KAAK,GAAG,CAACgC,CAAC,CAACrC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAC3ByC,GAAG,CAACzC,CAAC,CAAC,GAAGK,KAAK,GAAGpB,eAAe;QAChC,IAAIoB,KAAK,IAAIpB,eAAe,GAAG,CAAC,EAAE;UAC9B;QACJ;QACA,EAAEe,CAAC;MACP;MAEA,OAAO,IAAIzB,UAAU,CAACkE,GAAG,EAAEtC,IAAI,EAAE7B,SAAS,CAAC;IAC/C;IAEA,SAAS+E,WAAWA,CAAC7E,CAAC,EAAE2B,IAAI,EAAE;MAC1B,IAAIkC,CAAC,GAAG7D,CAAC,CAACM,EAAE;MACZ,IAAI2D,GAAG,GAAGJ,CAAC,CAACe,KAAK,CAAC,CAAC;MACnB,IAAIF,MAAM,GAAG,IAAI;MACjB,IAAIlD,CAAC,GAAG,CAAC;MAET,OAAO,IAAI,EAAE;QACT,IAAIK,KAAK,GAAG,CAACgC,CAAC,CAACrC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAC3B,IAAIK,KAAK,GAAG,CAAC,EAAE;UACXoC,GAAG,CAACzC,CAAC,CAAC,GAAGK,KAAK,GAAGpB,eAAe;QACpC,CAAC,MACI;UACDwD,GAAG,CAACzC,CAAC,CAAC,GAAGK,KAAK;UACd;QACJ;QACA,EAAEL,CAAC;MACP;MAEA,OAAO,IAAIzB,UAAU,CAACkE,GAAG,EAAEtC,IAAI,EAAE7B,SAAS,CAAC;IAC/C;;IAEA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQC,UAAU,CAACqB,SAAS,CAAC0D,IAAI,GAAG,YAAW;MACnC,QAAQ,IAAI,CAACvE,EAAE;QACf,KAAK,CAAC;UACF,OAAOM,GAAG;QACd,KAAK,CAAC,CAAC;UACH,OAAOgE,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAChC;QACA;UACI,OAAOF,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;MAC1B;IACJ,CAAC;;IAED;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ5E,UAAU,CAACqB,SAAS,CAAC2D,IAAI,GAAG,YAAW;MACnC,QAAQ,IAAI,CAACxE,EAAE;QACf,KAAK,CAAC;UACF,OAAOO,KAAK;QAChB,KAAK,CAAC,CAAC;UACH,OAAO6D,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC3B;QACA;UACI,OAAOE,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;MAC/B;IACJ,CAAC;EACL,CAAC,EAAE,CAAC;;EAEJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI9E,UAAU,CAACqB,SAAS,CAACmD,UAAU,GAAG,UAASvE,CAAC,EAAE;IAC1C,IAAI,IAAI,KAAKA,CAAC,EAAE;MACZ,OAAO,CAAC;IACZ;IAEA,IAAI,EAAEA,CAAC,YAAYD,UAAU,CAAC,EAAE;MAC5B,IAAI,CAACiF,QAAQ,CAAChF,CAAC,CAAC,EAAE;QACd,OAAOiF,KAAK,CAACjF,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;MAC5B;MACAA,CAAC,GAAGD,UAAU,CAACC,CAAC,CAAC;IACrB;IAEA,IAAI,IAAI,CAACO,EAAE,KAAK,CAAC,EAAE;MACf,OAAQP,CAAC,CAACO,EAAE,KAAK,CAAC,GAAI,CAAC,CAAC,GAAG,CAAC;IAChC;IACA,IAAIP,CAAC,CAACO,EAAE,KAAK,CAAC,EAAE;MACZ,OAAO,CAAC;IACZ;IAEA,IAAImC,CAAC,GAAG,IAAI,CAACpC,EAAE,CAACD,MAAM;IACtB,IAAI6E,EAAE,GAAGlF,CAAC,CAACM,EAAE,CAACD,MAAM;IACpB,IAAIqC,CAAC,GAAGwC,EAAE,EAAE;MACR,OAAO,CAAC,CAAC;IACb,CAAC,MACI,IAAIxC,CAAC,GAAGwC,EAAE,EAAE;MACb,OAAO,CAAC;IACZ;IAEA,IAAIrB,CAAC,GAAG,IAAI,CAACvD,EAAE;IACf,IAAIwD,CAAC,GAAG9D,CAAC,CAACM,EAAE;IACZ,KAAK,IAAIkB,CAAC,GAAGkB,CAAC,GAAC,CAAC,EAAElB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B,IAAIqC,CAAC,CAACrC,CAAC,CAAC,KAAKsC,CAAC,CAACtC,CAAC,CAAC,EAAE;QACf,OAAOqC,CAAC,CAACrC,CAAC,CAAC,GAAGsC,CAAC,CAACtC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC/B;IACJ;IAEA,OAAO,CAAC;EACZ,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIzB,UAAU,CAACqB,SAAS,CAAC+D,OAAO,GAAG,UAASnF,CAAC,EAAE;IACvC,IAAI,IAAI,KAAKA,CAAC,EAAE;MACZ,OAAO,CAAC;IACZ;IAEAA,CAAC,GAAGD,UAAU,CAACC,CAAC,CAAC;IAEjB,IAAI,IAAI,CAACO,EAAE,KAAK,CAAC,EAAE;MACf,OAAO,CAACP,CAAC,CAACO,EAAE;IAChB;IAEA,IAAI,IAAI,CAACA,EAAE,KAAKP,CAAC,CAACO,EAAE,EAAE;MAAE;MACpB,IAAI6E,GAAG,GAAG,IAAI,CAACb,UAAU,CAACvE,CAAC,CAAC;MAC5B,OAAOoF,GAAG,GAAG,IAAI,CAAC7E,EAAE;IACxB,CAAC,MACI;MACD,OAAO,IAAI,CAACA,EAAE;IAClB;EACJ,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,UAAU,CAACqB,SAAS,CAACiE,MAAM,GAAG,YAAW;IACrC,OAAO,IAAI,KAAKxE,GAAG,IACf,IAAI,KAAKC,KAAK,IACb,IAAI,CAACR,EAAE,CAACD,MAAM,KAAK,CAAC,IAAI,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAE;EAClD,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIP,UAAU,CAACqB,SAAS,CAACqC,QAAQ,GAAG,UAASzD,CAAC,EAAE;IACxC;IACA,IAAI,IAAI,CAACO,EAAE,KAAK,CAAC,EAAE;MACf,OAAOJ,IAAI;IACf;IAEAH,CAAC,GAAGD,UAAU,CAACC,CAAC,CAAC;IACjB,IAAIA,CAAC,CAACO,EAAE,KAAK,CAAC,EAAE;MACZ,OAAOJ,IAAI;IACf;IACA,IAAI,IAAI,CAACkF,MAAM,CAAC,CAAC,EAAE;MACf,IAAI,IAAI,CAAC9E,EAAE,GAAG,CAAC,EAAE;QACb,OAAOP,CAAC,CAAC2D,MAAM,CAAC,CAAC;MACrB;MACA,OAAO3D,CAAC;IACZ;IACA,IAAIA,CAAC,CAACqF,MAAM,CAAC,CAAC,EAAE;MACZ,IAAIrF,CAAC,CAACO,EAAE,GAAG,CAAC,EAAE;QACV,OAAO,IAAI,CAACoD,MAAM,CAAC,CAAC;MACxB;MACA,OAAO,IAAI;IACf;IACA,IAAI,IAAI,KAAK3D,CAAC,EAAE;MACZ,OAAO,IAAI,CAACsF,MAAM,CAAC,CAAC;IACxB;IAEA,IAAIxC,CAAC,GAAI,IAAI,CAACxC,EAAE,CAACD,MAAM,IAAIL,CAAC,CAACM,EAAE,CAACD,MAAO;IACvC,IAAIwD,CAAC,GAAG,CAACf,CAAC,GAAG,IAAI,GAAG9C,CAAC,EAAEM,EAAE,CAAC,CAAC;IAC3B,IAAIwD,CAAC,GAAG,CAAChB,CAAC,GAAG9C,CAAC,GAAG,IAAI,EAAEM,EAAE;IACzB,IAAIyD,EAAE,GAAGF,CAAC,CAACxD,MAAM;IACjB,IAAI2D,EAAE,GAAGF,CAAC,CAACzD,MAAM;IAEjB,IAAIkF,EAAE,GAAGxB,EAAE,GAAGC,EAAE;IAChB,IAAIwB,OAAO,GAAG,IAAI3C,KAAK,CAAC0C,EAAE,CAAC;IAC3B,IAAI/D,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,EAAE,EAAE/D,CAAC,EAAE,EAAE;MACrBgE,OAAO,CAAChE,CAAC,CAAC,GAAG,CAAC;IAClB;IAEA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,EAAE,EAAExC,CAAC,EAAE,EAAE;MACrB,IAAI6C,KAAK,GAAG,CAAC;MACb,IAAIoB,EAAE,GAAG3B,CAAC,CAACtC,CAAC,CAAC;MACb,IAAIkE,MAAM,GAAG3B,EAAE,GAAGvC,CAAC;MACnB,IAAIK,KAAK;MACT,KAAK,IAAI8D,CAAC,GAAGnE,CAAC,EAAEmE,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;QAC7B9D,KAAK,GAAG2D,OAAO,CAACG,CAAC,CAAC,GAAGF,EAAE,GAAG5B,CAAC,CAAC8B,CAAC,GAAGnE,CAAC,CAAC,GAAG6C,KAAK;QAC1CA,KAAK,GAAIxC,KAAK,GAAGpB,eAAe,GAAI,CAAC;QACrC+E,OAAO,CAACG,CAAC,CAAC,GAAI9D,KAAK,GAAGpB,eAAe,GAAI,CAAC;MAC9C;MACA,IAAI4D,KAAK,EAAE;QACPxC,KAAK,GAAG2D,OAAO,CAACG,CAAC,CAAC,GAAGtB,KAAK;QAC1BA,KAAK,GAAIxC,KAAK,GAAGpB,eAAe,GAAI,CAAC;QACrC+E,OAAO,CAACG,CAAC,CAAC,GAAG9D,KAAK,GAAGpB,eAAe;MACxC;IACJ;IACA,OAAO,IAAIV,UAAU,CAACyF,OAAO,EAAE,IAAI,CAACjF,EAAE,GAAGP,CAAC,CAACO,EAAE,EAAET,SAAS,CAAC;EAC7D,CAAC;;EAED;EACA;EACA;EACAC,UAAU,CAACqB,SAAS,CAACwE,mBAAmB,GAAG,UAAS5F,CAAC,EAAE;IACnD,IAAIA,CAAC,KAAK,CAAC,IAAI,IAAI,CAACO,EAAE,KAAK,CAAC,EAAE;MAC1B,OAAOJ,IAAI;IACf;IACA,IAAIH,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,IAAI;IACf;IAEA,IAAI6B,KAAK;IACT,IAAI,IAAI,CAACvB,EAAE,CAACD,MAAM,KAAK,CAAC,EAAE;MACtBwB,KAAK,GAAG,IAAI,CAACvB,EAAE,CAAC,CAAC,CAAC,GAAGN,CAAC;MACtB,IAAI6B,KAAK,IAAIpB,eAAe,EAAE;QAC1B,OAAO,IAAIV,UAAU,CAAC,CAAE8B,KAAK,GAAGpB,eAAe,GAAE,CAAC,EACzCoB,KAAK,GAAGpB,eAAe,GAAE,CAAC,CAAC,EAAE,CAAC,EAAEX,SAAS,CAAC;MACvD;MACA,OAAO,IAAIC,UAAU,CAAC,CAAC8B,KAAK,CAAC,EAAE,CAAC,EAAE/B,SAAS,CAAC;IAChD;IAEA,IAAIE,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,IAAI,CAAC0D,GAAG,CAAC,IAAI,CAAC;IACzB;IACA,IAAI,IAAI,CAAC2B,MAAM,CAAC,CAAC,EAAE;MACf,OAAO,IAAItF,UAAU,CAAC,CAACC,CAAC,CAAC,EAAE,CAAC,EAAEF,SAAS,CAAC;IAC5C;IAEA,IAAI+D,CAAC,GAAG,IAAI,CAACvD,EAAE;IACf,IAAIyD,EAAE,GAAGF,CAAC,CAACxD,MAAM;IAEjB,IAAIkF,EAAE,GAAGxB,EAAE,GAAG,CAAC;IACf,IAAIyB,OAAO,GAAG,IAAI3C,KAAK,CAAC0C,EAAE,CAAC;IAC3B,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,EAAE,EAAE/D,CAAC,EAAE,EAAE;MACzBgE,OAAO,CAAChE,CAAC,CAAC,GAAG,CAAC;IAClB;IAEA,IAAI6C,KAAK,GAAG,CAAC;IACb,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,EAAE,EAAE4B,CAAC,EAAE,EAAE;MACzB9D,KAAK,GAAG7B,CAAC,GAAG6D,CAAC,CAAC8B,CAAC,CAAC,GAAGtB,KAAK;MACxBA,KAAK,GAAIxC,KAAK,GAAGpB,eAAe,GAAI,CAAC;MACrC+E,OAAO,CAACG,CAAC,CAAC,GAAI9D,KAAK,GAAGpB,eAAe,GAAI,CAAC;IAC9C;IACA,IAAI4D,KAAK,EAAE;MACPmB,OAAO,CAACG,CAAC,CAAC,GAAGtB,KAAK;IACtB;IAEA,OAAO,IAAItE,UAAU,CAACyF,OAAO,EAAE,CAAC,EAAE1F,SAAS,CAAC;EAChD,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,UAAU,CAACqB,SAAS,CAACkE,MAAM,GAAG,YAAW;IACrC;IACA;IACA;IACA;;IAEA,IAAI,IAAI,CAAC/E,EAAE,KAAK,CAAC,EAAE;MACf,OAAOJ,IAAI;IACf;IACA,IAAI,IAAI,CAACkF,MAAM,CAAC,CAAC,EAAE;MACf,OAAOxE,GAAG;IACd;IAEA,IAAIK,MAAM,GAAG,IAAI,CAACZ,EAAE;IACpB,IAAID,MAAM,GAAGa,MAAM,CAACb,MAAM;IAC1B,IAAIwF,MAAM,GAAG,IAAIhD,KAAK,CAACxC,MAAM,GAAGA,MAAM,GAAG,CAAC,CAAC;IAC3C,IAAIyF,OAAO,EAAEzB,KAAK,EAAE0B,CAAC;IACrB,IAAIvE,CAAC;;IAEL;IACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,MAAM,EAAEmB,CAAC,EAAE,EAAE;MACzBuE,CAAC,GAAGvE,CAAC,GAAG,CAAC;MACTsE,OAAO,GAAG5E,MAAM,CAACM,CAAC,CAAC,GAAGN,MAAM,CAACM,CAAC,CAAC;MAC/B6C,KAAK,GAAIyB,OAAO,GAAGrF,eAAe,GAAI,CAAC;MACvCoF,MAAM,CAACE,CAAC,CAAC,GAAGD,OAAO,GAAGrF,eAAe;MACrCoF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG1B,KAAK;IACzB;;IAEA;IACA,KAAK7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,MAAM,EAAEmB,CAAC,EAAE,EAAE;MACzB6C,KAAK,GAAG,CAAC;MACT0B,CAAC,GAAGvE,CAAC,GAAG,CAAC,GAAG,CAAC;MACb,KAAK,IAAImE,CAAC,GAAGnE,CAAC,GAAG,CAAC,EAAEmE,CAAC,GAAGtF,MAAM,EAAEsF,CAAC,EAAE,EAAEI,CAAC,EAAE,EAAE;QACtCD,OAAO,GAAG5E,MAAM,CAACyE,CAAC,CAAC,GAAGzE,MAAM,CAACM,CAAC,CAAC,GAAG,CAAC,GAAGqE,MAAM,CAACE,CAAC,CAAC,GAAG1B,KAAK;QACvDA,KAAK,GAAIyB,OAAO,GAAGrF,eAAe,GAAI,CAAC;QACvCoF,MAAM,CAACE,CAAC,CAAC,GAAGD,OAAO,GAAGrF,eAAe;MACzC;MACAsF,CAAC,GAAG1F,MAAM,GAAGmB,CAAC;MACd,IAAIK,KAAK,GAAGwC,KAAK,GAAGwB,MAAM,CAACE,CAAC,CAAC;MAC7B1B,KAAK,GAAIxC,KAAK,GAAGpB,eAAe,GAAI,CAAC;MACrCoF,MAAM,CAACE,CAAC,CAAC,GAAGlE,KAAK,GAAGpB,eAAe;MACnCoF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,IAAI1B,KAAK;IAC1B;IAEA,OAAO,IAAItE,UAAU,CAAC8F,MAAM,EAAE,CAAC,EAAE/F,SAAS,CAAC;EAC/C,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,UAAU,CAACqB,SAAS,CAAC4E,QAAQ,GAAG,UAAShG,CAAC,EAAE;IACxC,OAAO,IAAI,CAAC+B,MAAM,CAAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC;;EAED;AACJ;AACA;AACA;EACID,UAAU,CAACqB,SAAS,CAAC6E,MAAM,GAAGlG,UAAU,CAACqB,SAAS,CAAC4E,QAAQ;;EAE3D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIjG,UAAU,CAACqB,SAAS,CAAC8E,SAAS,GAAG,UAASlG,CAAC,EAAE;IACzC,OAAO,IAAI,CAAC+B,MAAM,CAAC/B,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACID,UAAU,CAACqB,SAAS,CAACW,MAAM,GAAG,UAAS/B,CAAC,EAAE;IACtCA,CAAC,GAAGD,UAAU,CAACC,CAAC,CAAC;IACjB,IAAIA,CAAC,CAACO,EAAE,KAAK,CAAC,EAAE;MACZ,MAAM,IAAIe,KAAK,CAAC,gBAAgB,CAAC;IACrC;IACA,IAAI,IAAI,CAACf,EAAE,KAAK,CAAC,EAAE;MACf,OAAO,CAACJ,IAAI,EAAEA,IAAI,CAAC;IACvB;IACA,IAAIH,CAAC,CAACM,EAAE,CAACD,MAAM,KAAK,CAAC,EAAE;MACnB,OAAO,IAAI,CAAC8F,WAAW,CAACnG,CAAC,CAACO,EAAE,GAAGP,CAAC,CAACM,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3C;;IAEA;IACA,QAAQ,IAAI,CAACiE,UAAU,CAACvE,CAAC,CAAC;MAC1B,KAAK,CAAC;QAAE;QACJ,OAAO,CAAC,IAAI,CAACO,EAAE,KAAKP,CAAC,CAACO,EAAE,GAAGM,GAAG,GAAGC,KAAK,EAAEX,IAAI,CAAC;MACjD,KAAK,CAAC,CAAC;QAAE;QACL,OAAO,CAACA,IAAI,EAAE,IAAI,CAAC;IACvB;IAEA,IAAIwB,IAAI,GAAG,IAAI,CAACpB,EAAE,GAAGP,CAAC,CAACO,EAAE;IACzB,IAAIsD,CAAC,GAAG7D,CAAC,CAAC4B,GAAG,CAAC,CAAC;IACf,IAAIwE,QAAQ,GAAG,IAAI,CAAC9F,EAAE;IACtB,IAAI+F,OAAO,GAAGD,QAAQ,CAAC/F,MAAM;IAC7B,IAAIa,MAAM,GAAGlB,CAAC,CAACM,EAAE,CAACD,MAAM;IACxB,IAAIiG,IAAI,GAAG,EAAE;IACb,IAAIC,KAAK;IAET,IAAIC,IAAI,GAAG,IAAIzG,UAAU,CAAC,EAAE,EAAE,CAAC,EAAED,SAAS,CAAC;IAE3C,OAAOuG,OAAO,EAAE;MACZG,IAAI,CAAClG,EAAE,CAACmG,OAAO,CAACL,QAAQ,CAAC,EAAEC,OAAO,CAAC,CAAC;MACpCG,IAAI,GAAG,IAAIzG,UAAU,CAACyG,IAAI,CAAClG,EAAE,EAAE,CAAC,EAAER,SAAS,CAAC;MAE5C,IAAI0G,IAAI,CAACjC,UAAU,CAACvE,CAAC,CAAC,GAAG,CAAC,EAAE;QACxBsG,IAAI,CAACtE,IAAI,CAAC,CAAC,CAAC;QACZ;MACJ;MACA,IAAIwE,IAAI,CAACjG,EAAE,KAAK,CAAC,EAAE;QACfgG,KAAK,GAAG,CAAC;MACb,CAAC,MACI;QACD,IAAIG,IAAI,GAAGF,IAAI,CAAClG,EAAE,CAACD,MAAM;UAAEsG,IAAI,GAAG9C,CAAC,CAACvD,EAAE,CAACD,MAAM;QAC7C,IAAIuG,KAAK,GAAGJ,IAAI,CAAClG,EAAE,CAACoG,IAAI,GAAC,CAAC,CAAC,GAACjG,eAAe,GAAG+F,IAAI,CAAClG,EAAE,CAACoG,IAAI,GAAC,CAAC,CAAC;QAC7D,IAAIG,KAAK,GAAGhD,CAAC,CAACvD,EAAE,CAACqG,IAAI,GAAC,CAAC,CAAC,GAAClG,eAAe,GAAGoD,CAAC,CAACvD,EAAE,CAACqG,IAAI,GAAC,CAAC,CAAC;QACvD,IAAIH,IAAI,CAAClG,EAAE,CAACD,MAAM,GAAGwD,CAAC,CAACvD,EAAE,CAACD,MAAM,EAAE;UAC9B;UACA;UACAuG,KAAK,GAAG,CAACA,KAAK,GAAC,CAAC,IAAEnG,eAAe;QACrC;QACA8F,KAAK,GAAG5D,IAAI,CAACmE,IAAI,CAACF,KAAK,GAACC,KAAK,CAAC;MAClC;MACA,GAAG;QACC,IAAIE,KAAK,GAAGlD,CAAC,CAAC+B,mBAAmB,CAACW,KAAK,CAAC;QACxC,IAAIQ,KAAK,CAACxC,UAAU,CAACiC,IAAI,CAAC,IAAI,CAAC,EAAE;UAC7B;QACJ;QACAD,KAAK,EAAE;MACX,CAAC,QAAQA,KAAK;MAEdD,IAAI,CAACtE,IAAI,CAACuE,KAAK,CAAC;MAChB,IAAI,CAACA,KAAK,EAAE;QACR;MACJ;MACA,IAAI9B,IAAI,GAAG+B,IAAI,CAAC5C,QAAQ,CAACmD,KAAK,CAAC;MAC/BP,IAAI,CAAClG,EAAE,GAAGmE,IAAI,CAACnE,EAAE,CAACsE,KAAK,CAAC,CAAC;IAC7B;IAEA,OAAO,CAAC,IAAI7E,UAAU,CAACuG,IAAI,CAACpE,OAAO,CAAC,CAAC,EAAEP,IAAI,EAAE7B,SAAS,CAAC,EAChD,IAAIC,UAAU,CAACyG,IAAI,CAAClG,EAAE,EAAE,IAAI,CAACC,EAAE,EAAET,SAAS,CAAC,CAAC;EACvD,CAAC;;EAED;EACA;EACA;EACAC,UAAU,CAACqB,SAAS,CAAC+E,WAAW,GAAG,UAASnG,CAAC,EAAE;IAC3C,IAAI8C,CAAC;IACL9C,CAAC,GAAG,CAACA,CAAC;IACN,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,MAAM,IAAIsB,KAAK,CAAC,gBAAgB,CAAC;IACrC;IAEA,IAAI0F,GAAG,GAAGhH,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACxB,IAAI2B,IAAI,GAAG,IAAI,CAACpB,EAAE,GAAGyG,GAAG;IACxBhH,CAAC,GAAG2C,IAAI,CAACf,GAAG,CAAC5B,CAAC,CAAC;IAEf,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAIS,eAAe,EAAE;MAC/B,MAAM,IAAIa,KAAK,CAAC,uBAAuB,CAAC;IAC5C;IAEA,IAAI,IAAI,CAACf,EAAE,KAAK,CAAC,EAAE;MACf,OAAO,CAACJ,IAAI,EAAEA,IAAI,CAAC;IACvB;IAEA,IAAIH,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC,EAAE;MACrB,OAAO,CAAE2B,IAAI,KAAK,CAAC,GAAI,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI7B,UAAU,CAAC,IAAI,CAACO,EAAE,EAAEqB,IAAI,EAAE7B,SAAS,CAAC,EAAEK,IAAI,CAAC;IACvF;;IAEA;;IAEA;IACA,IAAI,IAAI,CAACG,EAAE,CAACD,MAAM,KAAK,CAAC,EAAE;MACtB,IAAI4G,CAAC,GAAG,IAAIlH,UAAU,CAAC,CAAE,IAAI,CAACO,EAAE,CAAC,CAAC,CAAC,GAAGN,CAAC,GAAI,CAAC,CAAC,EAAE,CAAC,EAAEF,SAAS,CAAC;MAC5DgD,CAAC,GAAG,IAAI/C,UAAU,CAAC,CAAE,IAAI,CAACO,EAAE,CAAC,CAAC,CAAC,GAAGN,CAAC,GAAI,CAAC,CAAC,EAAE,CAAC,EAAEF,SAAS,CAAC;MACxD,IAAI6B,IAAI,GAAG,CAAC,EAAE;QACVsF,CAAC,GAAGA,CAAC,CAACtD,MAAM,CAAC,CAAC;MAClB;MACA,IAAI,IAAI,CAACpD,EAAE,GAAG,CAAC,EAAE;QACbuC,CAAC,GAAGA,CAAC,CAACa,MAAM,CAAC,CAAC;MAClB;MACA,OAAO,CAACsD,CAAC,EAAEnE,CAAC,CAAC;IACjB;IAEA,IAAI5B,MAAM,GAAG,IAAI,CAACZ,EAAE,CAACsE,KAAK,CAAC,CAAC;IAC5B,IAAI0B,IAAI,GAAG,IAAIzD,KAAK,CAAC3B,MAAM,CAACb,MAAM,CAAC;IACnC,IAAImG,IAAI,GAAG,CAAC;IACZ,IAAI/B,IAAI,GAAG,CAAC;IACZ,IAAIjD,CAAC,GAAG,CAAC;IACT,IAAI+E,KAAK;IAET,OAAOrF,MAAM,CAACb,MAAM,EAAE;MAClBmG,IAAI,GAAGA,IAAI,GAAG/F,eAAe,GAAGS,MAAM,CAACA,MAAM,CAACb,MAAM,GAAG,CAAC,CAAC;MACzD,IAAImG,IAAI,GAAGxG,CAAC,EAAE;QACVsG,IAAI,CAAC9E,CAAC,EAAE,CAAC,GAAG,CAAC;QACbN,MAAM,CAACgG,GAAG,CAAC,CAAC;QACZzC,IAAI,GAAGhE,eAAe,GAAGgE,IAAI,GAAG+B,IAAI;QACpC;MACJ;MACA,IAAIA,IAAI,KAAK,CAAC,EAAE;QACZD,KAAK,GAAG,CAAC;MACb,CAAC,MACI;QACDA,KAAK,GAAIC,IAAI,GAAGxG,CAAC,GAAI,CAAC;MAC1B;MAEA,IAAI+G,KAAK,GAAG/G,CAAC,GAAGuG,KAAK;MACrB9B,IAAI,GAAG+B,IAAI,GAAGO,KAAK;MACnBT,IAAI,CAAC9E,CAAC,EAAE,CAAC,GAAG+E,KAAK;MACjB,IAAI,CAACA,KAAK,EAAE;QACRrF,MAAM,CAACgG,GAAG,CAAC,CAAC;QACZ;MACJ;MAEAhG,MAAM,CAACgG,GAAG,CAAC,CAAC;MACZV,IAAI,GAAG/B,IAAI;IACf;IAEA3B,CAAC,GAAG,IAAI/C,UAAU,CAAC,CAAC0E,IAAI,CAAC,EAAE,CAAC,EAAE3E,SAAS,CAAC;IACxC,IAAI,IAAI,CAACS,EAAE,GAAG,CAAC,EAAE;MACbuC,CAAC,GAAGA,CAAC,CAACa,MAAM,CAAC,CAAC;IAClB;IACA,OAAO,CAAC,IAAI5D,UAAU,CAACuG,IAAI,CAACpE,OAAO,CAAC,CAAC,EAAEP,IAAI,EAAE7B,SAAS,CAAC,EAAEgD,CAAC,CAAC;EAC/D,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI/C,UAAU,CAACqB,SAAS,CAAC+F,MAAM,GAAG,YAAW;IACrC,IAAIjG,MAAM,GAAG,IAAI,CAACZ,EAAE;IACpB,OAAO,IAAI,CAACC,EAAE,KAAK,CAAC,IAAIW,MAAM,CAACb,MAAM,KAAK,CAAC,IAAKa,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,KAAM,CAAC;EACxE,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACInB,UAAU,CAACqB,SAAS,CAACgG,KAAK,GAAG,YAAW;IACpC,OAAO,CAAC,IAAI,CAACD,MAAM,CAAC,CAAC;EACzB,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpH,UAAU,CAACqB,SAAS,CAACO,IAAI,GAAG,YAAW;IACnC,OAAO,IAAI,CAACpB,EAAE;EAClB,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,UAAU,CAACqB,SAAS,CAACiG,UAAU,GAAG,YAAW;IACzC,OAAO,IAAI,CAAC9G,EAAE,GAAG,CAAC;EACtB,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,UAAU,CAACqB,SAAS,CAACkG,UAAU,GAAG,YAAW;IACzC,OAAO,IAAI,CAAC/G,EAAE,GAAG,CAAC;EACtB,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,UAAU,CAACqB,SAAS,CAACmG,MAAM,GAAG,YAAW;IACrC,OAAO,IAAI,CAAChH,EAAE,KAAK,CAAC;EACxB,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,UAAU,CAACqB,SAAS,CAACoG,KAAK,GAAG,UAASxH,CAAC,EAAE;IACrCA,CAAC,GAAG,CAACA,CAAC;IACN,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,IAAI;IACf;IACA,IAAI2C,IAAI,CAACf,GAAG,CAAC5B,CAAC,CAAC,GAAGyH,MAAM,CAACC,OAAO,CAAC,EAAE;MAC/B,MAAM,IAAIpG,KAAK,CAAC,wCAAwC,CAAC;IAC7D;IACA;IACA,IAAI,IAAI,CAACf,EAAE,KAAK,CAAC,EAAE;MACf,OAAOJ,IAAI;IACf;IACA,IAAIH,CAAC,GAAG,CAAC,EAAE;MACP,IAAI+F,CAAC,GAAG,IAAIhG,UAAU,CAAC,IAAI,CAACO,EAAE,CAACsE,KAAK,CAAC,CAAC,EAAE,IAAI,CAACrE,EAAE,EAAET,SAAS,CAAC;MAE3D,OAAOE,CAAC,IAAIU,qBAAqB,EAAEV,CAAC,IAAIU,qBAAqB,EAAE;QAC3DqF,CAAC,CAACzF,EAAE,CAACmG,OAAO,CAAC,CAAC,CAAC;MACnB;MACA,IAAIzG,CAAC,IAAI,CAAC,EACN,OAAO+F,CAAC;MACZA,CAAC,CAACxF,EAAE,GAAG,CAAC;MACRwF,CAAC,GAAGA,CAAC,CAACH,mBAAmB,CAACjD,IAAI,CAACgF,GAAG,CAAC,EAAE,EAAE3H,CAAC,CAAC,CAAC;MAC1C,OAAQ,IAAI,CAACO,EAAE,GAAG,CAAC,GAAGwF,CAAC,CAACpC,MAAM,CAAC,CAAC,GAAGoC,CAAC;IACxC,CAAC,MAAM,IAAI,CAAC/F,CAAC,IAAI,IAAI,CAACM,EAAE,CAACD,MAAM,GAACK,qBAAqB,EAAE;MACnD,OAAOP,IAAI;IACf,CAAC,MAAM;MACH,IAAI4F,CAAC,GAAG,IAAIhG,UAAU,CAAC,IAAI,CAACO,EAAE,CAACsE,KAAK,CAAC,CAAC,EAAE,IAAI,CAACrE,EAAE,EAAET,SAAS,CAAC;MAE3D,KAAKE,CAAC,GAAG,CAACA,CAAC,EAAEA,CAAC,IAAIU,qBAAqB,EAAEV,CAAC,IAAIU,qBAAqB,EAAE;QACjEqF,CAAC,CAACzF,EAAE,CAACsH,KAAK,CAAC,CAAC;MAChB;MACA,OAAQ5H,CAAC,IAAI,CAAC,GAAI+F,CAAC,GAAGA,CAAC,CAACI,WAAW,CAACxD,IAAI,CAACgF,GAAG,CAAC,EAAE,EAAE3H,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;EACJ,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACID,UAAU,CAACqB,SAAS,CAACuG,GAAG,GAAG,UAAS3H,CAAC,EAAE;IACnC,IAAI,IAAI,CAACqF,MAAM,CAAC,CAAC,EAAE;MACf,IAAI,IAAI,CAAC9E,EAAE,GAAG,CAAC,EAAE;QACb,OAAO,IAAI;MACf,CAAC,MACI;QACD,OAAOR,UAAU,CAACC,CAAC,CAAC,CAACoH,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAACzD,MAAM,CAAC,CAAC;MACvD;IACJ;IAEA3D,CAAC,GAAGD,UAAU,CAACC,CAAC,CAAC;IACjB,IAAIA,CAAC,CAACO,EAAE,KAAK,CAAC,EAAE;MACZ,OAAOM,GAAG;IACd,CAAC,MACI,IAAIb,CAAC,CAACO,EAAE,GAAG,CAAC,EAAE;MACf,IAAI,IAAI,CAACA,EAAE,KAAK,CAAC,EAAE;QACf,MAAM,IAAIe,KAAK,CAAC,gBAAgB,CAAC;MACrC,CAAC,MACI;QACD,OAAOnB,IAAI;MACf;IACJ;IACA,IAAI,IAAI,CAACI,EAAE,KAAK,CAAC,EAAE;MACf,OAAOJ,IAAI;IACf;IACA,IAAIH,CAAC,CAACqF,MAAM,CAAC,CAAC,EAAE;MACZ,OAAO,IAAI;IACf;IAEA,IAAIrF,CAAC,CAACuE,UAAU,CAACmD,OAAO,CAAC,GAAG,CAAC,EAAE;MAC3B,MAAM,IAAIpG,KAAK,CAAC,sCAAsC,CAAC;IAC3D;IACA,IAAIiB,CAAC,GAAG,IAAI;IACZ,IAAIsF,GAAG,GAAGhH,GAAG;IACb,IAAIiH,GAAG,GAAG/H,UAAU,CAACkB,KAAK,CAAC,CAAC,CAAC;IAE7B,OAAOjB,CAAC,CAACqH,UAAU,CAAC,CAAC,EAAE;MACnB,IAAIrH,CAAC,CAACoH,KAAK,CAAC,CAAC,EAAE;QACXS,GAAG,GAAGA,GAAG,CAACpE,QAAQ,CAAClB,CAAC,CAAC;QACrB,IAAIvC,CAAC,CAACqF,MAAM,CAAC,CAAC,EAAE;UACZ,OAAOwC,GAAG;QACd;MACJ;MACAtF,CAAC,GAAGA,CAAC,CAAC+C,MAAM,CAAC,CAAC;MACdtF,CAAC,GAAGA,CAAC,CAACgG,QAAQ,CAAC8B,GAAG,CAAC;IACvB;IAEA,OAAOD,GAAG;EACd,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI9H,UAAU,CAACqB,SAAS,CAAC2G,MAAM,GAAG,UAASC,QAAQ,EAAEC,OAAO,EAAE;IACtD,IAAIC,MAAM,GAAGrH,GAAG;IAChB,IAAIF,IAAI,GAAG,IAAI;IAEf,OAAOqH,QAAQ,CAACX,UAAU,CAAC,CAAC,EAAE;MAC1B,IAAIW,QAAQ,CAACZ,KAAK,CAAC,CAAC,EAAE;QAClBc,MAAM,GAAGA,MAAM,CAACzE,QAAQ,CAAC9C,IAAI,CAAC,CAACuF,SAAS,CAAC+B,OAAO,CAAC;MACrD;MAEAD,QAAQ,GAAGA,QAAQ,CAAChC,QAAQ,CAACjG,UAAU,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC;MACjD,IAAI+G,QAAQ,CAACX,UAAU,CAAC,CAAC,EAAE;QACvB1G,IAAI,GAAGA,IAAI,CAAC2E,MAAM,CAAC,CAAC,CAACY,SAAS,CAAC+B,OAAO,CAAC;MAC3C;IACJ;IAEA,OAAOC,MAAM;EACjB,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACInI,UAAU,CAACqB,SAAS,CAAC+G,GAAG,GAAG,YAAW;IAClC,QAAQ,IAAI,CAAC5H,EAAE;MACf,KAAK,CAAC;QAAG,OAAO,CAAC6H,QAAQ;MACzB,KAAK,CAAC,CAAC;QAAE,OAAOC,GAAG;MACnB,QAAQ,CAAC;IACT;IAEA,IAAI3F,CAAC,GAAG,IAAI,CAACpC,EAAE,CAACD,MAAM;IAEtB,IAAIqC,CAAC,GAAChC,qBAAqB,GAAG,EAAE,EAAE;MAC9B,OAAOiC,IAAI,CAACwF,GAAG,CAAC,IAAI,CAAClG,OAAO,CAAC,CAAC,CAAC;IACnC;IAEA,IAAIqG,CAAC,GAAG3F,IAAI,CAACmE,IAAI,CAAC,EAAE,GAACpG,qBAAqB,CAAC;IAC3C,IAAI6H,YAAY,GAAG,IAAI,CAACjI,EAAE,CAACsE,KAAK,CAAClC,CAAC,GAAG4F,CAAC,CAAC;IACvC,OAAO3F,IAAI,CAACwF,GAAG,CAAE,IAAIpI,UAAU,CAACwI,YAAY,EAAE,CAAC,EAAEzI,SAAS,CAAC,CAAEmC,OAAO,CAAC,CAAC,CAAC,GAAG,CAACS,CAAC,GAAG4F,CAAC,IAAI3F,IAAI,CAACwF,GAAG,CAAC1H,eAAe,CAAC;EACjH,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIV,UAAU,CAACqB,SAAS,CAACa,OAAO,GAAG,YAAW;IACtC,OAAOsB,QAAQ,CAAC,IAAI,CAAClC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItB,UAAU,CAACqB,SAAS,CAACoH,SAAS,GAAG,YAAW;IACxC,OAAOjF,QAAQ,CAAC,IAAI,CAAClC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,CAAC;;EAGD;AACJ;AACA;AACA;EACItB,UAAU,CAACqB,SAAS,CAACqH,MAAM,GAAG,YAAY;IACtC,OAAO,IAAI,CAACnI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAIoH,OAAO,GAAG3H,UAAU,CAAC,UAAU,CAAC;EACpC;EACA;EACAA,UAAU,CAAC2H,OAAO,GAAGA,OAAO;EAE5B,CAAC,YAAW;IACR,SAASgB,SAASA,CAACC,EAAE,EAAE;MACnB,OAAO,UAAS9E,CAAC,EAAE;QACf,OAAO8E,EAAE,CAACC,IAAI,CAAC7I,UAAU,CAAC8D,CAAC,CAAC,CAAC;MACjC,CAAC;IACL;IAEA,SAASgF,UAAUA,CAACF,EAAE,EAAE;MACpB,OAAO,UAAS9E,CAAC,EAAEC,CAAC,EAAE;QAClB,OAAO6E,EAAE,CAACC,IAAI,CAAC7I,UAAU,CAAC8D,CAAC,CAAC,EAAE9D,UAAU,CAAC+D,CAAC,CAAC,CAAC;MAChD,CAAC;IACL;IAEA,SAASgF,WAAWA,CAACH,EAAE,EAAE;MACrB,OAAO,UAAS9E,CAAC,EAAEC,CAAC,EAAErB,CAAC,EAAE;QACrB,OAAOkG,EAAE,CAACC,IAAI,CAAC7I,UAAU,CAAC8D,CAAC,CAAC,EAAE9D,UAAU,CAAC+D,CAAC,CAAC,EAAE/D,UAAU,CAAC0C,CAAC,CAAC,CAAC;MAC/D,CAAC;IACL;IAEA,CAAC,YAAW;MACR,IAAIjB,CAAC,EAAEmH,EAAE;MACT,IAAII,KAAK,GAAG,0GAA0G,CAAC5H,KAAK,CAAC,GAAG,CAAC;MACjI,IAAI6H,MAAM,GAAG,+EAA+E,CAAC7H,KAAK,CAAC,GAAG,CAAC;MACvG,IAAI8H,OAAO,GAAG,CAAC,QAAQ,CAAC;MAExB,KAAKzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuH,KAAK,CAAC1I,MAAM,EAAEmB,CAAC,EAAE,EAAE;QAC/BmH,EAAE,GAAGI,KAAK,CAACvH,CAAC,CAAC;QACbzB,UAAU,CAAC4I,EAAE,CAAC,GAAGD,SAAS,CAAC3I,UAAU,CAACqB,SAAS,CAACuH,EAAE,CAAC,CAAC;MACxD;MAEA,KAAKnH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwH,MAAM,CAAC3I,MAAM,EAAEmB,CAAC,EAAE,EAAE;QAChCmH,EAAE,GAAGK,MAAM,CAACxH,CAAC,CAAC;QACdzB,UAAU,CAAC4I,EAAE,CAAC,GAAGE,UAAU,CAAC9I,UAAU,CAACqB,SAAS,CAACuH,EAAE,CAAC,CAAC;MACzD;MAEA,KAAKnH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyH,OAAO,CAAC5I,MAAM,EAAEmB,CAAC,EAAE,EAAE;QACjCmH,EAAE,GAAGM,OAAO,CAACzH,CAAC,CAAC;QACfzB,UAAU,CAAC4I,EAAE,CAAC,GAAGG,WAAW,CAAC/I,UAAU,CAACqB,SAAS,CAACuH,EAAE,CAAC,CAAC;MAC1D;MAEA5I,UAAU,CAACyH,KAAK,GAAG,UAASjF,CAAC,EAAEvC,CAAC,EAAE;QAC9B,OAAOD,UAAU,CAACwC,CAAC,CAAC,CAACiF,KAAK,CAACxH,CAAC,CAAC;MACjC,CAAC;IACL,CAAC,EAAE,CAAC;EACR,CAAC,EAAE,CAAC;EAEJH,OAAO,CAACqJ,QAAQ,GAAGnJ,UAAU,CAAC,CAAC;AAC/B,CAAC,EAAE,OAAOF,OAAO,KAAK,WAAW,GAAGA,OAAO,GAAG,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}