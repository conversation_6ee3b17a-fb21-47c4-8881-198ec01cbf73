{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousFriday\n * @category Weekday Helpers\n * @summary When is the previous Friday?\n *\n * @description\n * When is the previous Friday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Friday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Friday before Jun, 19, 2021?\n * const result = previousFriday(new Date(2021, 5, 19))\n * //=> Fri June 18 2021 00:00:00\n */\n\nexport default function previousFriday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 5);\n}", "map": {"version": 3, "names": ["requiredArgs", "previousDay", "previousFriday", "date", "arguments"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/previousFriday/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousFriday\n * @category Weekday Helpers\n * @summary When is the previous Friday?\n *\n * @description\n * When is the previous Friday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Friday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Friday before Jun, 19, 2021?\n * const result = previousFriday(new Date(2021, 5, 19))\n * //=> Fri June 18 2021 00:00:00\n */\n\nexport default function previousFriday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 5);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,OAAOC,WAAW,MAAM,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC3CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOH,WAAW,CAACE,IAAI,EAAE,CAAC,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module"}