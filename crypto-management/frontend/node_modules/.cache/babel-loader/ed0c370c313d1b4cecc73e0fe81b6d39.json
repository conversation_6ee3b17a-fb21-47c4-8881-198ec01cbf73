{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { bind, each, isFunction, isString, indexOf } from 'zrender/lib/core/util.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as throttle from '../../util/throttle.js';\nimport DataZoomView from './DataZoomView.js';\nimport { linearMap, asc, parsePercent } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport { getAxisMainType, collectReferCoordSysModelInfo } from './helper.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { createSymbol, symbolBuildProxies } from '../../util/symbol.js';\nimport { deprecateLog } from '../../util/log.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nvar Rect = graphic.Rect; // Constants\n\nvar DEFAULT_LOCATION_EDGE_GAP = 7;\nvar DEFAULT_FRAME_BORDER_WIDTH = 1;\nvar DEFAULT_FILLER_SIZE = 30;\nvar DEFAULT_MOVE_HANDLE_SIZE = 7;\nvar HORIZONTAL = 'horizontal';\nvar VERTICAL = 'vertical';\nvar LABEL_GAP = 5;\nvar SHOW_DATA_SHADOW_SERIES_TYPE = ['line', 'bar', 'candlestick', 'scatter'];\nvar REALTIME_ANIMATION_CONFIG = {\n  easing: 'cubicOut',\n  duration: 100,\n  delay: 0\n};\nvar SliderZoomView = /** @class */\nfunction (_super) {\n  __extends(SliderZoomView, _super);\n  function SliderZoomView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SliderZoomView.type;\n    _this._displayables = {};\n    return _this;\n  }\n  SliderZoomView.prototype.init = function (ecModel, api) {\n    this.api = api; // A unique handler for each dataZoom component\n\n    this._onBrush = bind(this._onBrush, this);\n    this._onBrushEnd = bind(this._onBrushEnd, this);\n  };\n  SliderZoomView.prototype.render = function (dataZoomModel, ecModel, api, payload) {\n    _super.prototype.render.apply(this, arguments);\n    throttle.createOrUpdate(this, '_dispatchZoomAction', dataZoomModel.get('throttle'), 'fixRate');\n    this._orient = dataZoomModel.getOrient();\n    if (dataZoomModel.get('show') === false) {\n      this.group.removeAll();\n      return;\n    }\n    if (dataZoomModel.noTarget()) {\n      this._clear();\n      this.group.removeAll();\n      return;\n    } // Notice: this._resetInterval() should not be executed when payload.type\n    // is 'dataZoom', origin this._range should be maintained, otherwise 'pan'\n    // or 'zoom' info will be missed because of 'throttle' of this.dispatchAction,\n\n    if (!payload || payload.type !== 'dataZoom' || payload.from !== this.uid) {\n      this._buildView();\n    }\n    this._updateView();\n  };\n  SliderZoomView.prototype.dispose = function () {\n    this._clear();\n    _super.prototype.dispose.apply(this, arguments);\n  };\n  SliderZoomView.prototype._clear = function () {\n    throttle.clear(this, '_dispatchZoomAction');\n    var zr = this.api.getZr();\n    zr.off('mousemove', this._onBrush);\n    zr.off('mouseup', this._onBrushEnd);\n  };\n  SliderZoomView.prototype._buildView = function () {\n    var thisGroup = this.group;\n    thisGroup.removeAll();\n    this._brushing = false;\n    this._displayables.brushRect = null;\n    this._resetLocation();\n    this._resetInterval();\n    var barGroup = this._displayables.sliderGroup = new graphic.Group();\n    this._renderBackground();\n    this._renderHandle();\n    this._renderDataShadow();\n    thisGroup.add(barGroup);\n    this._positionGroup();\n  };\n  SliderZoomView.prototype._resetLocation = function () {\n    var dataZoomModel = this.dataZoomModel;\n    var api = this.api;\n    var showMoveHandle = dataZoomModel.get('brushSelect');\n    var moveHandleSize = showMoveHandle ? DEFAULT_MOVE_HANDLE_SIZE : 0; // If some of x/y/width/height are not specified,\n    // auto-adapt according to target grid.\n\n    var coordRect = this._findCoordRect();\n    var ecSize = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    }; // Default align by coordinate system rect.\n\n    var positionInfo = this._orient === HORIZONTAL ? {\n      // Why using 'right', because right should be used in vertical,\n      // and it is better to be consistent for dealing with position param merge.\n      right: ecSize.width - coordRect.x - coordRect.width,\n      top: ecSize.height - DEFAULT_FILLER_SIZE - DEFAULT_LOCATION_EDGE_GAP - moveHandleSize,\n      width: coordRect.width,\n      height: DEFAULT_FILLER_SIZE\n    } : {\n      right: DEFAULT_LOCATION_EDGE_GAP,\n      top: coordRect.y,\n      width: DEFAULT_FILLER_SIZE,\n      height: coordRect.height\n    }; // Do not write back to option and replace value 'ph', because\n    // the 'ph' value should be recalculated when resize.\n\n    var layoutParams = layout.getLayoutParams(dataZoomModel.option); // Replace the placeholder value.\n\n    each(['right', 'top', 'width', 'height'], function (name) {\n      if (layoutParams[name] === 'ph') {\n        layoutParams[name] = positionInfo[name];\n      }\n    });\n    var layoutRect = layout.getLayoutRect(layoutParams, ecSize);\n    this._location = {\n      x: layoutRect.x,\n      y: layoutRect.y\n    };\n    this._size = [layoutRect.width, layoutRect.height];\n    this._orient === VERTICAL && this._size.reverse();\n  };\n  SliderZoomView.prototype._positionGroup = function () {\n    var thisGroup = this.group;\n    var location = this._location;\n    var orient = this._orient; // Just use the first axis to determine mapping.\n\n    var targetAxisModel = this.dataZoomModel.getFirstTargetAxisModel();\n    var inverse = targetAxisModel && targetAxisModel.get('inverse');\n    var sliderGroup = this._displayables.sliderGroup;\n    var otherAxisInverse = (this._dataShadowInfo || {}).otherAxisInverse; // Transform barGroup.\n\n    sliderGroup.attr(orient === HORIZONTAL && !inverse ? {\n      scaleY: otherAxisInverse ? 1 : -1,\n      scaleX: 1\n    } : orient === HORIZONTAL && inverse ? {\n      scaleY: otherAxisInverse ? 1 : -1,\n      scaleX: -1\n    } : orient === VERTICAL && !inverse ? {\n      scaleY: otherAxisInverse ? -1 : 1,\n      scaleX: 1,\n      rotation: Math.PI / 2\n    } // Dont use Math.PI, considering shadow direction.\n    : {\n      scaleY: otherAxisInverse ? -1 : 1,\n      scaleX: -1,\n      rotation: Math.PI / 2\n    }); // Position barGroup\n\n    var rect = thisGroup.getBoundingRect([sliderGroup]);\n    thisGroup.x = location.x - rect.x;\n    thisGroup.y = location.y - rect.y;\n    thisGroup.markRedraw();\n  };\n  SliderZoomView.prototype._getViewExtent = function () {\n    return [0, this._size[0]];\n  };\n  SliderZoomView.prototype._renderBackground = function () {\n    var dataZoomModel = this.dataZoomModel;\n    var size = this._size;\n    var barGroup = this._displayables.sliderGroup;\n    var brushSelect = dataZoomModel.get('brushSelect');\n    barGroup.add(new Rect({\n      silent: true,\n      shape: {\n        x: 0,\n        y: 0,\n        width: size[0],\n        height: size[1]\n      },\n      style: {\n        fill: dataZoomModel.get('backgroundColor')\n      },\n      z2: -40\n    })); // Click panel, over shadow, below handles.\n\n    var clickPanel = new Rect({\n      shape: {\n        x: 0,\n        y: 0,\n        width: size[0],\n        height: size[1]\n      },\n      style: {\n        fill: 'transparent'\n      },\n      z2: 0,\n      onclick: bind(this._onClickPanel, this)\n    });\n    var zr = this.api.getZr();\n    if (brushSelect) {\n      clickPanel.on('mousedown', this._onBrushStart, this);\n      clickPanel.cursor = 'crosshair';\n      zr.on('mousemove', this._onBrush);\n      zr.on('mouseup', this._onBrushEnd);\n    } else {\n      zr.off('mousemove', this._onBrush);\n      zr.off('mouseup', this._onBrushEnd);\n    }\n    barGroup.add(clickPanel);\n  };\n  SliderZoomView.prototype._renderDataShadow = function () {\n    var info = this._dataShadowInfo = this._prepareDataShadowInfo();\n    this._displayables.dataShadowSegs = [];\n    if (!info) {\n      return;\n    }\n    var size = this._size;\n    var oldSize = this._shadowSize || [];\n    var seriesModel = info.series;\n    var data = seriesModel.getRawData();\n    var otherDim = seriesModel.getShadowDim ? seriesModel.getShadowDim() // @see candlestick\n    : info.otherDim;\n    if (otherDim == null) {\n      return;\n    }\n    var polygonPts = this._shadowPolygonPts;\n    var polylinePts = this._shadowPolylinePts; // Not re-render if data doesn't change.\n\n    if (data !== this._shadowData || otherDim !== this._shadowDim || size[0] !== oldSize[0] || size[1] !== oldSize[1]) {\n      var otherDataExtent_1 = data.getDataExtent(otherDim); // Nice extent.\n\n      var otherOffset = (otherDataExtent_1[1] - otherDataExtent_1[0]) * 0.3;\n      otherDataExtent_1 = [otherDataExtent_1[0] - otherOffset, otherDataExtent_1[1] + otherOffset];\n      var otherShadowExtent_1 = [0, size[1]];\n      var thisShadowExtent = [0, size[0]];\n      var areaPoints_1 = [[size[0], 0], [0, 0]];\n      var linePoints_1 = [];\n      var step_1 = thisShadowExtent[1] / (data.count() - 1);\n      var thisCoord_1 = 0; // Optimize for large data shadow\n\n      var stride_1 = Math.round(data.count() / size[0]);\n      var lastIsEmpty_1;\n      data.each([otherDim], function (value, index) {\n        if (stride_1 > 0 && index % stride_1) {\n          thisCoord_1 += step_1;\n          return;\n        } // FIXME\n        // Should consider axis.min/axis.max when drawing dataShadow.\n        // FIXME\n        // 应该使用统一的空判断？还是在list里进行空判断？\n\n        var isEmpty = value == null || isNaN(value) || value === ''; // See #4235.\n\n        var otherCoord = isEmpty ? 0 : linearMap(value, otherDataExtent_1, otherShadowExtent_1, true); // Attempt to draw data shadow precisely when there are empty value.\n\n        if (isEmpty && !lastIsEmpty_1 && index) {\n          areaPoints_1.push([areaPoints_1[areaPoints_1.length - 1][0], 0]);\n          linePoints_1.push([linePoints_1[linePoints_1.length - 1][0], 0]);\n        } else if (!isEmpty && lastIsEmpty_1) {\n          areaPoints_1.push([thisCoord_1, 0]);\n          linePoints_1.push([thisCoord_1, 0]);\n        }\n        areaPoints_1.push([thisCoord_1, otherCoord]);\n        linePoints_1.push([thisCoord_1, otherCoord]);\n        thisCoord_1 += step_1;\n        lastIsEmpty_1 = isEmpty;\n      });\n      polygonPts = this._shadowPolygonPts = areaPoints_1;\n      polylinePts = this._shadowPolylinePts = linePoints_1;\n    }\n    this._shadowData = data;\n    this._shadowDim = otherDim;\n    this._shadowSize = [size[0], size[1]];\n    var dataZoomModel = this.dataZoomModel;\n    function createDataShadowGroup(isSelectedArea) {\n      var model = dataZoomModel.getModel(isSelectedArea ? 'selectedDataBackground' : 'dataBackground');\n      var group = new graphic.Group();\n      var polygon = new graphic.Polygon({\n        shape: {\n          points: polygonPts\n        },\n        segmentIgnoreThreshold: 1,\n        style: model.getModel('areaStyle').getAreaStyle(),\n        silent: true,\n        z2: -20\n      });\n      var polyline = new graphic.Polyline({\n        shape: {\n          points: polylinePts\n        },\n        segmentIgnoreThreshold: 1,\n        style: model.getModel('lineStyle').getLineStyle(),\n        silent: true,\n        z2: -19\n      });\n      group.add(polygon);\n      group.add(polyline);\n      return group;\n    } // let dataBackgroundModel = dataZoomModel.getModel('dataBackground');\n\n    for (var i = 0; i < 3; i++) {\n      var group = createDataShadowGroup(i === 1);\n      this._displayables.sliderGroup.add(group);\n      this._displayables.dataShadowSegs.push(group);\n    }\n  };\n  SliderZoomView.prototype._prepareDataShadowInfo = function () {\n    var dataZoomModel = this.dataZoomModel;\n    var showDataShadow = dataZoomModel.get('showDataShadow');\n    if (showDataShadow === false) {\n      return;\n    } // Find a representative series.\n\n    var result;\n    var ecModel = this.ecModel;\n    dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n      var seriesModels = dataZoomModel.getAxisProxy(axisDim, axisIndex).getTargetSeriesModels();\n      each(seriesModels, function (seriesModel) {\n        if (result) {\n          return;\n        }\n        if (showDataShadow !== true && indexOf(SHOW_DATA_SHADOW_SERIES_TYPE, seriesModel.get('type')) < 0) {\n          return;\n        }\n        var thisAxis = ecModel.getComponent(getAxisMainType(axisDim), axisIndex).axis;\n        var otherDim = getOtherDim(axisDim);\n        var otherAxisInverse;\n        var coordSys = seriesModel.coordinateSystem;\n        if (otherDim != null && coordSys.getOtherAxis) {\n          otherAxisInverse = coordSys.getOtherAxis(thisAxis).inverse;\n        }\n        otherDim = seriesModel.getData().mapDimension(otherDim);\n        result = {\n          thisAxis: thisAxis,\n          series: seriesModel,\n          thisDim: axisDim,\n          otherDim: otherDim,\n          otherAxisInverse: otherAxisInverse\n        };\n      }, this);\n    }, this);\n    return result;\n  };\n  SliderZoomView.prototype._renderHandle = function () {\n    var thisGroup = this.group;\n    var displayables = this._displayables;\n    var handles = displayables.handles = [null, null];\n    var handleLabels = displayables.handleLabels = [null, null];\n    var sliderGroup = this._displayables.sliderGroup;\n    var size = this._size;\n    var dataZoomModel = this.dataZoomModel;\n    var api = this.api;\n    var borderRadius = dataZoomModel.get('borderRadius') || 0;\n    var brushSelect = dataZoomModel.get('brushSelect');\n    var filler = displayables.filler = new Rect({\n      silent: brushSelect,\n      style: {\n        fill: dataZoomModel.get('fillerColor')\n      },\n      textConfig: {\n        position: 'inside'\n      }\n    });\n    sliderGroup.add(filler); // Frame border.\n\n    sliderGroup.add(new Rect({\n      silent: true,\n      subPixelOptimize: true,\n      shape: {\n        x: 0,\n        y: 0,\n        width: size[0],\n        height: size[1],\n        r: borderRadius\n      },\n      style: {\n        // deprecated option\n        stroke: dataZoomModel.get('dataBackgroundColor') || dataZoomModel.get('borderColor'),\n        lineWidth: DEFAULT_FRAME_BORDER_WIDTH,\n        fill: 'rgba(0,0,0,0)'\n      }\n    })); // Left and right handle to resize\n\n    each([0, 1], function (handleIndex) {\n      var iconStr = dataZoomModel.get('handleIcon');\n      if (!symbolBuildProxies[iconStr] && iconStr.indexOf('path://') < 0 && iconStr.indexOf('image://') < 0) {\n        // Compatitable with the old icon parsers. Which can use a path string without path://\n        iconStr = 'path://' + iconStr;\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateLog('handleIcon now needs \\'path://\\' prefix when using a path string');\n        }\n      }\n      var path = createSymbol(iconStr, -1, 0, 2, 2, null, true);\n      path.attr({\n        cursor: getCursor(this._orient),\n        draggable: true,\n        drift: bind(this._onDragMove, this, handleIndex),\n        ondragend: bind(this._onDragEnd, this),\n        onmouseover: bind(this._showDataInfo, this, true),\n        onmouseout: bind(this._showDataInfo, this, false),\n        z2: 5\n      });\n      var bRect = path.getBoundingRect();\n      var handleSize = dataZoomModel.get('handleSize');\n      this._handleHeight = parsePercent(handleSize, this._size[1]);\n      this._handleWidth = bRect.width / bRect.height * this._handleHeight;\n      path.setStyle(dataZoomModel.getModel('handleStyle').getItemStyle());\n      path.style.strokeNoScale = true;\n      path.rectHover = true;\n      path.ensureState('emphasis').style = dataZoomModel.getModel(['emphasis', 'handleStyle']).getItemStyle();\n      enableHoverEmphasis(path);\n      var handleColor = dataZoomModel.get('handleColor'); // deprecated option\n      // Compatitable with previous version\n\n      if (handleColor != null) {\n        path.style.fill = handleColor;\n      }\n      sliderGroup.add(handles[handleIndex] = path);\n      var textStyleModel = dataZoomModel.getModel('textStyle');\n      thisGroup.add(handleLabels[handleIndex] = new graphic.Text({\n        silent: true,\n        invisible: true,\n        style: createTextStyle(textStyleModel, {\n          x: 0,\n          y: 0,\n          text: '',\n          verticalAlign: 'middle',\n          align: 'center',\n          fill: textStyleModel.getTextColor(),\n          font: textStyleModel.getFont()\n        }),\n        z2: 10\n      }));\n    }, this); // Handle to move. Only visible when brushSelect is set true.\n\n    var actualMoveZone = filler;\n    if (brushSelect) {\n      var moveHandleHeight = parsePercent(dataZoomModel.get('moveHandleSize'), size[1]);\n      var moveHandle_1 = displayables.moveHandle = new graphic.Rect({\n        style: dataZoomModel.getModel('moveHandleStyle').getItemStyle(),\n        silent: true,\n        shape: {\n          r: [0, 0, 2, 2],\n          y: size[1] - 0.5,\n          height: moveHandleHeight\n        }\n      });\n      var iconSize = moveHandleHeight * 0.8;\n      var moveHandleIcon = displayables.moveHandleIcon = createSymbol(dataZoomModel.get('moveHandleIcon'), -iconSize / 2, -iconSize / 2, iconSize, iconSize, '#fff', true);\n      moveHandleIcon.silent = true;\n      moveHandleIcon.y = size[1] + moveHandleHeight / 2 - 0.5;\n      moveHandle_1.ensureState('emphasis').style = dataZoomModel.getModel(['emphasis', 'moveHandleStyle']).getItemStyle();\n      var moveZoneExpandSize = Math.min(size[1] / 2, Math.max(moveHandleHeight, 10));\n      actualMoveZone = displayables.moveZone = new graphic.Rect({\n        invisible: true,\n        shape: {\n          y: size[1] - moveZoneExpandSize,\n          height: moveHandleHeight + moveZoneExpandSize\n        }\n      });\n      actualMoveZone.on('mouseover', function () {\n        api.enterEmphasis(moveHandle_1);\n      }).on('mouseout', function () {\n        api.leaveEmphasis(moveHandle_1);\n      });\n      sliderGroup.add(moveHandle_1);\n      sliderGroup.add(moveHandleIcon);\n      sliderGroup.add(actualMoveZone);\n    }\n    actualMoveZone.attr({\n      draggable: true,\n      cursor: getCursor(this._orient),\n      drift: bind(this._onDragMove, this, 'all'),\n      ondragstart: bind(this._showDataInfo, this, true),\n      ondragend: bind(this._onDragEnd, this),\n      onmouseover: bind(this._showDataInfo, this, true),\n      onmouseout: bind(this._showDataInfo, this, false)\n    });\n  };\n  SliderZoomView.prototype._resetInterval = function () {\n    var range = this._range = this.dataZoomModel.getPercentRange();\n    var viewExtent = this._getViewExtent();\n    this._handleEnds = [linearMap(range[0], [0, 100], viewExtent, true), linearMap(range[1], [0, 100], viewExtent, true)];\n  };\n  SliderZoomView.prototype._updateInterval = function (handleIndex, delta) {\n    var dataZoomModel = this.dataZoomModel;\n    var handleEnds = this._handleEnds;\n    var viewExtend = this._getViewExtent();\n    var minMaxSpan = dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();\n    var percentExtent = [0, 100];\n    sliderMove(delta, handleEnds, viewExtend, dataZoomModel.get('zoomLock') ? 'all' : handleIndex, minMaxSpan.minSpan != null ? linearMap(minMaxSpan.minSpan, percentExtent, viewExtend, true) : null, minMaxSpan.maxSpan != null ? linearMap(minMaxSpan.maxSpan, percentExtent, viewExtend, true) : null);\n    var lastRange = this._range;\n    var range = this._range = asc([linearMap(handleEnds[0], viewExtend, percentExtent, true), linearMap(handleEnds[1], viewExtend, percentExtent, true)]);\n    return !lastRange || lastRange[0] !== range[0] || lastRange[1] !== range[1];\n  };\n  SliderZoomView.prototype._updateView = function (nonRealtime) {\n    var displaybles = this._displayables;\n    var handleEnds = this._handleEnds;\n    var handleInterval = asc(handleEnds.slice());\n    var size = this._size;\n    each([0, 1], function (handleIndex) {\n      // Handles\n      var handle = displaybles.handles[handleIndex];\n      var handleHeight = this._handleHeight;\n      handle.attr({\n        scaleX: handleHeight / 2,\n        scaleY: handleHeight / 2,\n        // This is a trick, by adding an extra tiny offset to let the default handle's end point align to the drag window.\n        // NOTE: It may affect some custom shapes a bit. But we prefer to have better result by default.\n        x: handleEnds[handleIndex] + (handleIndex ? -1 : 1),\n        y: size[1] / 2 - handleHeight / 2\n      });\n    }, this); // Filler\n\n    displaybles.filler.setShape({\n      x: handleInterval[0],\n      y: 0,\n      width: handleInterval[1] - handleInterval[0],\n      height: size[1]\n    });\n    var viewExtent = {\n      x: handleInterval[0],\n      width: handleInterval[1] - handleInterval[0]\n    }; // Move handle\n\n    if (displaybles.moveHandle) {\n      displaybles.moveHandle.setShape(viewExtent);\n      displaybles.moveZone.setShape(viewExtent); // Force update path on the invisible object\n\n      displaybles.moveZone.getBoundingRect();\n      displaybles.moveHandleIcon && displaybles.moveHandleIcon.attr('x', viewExtent.x + viewExtent.width / 2);\n    } // update clip path of shadow.\n\n    var dataShadowSegs = displaybles.dataShadowSegs;\n    var segIntervals = [0, handleInterval[0], handleInterval[1], size[0]];\n    for (var i = 0; i < dataShadowSegs.length; i++) {\n      var segGroup = dataShadowSegs[i];\n      var clipPath = segGroup.getClipPath();\n      if (!clipPath) {\n        clipPath = new graphic.Rect();\n        segGroup.setClipPath(clipPath);\n      }\n      clipPath.setShape({\n        x: segIntervals[i],\n        y: 0,\n        width: segIntervals[i + 1] - segIntervals[i],\n        height: size[1]\n      });\n    }\n    this._updateDataInfo(nonRealtime);\n  };\n  SliderZoomView.prototype._updateDataInfo = function (nonRealtime) {\n    var dataZoomModel = this.dataZoomModel;\n    var displaybles = this._displayables;\n    var handleLabels = displaybles.handleLabels;\n    var orient = this._orient;\n    var labelTexts = ['', '']; // FIXME\n    // date型，支持formatter，autoformatter（ec2 date.getAutoFormatter）\n\n    if (dataZoomModel.get('showDetail')) {\n      var axisProxy = dataZoomModel.findRepresentativeAxisProxy();\n      if (axisProxy) {\n        var axis = axisProxy.getAxisModel().axis;\n        var range = this._range;\n        var dataInterval = nonRealtime // See #4434, data and axis are not processed and reset yet in non-realtime mode.\n        ? axisProxy.calculateDataWindow({\n          start: range[0],\n          end: range[1]\n        }).valueWindow : axisProxy.getDataValueWindow();\n        labelTexts = [this._formatLabel(dataInterval[0], axis), this._formatLabel(dataInterval[1], axis)];\n      }\n    }\n    var orderedHandleEnds = asc(this._handleEnds.slice());\n    setLabel.call(this, 0);\n    setLabel.call(this, 1);\n    function setLabel(handleIndex) {\n      // Label\n      // Text should not transform by barGroup.\n      // Ignore handlers transform\n      var barTransform = graphic.getTransform(displaybles.handles[handleIndex].parent, this.group);\n      var direction = graphic.transformDirection(handleIndex === 0 ? 'right' : 'left', barTransform);\n      var offset = this._handleWidth / 2 + LABEL_GAP;\n      var textPoint = graphic.applyTransform([orderedHandleEnds[handleIndex] + (handleIndex === 0 ? -offset : offset), this._size[1] / 2], barTransform);\n      handleLabels[handleIndex].setStyle({\n        x: textPoint[0],\n        y: textPoint[1],\n        verticalAlign: orient === HORIZONTAL ? 'middle' : direction,\n        align: orient === HORIZONTAL ? direction : 'center',\n        text: labelTexts[handleIndex]\n      });\n    }\n  };\n  SliderZoomView.prototype._formatLabel = function (value, axis) {\n    var dataZoomModel = this.dataZoomModel;\n    var labelFormatter = dataZoomModel.get('labelFormatter');\n    var labelPrecision = dataZoomModel.get('labelPrecision');\n    if (labelPrecision == null || labelPrecision === 'auto') {\n      labelPrecision = axis.getPixelPrecision();\n    }\n    var valueStr = value == null || isNaN(value) ? '' // FIXME Glue code\n    : axis.type === 'category' || axis.type === 'time' ? axis.scale.getLabel({\n      value: Math.round(value)\n    }) // param of toFixed should less then 20.\n    : value.toFixed(Math.min(labelPrecision, 20));\n    return isFunction(labelFormatter) ? labelFormatter(value, valueStr) : isString(labelFormatter) ? labelFormatter.replace('{value}', valueStr) : valueStr;\n  };\n  /**\n   * @param showOrHide true: show, false: hide\n   */\n\n  SliderZoomView.prototype._showDataInfo = function (showOrHide) {\n    // Always show when drgging.\n    showOrHide = this._dragging || showOrHide;\n    var displayables = this._displayables;\n    var handleLabels = displayables.handleLabels;\n    handleLabels[0].attr('invisible', !showOrHide);\n    handleLabels[1].attr('invisible', !showOrHide); // Highlight move handle\n\n    displayables.moveHandle && this.api[showOrHide ? 'enterEmphasis' : 'leaveEmphasis'](displayables.moveHandle, 1);\n  };\n  SliderZoomView.prototype._onDragMove = function (handleIndex, dx, dy, event) {\n    this._dragging = true; // For mobile device, prevent screen slider on the button.\n\n    eventTool.stop(event.event); // Transform dx, dy to bar coordination.\n\n    var barTransform = this._displayables.sliderGroup.getLocalTransform();\n    var vertex = graphic.applyTransform([dx, dy], barTransform, true);\n    var changed = this._updateInterval(handleIndex, vertex[0]);\n    var realtime = this.dataZoomModel.get('realtime');\n    this._updateView(!realtime); // Avoid dispatch dataZoom repeatly but range not changed,\n    // which cause bad visual effect when progressive enabled.\n\n    changed && realtime && this._dispatchZoomAction(true);\n  };\n  SliderZoomView.prototype._onDragEnd = function () {\n    this._dragging = false;\n    this._showDataInfo(false); // While in realtime mode and stream mode, dispatch action when\n    // drag end will cause the whole view rerender, which is unnecessary.\n\n    var realtime = this.dataZoomModel.get('realtime');\n    !realtime && this._dispatchZoomAction(false);\n  };\n  SliderZoomView.prototype._onClickPanel = function (e) {\n    var size = this._size;\n    var localPoint = this._displayables.sliderGroup.transformCoordToLocal(e.offsetX, e.offsetY);\n    if (localPoint[0] < 0 || localPoint[0] > size[0] || localPoint[1] < 0 || localPoint[1] > size[1]) {\n      return;\n    }\n    var handleEnds = this._handleEnds;\n    var center = (handleEnds[0] + handleEnds[1]) / 2;\n    var changed = this._updateInterval('all', localPoint[0] - center);\n    this._updateView();\n    changed && this._dispatchZoomAction(false);\n  };\n  SliderZoomView.prototype._onBrushStart = function (e) {\n    var x = e.offsetX;\n    var y = e.offsetY;\n    this._brushStart = new graphic.Point(x, y);\n    this._brushing = true;\n    this._brushStartTime = +new Date(); // this._updateBrushRect(x, y);\n  };\n  SliderZoomView.prototype._onBrushEnd = function (e) {\n    if (!this._brushing) {\n      return;\n    }\n    var brushRect = this._displayables.brushRect;\n    this._brushing = false;\n    if (!brushRect) {\n      return;\n    }\n    brushRect.attr('ignore', true);\n    var brushShape = brushRect.shape;\n    var brushEndTime = +new Date(); // console.log(brushEndTime - this._brushStartTime);\n\n    if (brushEndTime - this._brushStartTime < 200 && Math.abs(brushShape.width) < 5) {\n      // Will treat it as a click\n      return;\n    }\n    var viewExtend = this._getViewExtent();\n    var percentExtent = [0, 100];\n    this._range = asc([linearMap(brushShape.x, viewExtend, percentExtent, true), linearMap(brushShape.x + brushShape.width, viewExtend, percentExtent, true)]);\n    this._handleEnds = [brushShape.x, brushShape.x + brushShape.width];\n    this._updateView();\n    this._dispatchZoomAction(false);\n  };\n  SliderZoomView.prototype._onBrush = function (e) {\n    if (this._brushing) {\n      // For mobile device, prevent screen slider on the button.\n      eventTool.stop(e.event);\n      this._updateBrushRect(e.offsetX, e.offsetY);\n    }\n  };\n  SliderZoomView.prototype._updateBrushRect = function (mouseX, mouseY) {\n    var displayables = this._displayables;\n    var dataZoomModel = this.dataZoomModel;\n    var brushRect = displayables.brushRect;\n    if (!brushRect) {\n      brushRect = displayables.brushRect = new Rect({\n        silent: true,\n        style: dataZoomModel.getModel('brushStyle').getItemStyle()\n      });\n      displayables.sliderGroup.add(brushRect);\n    }\n    brushRect.attr('ignore', false);\n    var brushStart = this._brushStart;\n    var sliderGroup = this._displayables.sliderGroup;\n    var endPoint = sliderGroup.transformCoordToLocal(mouseX, mouseY);\n    var startPoint = sliderGroup.transformCoordToLocal(brushStart.x, brushStart.y);\n    var size = this._size;\n    endPoint[0] = Math.max(Math.min(size[0], endPoint[0]), 0);\n    brushRect.setShape({\n      x: startPoint[0],\n      y: 0,\n      width: endPoint[0] - startPoint[0],\n      height: size[1]\n    });\n  };\n  /**\n   * This action will be throttled.\n   */\n\n  SliderZoomView.prototype._dispatchZoomAction = function (realtime) {\n    var range = this._range;\n    this.api.dispatchAction({\n      type: 'dataZoom',\n      from: this.uid,\n      dataZoomId: this.dataZoomModel.id,\n      animation: realtime ? REALTIME_ANIMATION_CONFIG : null,\n      start: range[0],\n      end: range[1]\n    });\n  };\n  SliderZoomView.prototype._findCoordRect = function () {\n    // Find the grid coresponding to the first axis referred by dataZoom.\n    var rect;\n    var coordSysInfoList = collectReferCoordSysModelInfo(this.dataZoomModel).infoList;\n    if (!rect && coordSysInfoList.length) {\n      var coordSys = coordSysInfoList[0].model.coordinateSystem;\n      rect = coordSys.getRect && coordSys.getRect();\n    }\n    if (!rect) {\n      var width = this.api.getWidth();\n      var height = this.api.getHeight();\n      rect = {\n        x: width * 0.2,\n        y: height * 0.2,\n        width: width * 0.6,\n        height: height * 0.6\n      };\n    }\n    return rect;\n  };\n  SliderZoomView.type = 'dataZoom.slider';\n  return SliderZoomView;\n}(DataZoomView);\nfunction getOtherDim(thisDim) {\n  // FIXME\n  // 这个逻辑和getOtherAxis里一致，但是写在这里是否不好\n  var map = {\n    x: 'y',\n    y: 'x',\n    radius: 'angle',\n    angle: 'radius'\n  };\n  return map[thisDim];\n}\nfunction getCursor(orient) {\n  return orient === 'vertical' ? 'ns-resize' : 'ew-resize';\n}\nexport default SliderZoomView;", "map": {"version": 3, "names": ["__extends", "bind", "each", "isFunction", "isString", "indexOf", "eventTool", "graphic", "throttle", "DataZoomView", "linearMap", "asc", "parsePercent", "layout", "slider<PERSON><PERSON>", "getAxisMainType", "collectReferCoordSysModelInfo", "enableHoverEmphasis", "createSymbol", "symbolBuildProxies", "deprecateLog", "createTextStyle", "Rect", "DEFAULT_LOCATION_EDGE_GAP", "DEFAULT_FRAME_BORDER_WIDTH", "DEFAULT_FILLER_SIZE", "DEFAULT_MOVE_HANDLE_SIZE", "HORIZONTAL", "VERTICAL", "LABEL_GAP", "SHOW_DATA_SHADOW_SERIES_TYPE", "REALTIME_ANIMATION_CONFIG", "easing", "duration", "delay", "SliderZoomView", "_super", "_this", "apply", "arguments", "type", "_displayables", "prototype", "init", "ecModel", "api", "_onBrush", "_onBrushEnd", "render", "dataZoomModel", "payload", "createOrUpdate", "get", "_orient", "getOrient", "group", "removeAll", "no<PERSON><PERSON><PERSON>", "_clear", "from", "uid", "_buildView", "_updateView", "dispose", "clear", "zr", "getZr", "off", "thisGroup", "_brushing", "brushRect", "_resetLocation", "_resetInterval", "barGroup", "sliderGroup", "Group", "_renderBackground", "_renderHandle", "_renderDataShadow", "add", "_positionGroup", "showMoveHandle", "moveHandleSize", "coordRect", "_findCoordRect", "ecSize", "width", "getWidth", "height", "getHeight", "positionInfo", "right", "x", "top", "y", "layoutParams", "getLayoutParams", "option", "name", "layoutRect", "getLayoutRect", "_location", "_size", "reverse", "location", "orient", "targetAxisModel", "getFirstTargetAxisModel", "inverse", "otherAxisInverse", "_dataShadowInfo", "attr", "scaleY", "scaleX", "rotation", "Math", "PI", "rect", "getBoundingRect", "mark<PERSON><PERSON><PERSON>", "_getViewExtent", "size", "brushSelect", "silent", "shape", "style", "fill", "z2", "clickPanel", "onclick", "_onClickPanel", "on", "_onBrushStart", "cursor", "info", "_prepareDataShadowInfo", "dataShadowSegs", "oldSize", "_shadowSize", "seriesModel", "series", "data", "getRawData", "otherDim", "getS<PERSON>owDim", "polygonPts", "_shadowPolygonPts", "polylinePts", "_shadowPolylinePts", "_shadowData", "_shadowDim", "otherDataExtent_1", "getDataExtent", "otherOffset", "otherShadowExtent_1", "thisShadowExtent", "areaPoints_1", "linePoints_1", "step_1", "count", "thisCoord_1", "stride_1", "round", "lastIsEmpty_1", "value", "index", "isEmpty", "isNaN", "otherCoord", "push", "length", "createDataShadowGroup", "isSelectedArea", "model", "getModel", "polygon", "Polygon", "points", "segmentIgnoreThreshold", "getAreaStyle", "polyline", "Polyline", "getLineStyle", "i", "showDataShadow", "result", "eachTargetAxis", "axisDim", "axisIndex", "seriesModels", "getAxisProxy", "getTargetSeriesModels", "thisAxis", "getComponent", "axis", "get<PERSON><PERSON><PERSON><PERSON>", "coordSys", "coordinateSystem", "getOtherAxis", "getData", "mapDimension", "thisDim", "displayables", "handles", "handleLabels", "borderRadius", "filler", "textConfig", "position", "subPixelOptimize", "r", "stroke", "lineWidth", "handleIndex", "iconStr", "process", "env", "NODE_ENV", "path", "getCursor", "draggable", "drift", "_onDragMove", "ondragend", "_onDragEnd", "on<PERSON><PERSON>ver", "_showDataInfo", "onmouseout", "bRect", "handleSize", "_handleHeight", "_handleWidth", "setStyle", "getItemStyle", "strokeNoScale", "rectHover", "ensureState", "handleColor", "textStyleModel", "Text", "invisible", "text", "verticalAlign", "align", "getTextColor", "font", "getFont", "actualMoveZone", "moveHandleHeight", "moveHandle_1", "moveHandle", "iconSize", "moveHandleIcon", "moveZoneExpandSize", "min", "max", "moveZone", "enterEmphasis", "leaveEmphasis", "ondragstart", "range", "_range", "getPercentRange", "viewExtent", "_handleEnds", "_updateInterval", "delta", "handleEnds", "viewExtend", "minMaxSpan", "findRepresentativeAxisProxy", "getMinMaxSpan", "percentExtent", "minSpan", "maxSpan", "<PERSON><PERSON><PERSON><PERSON>", "nonRealtime", "displaybles", "handleInterval", "slice", "handle", "handleHeight", "setShape", "segIntervals", "segGroup", "clipPath", "getClipPath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_updateDataInfo", "labelTexts", "axisProxy", "getAxisModel", "dataInterval", "calculateDataWindow", "start", "end", "valueWindow", "getDataValueWindow", "_formatLabel", "orderedHandleEnds", "<PERSON><PERSON><PERSON><PERSON>", "call", "barTransform", "getTransform", "parent", "direction", "transformDirection", "offset", "textPoint", "applyTransform", "labelFormatter", "labelPrecision", "getPixelPrecision", "valueStr", "scale", "get<PERSON><PERSON><PERSON>", "toFixed", "replace", "showOrHide", "_dragging", "dx", "dy", "event", "stop", "getLocalTransform", "vertex", "changed", "realtime", "_dispatchZoomAction", "e", "localPoint", "transformCoordToLocal", "offsetX", "offsetY", "center", "_brushStart", "Point", "_brushStartTime", "Date", "brushShape", "brushEndTime", "abs", "_updateBrushRect", "mouseX", "mouseY", "brushStart", "endPoint", "startPoint", "dispatchAction", "dataZoomId", "id", "animation", "coordSysInfoList", "infoList", "getRect", "map", "radius", "angle"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/echarts/lib/component/dataZoom/SliderZoomView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nimport { __extends } from \"tslib\";\nimport { bind, each, isFunction, isString, indexOf } from 'zrender/lib/core/util.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as throttle from '../../util/throttle.js';\nimport DataZoomView from './DataZoomView.js';\nimport { linearMap, asc, parsePercent } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport { getAxisMainType, collectReferCoordSysModelInfo } from './helper.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { createSymbol, symbolBuildProxies } from '../../util/symbol.js';\nimport { deprecateLog } from '../../util/log.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nvar Rect = graphic.Rect; // Constants\n\nvar DEFAULT_LOCATION_EDGE_GAP = 7;\nvar DEFAULT_FRAME_BORDER_WIDTH = 1;\nvar DEFAULT_FILLER_SIZE = 30;\nvar DEFAULT_MOVE_HANDLE_SIZE = 7;\nvar HORIZONTAL = 'horizontal';\nvar VERTICAL = 'vertical';\nvar LABEL_GAP = 5;\nvar SHOW_DATA_SHADOW_SERIES_TYPE = ['line', 'bar', 'candlestick', 'scatter'];\nvar REALTIME_ANIMATION_CONFIG = {\n  easing: 'cubicOut',\n  duration: 100,\n  delay: 0\n};\n\nvar SliderZoomView =\n/** @class */\nfunction (_super) {\n  __extends(SliderZoomView, _super);\n\n  function SliderZoomView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n\n    _this.type = SliderZoomView.type;\n    _this._displayables = {};\n    return _this;\n  }\n\n  SliderZoomView.prototype.init = function (ecModel, api) {\n    this.api = api; // A unique handler for each dataZoom component\n\n    this._onBrush = bind(this._onBrush, this);\n    this._onBrushEnd = bind(this._onBrushEnd, this);\n  };\n\n  SliderZoomView.prototype.render = function (dataZoomModel, ecModel, api, payload) {\n    _super.prototype.render.apply(this, arguments);\n\n    throttle.createOrUpdate(this, '_dispatchZoomAction', dataZoomModel.get('throttle'), 'fixRate');\n    this._orient = dataZoomModel.getOrient();\n\n    if (dataZoomModel.get('show') === false) {\n      this.group.removeAll();\n      return;\n    }\n\n    if (dataZoomModel.noTarget()) {\n      this._clear();\n\n      this.group.removeAll();\n      return;\n    } // Notice: this._resetInterval() should not be executed when payload.type\n    // is 'dataZoom', origin this._range should be maintained, otherwise 'pan'\n    // or 'zoom' info will be missed because of 'throttle' of this.dispatchAction,\n\n\n    if (!payload || payload.type !== 'dataZoom' || payload.from !== this.uid) {\n      this._buildView();\n    }\n\n    this._updateView();\n  };\n\n  SliderZoomView.prototype.dispose = function () {\n    this._clear();\n\n    _super.prototype.dispose.apply(this, arguments);\n  };\n\n  SliderZoomView.prototype._clear = function () {\n    throttle.clear(this, '_dispatchZoomAction');\n    var zr = this.api.getZr();\n    zr.off('mousemove', this._onBrush);\n    zr.off('mouseup', this._onBrushEnd);\n  };\n\n  SliderZoomView.prototype._buildView = function () {\n    var thisGroup = this.group;\n    thisGroup.removeAll();\n    this._brushing = false;\n    this._displayables.brushRect = null;\n\n    this._resetLocation();\n\n    this._resetInterval();\n\n    var barGroup = this._displayables.sliderGroup = new graphic.Group();\n\n    this._renderBackground();\n\n    this._renderHandle();\n\n    this._renderDataShadow();\n\n    thisGroup.add(barGroup);\n\n    this._positionGroup();\n  };\n\n  SliderZoomView.prototype._resetLocation = function () {\n    var dataZoomModel = this.dataZoomModel;\n    var api = this.api;\n    var showMoveHandle = dataZoomModel.get('brushSelect');\n    var moveHandleSize = showMoveHandle ? DEFAULT_MOVE_HANDLE_SIZE : 0; // If some of x/y/width/height are not specified,\n    // auto-adapt according to target grid.\n\n    var coordRect = this._findCoordRect();\n\n    var ecSize = {\n      width: api.getWidth(),\n      height: api.getHeight()\n    }; // Default align by coordinate system rect.\n\n    var positionInfo = this._orient === HORIZONTAL ? {\n      // Why using 'right', because right should be used in vertical,\n      // and it is better to be consistent for dealing with position param merge.\n      right: ecSize.width - coordRect.x - coordRect.width,\n      top: ecSize.height - DEFAULT_FILLER_SIZE - DEFAULT_LOCATION_EDGE_GAP - moveHandleSize,\n      width: coordRect.width,\n      height: DEFAULT_FILLER_SIZE\n    } : {\n      right: DEFAULT_LOCATION_EDGE_GAP,\n      top: coordRect.y,\n      width: DEFAULT_FILLER_SIZE,\n      height: coordRect.height\n    }; // Do not write back to option and replace value 'ph', because\n    // the 'ph' value should be recalculated when resize.\n\n    var layoutParams = layout.getLayoutParams(dataZoomModel.option); // Replace the placeholder value.\n\n    each(['right', 'top', 'width', 'height'], function (name) {\n      if (layoutParams[name] === 'ph') {\n        layoutParams[name] = positionInfo[name];\n      }\n    });\n    var layoutRect = layout.getLayoutRect(layoutParams, ecSize);\n    this._location = {\n      x: layoutRect.x,\n      y: layoutRect.y\n    };\n    this._size = [layoutRect.width, layoutRect.height];\n    this._orient === VERTICAL && this._size.reverse();\n  };\n\n  SliderZoomView.prototype._positionGroup = function () {\n    var thisGroup = this.group;\n    var location = this._location;\n    var orient = this._orient; // Just use the first axis to determine mapping.\n\n    var targetAxisModel = this.dataZoomModel.getFirstTargetAxisModel();\n    var inverse = targetAxisModel && targetAxisModel.get('inverse');\n    var sliderGroup = this._displayables.sliderGroup;\n    var otherAxisInverse = (this._dataShadowInfo || {}).otherAxisInverse; // Transform barGroup.\n\n    sliderGroup.attr(orient === HORIZONTAL && !inverse ? {\n      scaleY: otherAxisInverse ? 1 : -1,\n      scaleX: 1\n    } : orient === HORIZONTAL && inverse ? {\n      scaleY: otherAxisInverse ? 1 : -1,\n      scaleX: -1\n    } : orient === VERTICAL && !inverse ? {\n      scaleY: otherAxisInverse ? -1 : 1,\n      scaleX: 1,\n      rotation: Math.PI / 2\n    } // Dont use Math.PI, considering shadow direction.\n    : {\n      scaleY: otherAxisInverse ? -1 : 1,\n      scaleX: -1,\n      rotation: Math.PI / 2\n    }); // Position barGroup\n\n    var rect = thisGroup.getBoundingRect([sliderGroup]);\n    thisGroup.x = location.x - rect.x;\n    thisGroup.y = location.y - rect.y;\n    thisGroup.markRedraw();\n  };\n\n  SliderZoomView.prototype._getViewExtent = function () {\n    return [0, this._size[0]];\n  };\n\n  SliderZoomView.prototype._renderBackground = function () {\n    var dataZoomModel = this.dataZoomModel;\n    var size = this._size;\n    var barGroup = this._displayables.sliderGroup;\n    var brushSelect = dataZoomModel.get('brushSelect');\n    barGroup.add(new Rect({\n      silent: true,\n      shape: {\n        x: 0,\n        y: 0,\n        width: size[0],\n        height: size[1]\n      },\n      style: {\n        fill: dataZoomModel.get('backgroundColor')\n      },\n      z2: -40\n    })); // Click panel, over shadow, below handles.\n\n    var clickPanel = new Rect({\n      shape: {\n        x: 0,\n        y: 0,\n        width: size[0],\n        height: size[1]\n      },\n      style: {\n        fill: 'transparent'\n      },\n      z2: 0,\n      onclick: bind(this._onClickPanel, this)\n    });\n    var zr = this.api.getZr();\n\n    if (brushSelect) {\n      clickPanel.on('mousedown', this._onBrushStart, this);\n      clickPanel.cursor = 'crosshair';\n      zr.on('mousemove', this._onBrush);\n      zr.on('mouseup', this._onBrushEnd);\n    } else {\n      zr.off('mousemove', this._onBrush);\n      zr.off('mouseup', this._onBrushEnd);\n    }\n\n    barGroup.add(clickPanel);\n  };\n\n  SliderZoomView.prototype._renderDataShadow = function () {\n    var info = this._dataShadowInfo = this._prepareDataShadowInfo();\n\n    this._displayables.dataShadowSegs = [];\n\n    if (!info) {\n      return;\n    }\n\n    var size = this._size;\n    var oldSize = this._shadowSize || [];\n    var seriesModel = info.series;\n    var data = seriesModel.getRawData();\n    var otherDim = seriesModel.getShadowDim ? seriesModel.getShadowDim() // @see candlestick\n    : info.otherDim;\n\n    if (otherDim == null) {\n      return;\n    }\n\n    var polygonPts = this._shadowPolygonPts;\n    var polylinePts = this._shadowPolylinePts; // Not re-render if data doesn't change.\n\n    if (data !== this._shadowData || otherDim !== this._shadowDim || size[0] !== oldSize[0] || size[1] !== oldSize[1]) {\n      var otherDataExtent_1 = data.getDataExtent(otherDim); // Nice extent.\n\n      var otherOffset = (otherDataExtent_1[1] - otherDataExtent_1[0]) * 0.3;\n      otherDataExtent_1 = [otherDataExtent_1[0] - otherOffset, otherDataExtent_1[1] + otherOffset];\n      var otherShadowExtent_1 = [0, size[1]];\n      var thisShadowExtent = [0, size[0]];\n      var areaPoints_1 = [[size[0], 0], [0, 0]];\n      var linePoints_1 = [];\n      var step_1 = thisShadowExtent[1] / (data.count() - 1);\n      var thisCoord_1 = 0; // Optimize for large data shadow\n\n      var stride_1 = Math.round(data.count() / size[0]);\n      var lastIsEmpty_1;\n      data.each([otherDim], function (value, index) {\n        if (stride_1 > 0 && index % stride_1) {\n          thisCoord_1 += step_1;\n          return;\n        } // FIXME\n        // Should consider axis.min/axis.max when drawing dataShadow.\n        // FIXME\n        // 应该使用统一的空判断？还是在list里进行空判断？\n\n\n        var isEmpty = value == null || isNaN(value) || value === ''; // See #4235.\n\n        var otherCoord = isEmpty ? 0 : linearMap(value, otherDataExtent_1, otherShadowExtent_1, true); // Attempt to draw data shadow precisely when there are empty value.\n\n        if (isEmpty && !lastIsEmpty_1 && index) {\n          areaPoints_1.push([areaPoints_1[areaPoints_1.length - 1][0], 0]);\n          linePoints_1.push([linePoints_1[linePoints_1.length - 1][0], 0]);\n        } else if (!isEmpty && lastIsEmpty_1) {\n          areaPoints_1.push([thisCoord_1, 0]);\n          linePoints_1.push([thisCoord_1, 0]);\n        }\n\n        areaPoints_1.push([thisCoord_1, otherCoord]);\n        linePoints_1.push([thisCoord_1, otherCoord]);\n        thisCoord_1 += step_1;\n        lastIsEmpty_1 = isEmpty;\n      });\n      polygonPts = this._shadowPolygonPts = areaPoints_1;\n      polylinePts = this._shadowPolylinePts = linePoints_1;\n    }\n\n    this._shadowData = data;\n    this._shadowDim = otherDim;\n    this._shadowSize = [size[0], size[1]];\n    var dataZoomModel = this.dataZoomModel;\n\n    function createDataShadowGroup(isSelectedArea) {\n      var model = dataZoomModel.getModel(isSelectedArea ? 'selectedDataBackground' : 'dataBackground');\n      var group = new graphic.Group();\n      var polygon = new graphic.Polygon({\n        shape: {\n          points: polygonPts\n        },\n        segmentIgnoreThreshold: 1,\n        style: model.getModel('areaStyle').getAreaStyle(),\n        silent: true,\n        z2: -20\n      });\n      var polyline = new graphic.Polyline({\n        shape: {\n          points: polylinePts\n        },\n        segmentIgnoreThreshold: 1,\n        style: model.getModel('lineStyle').getLineStyle(),\n        silent: true,\n        z2: -19\n      });\n      group.add(polygon);\n      group.add(polyline);\n      return group;\n    } // let dataBackgroundModel = dataZoomModel.getModel('dataBackground');\n\n\n    for (var i = 0; i < 3; i++) {\n      var group = createDataShadowGroup(i === 1);\n\n      this._displayables.sliderGroup.add(group);\n\n      this._displayables.dataShadowSegs.push(group);\n    }\n  };\n\n  SliderZoomView.prototype._prepareDataShadowInfo = function () {\n    var dataZoomModel = this.dataZoomModel;\n    var showDataShadow = dataZoomModel.get('showDataShadow');\n\n    if (showDataShadow === false) {\n      return;\n    } // Find a representative series.\n\n\n    var result;\n    var ecModel = this.ecModel;\n    dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n      var seriesModels = dataZoomModel.getAxisProxy(axisDim, axisIndex).getTargetSeriesModels();\n      each(seriesModels, function (seriesModel) {\n        if (result) {\n          return;\n        }\n\n        if (showDataShadow !== true && indexOf(SHOW_DATA_SHADOW_SERIES_TYPE, seriesModel.get('type')) < 0) {\n          return;\n        }\n\n        var thisAxis = ecModel.getComponent(getAxisMainType(axisDim), axisIndex).axis;\n        var otherDim = getOtherDim(axisDim);\n        var otherAxisInverse;\n        var coordSys = seriesModel.coordinateSystem;\n\n        if (otherDim != null && coordSys.getOtherAxis) {\n          otherAxisInverse = coordSys.getOtherAxis(thisAxis).inverse;\n        }\n\n        otherDim = seriesModel.getData().mapDimension(otherDim);\n        result = {\n          thisAxis: thisAxis,\n          series: seriesModel,\n          thisDim: axisDim,\n          otherDim: otherDim,\n          otherAxisInverse: otherAxisInverse\n        };\n      }, this);\n    }, this);\n    return result;\n  };\n\n  SliderZoomView.prototype._renderHandle = function () {\n    var thisGroup = this.group;\n    var displayables = this._displayables;\n    var handles = displayables.handles = [null, null];\n    var handleLabels = displayables.handleLabels = [null, null];\n    var sliderGroup = this._displayables.sliderGroup;\n    var size = this._size;\n    var dataZoomModel = this.dataZoomModel;\n    var api = this.api;\n    var borderRadius = dataZoomModel.get('borderRadius') || 0;\n    var brushSelect = dataZoomModel.get('brushSelect');\n    var filler = displayables.filler = new Rect({\n      silent: brushSelect,\n      style: {\n        fill: dataZoomModel.get('fillerColor')\n      },\n      textConfig: {\n        position: 'inside'\n      }\n    });\n    sliderGroup.add(filler); // Frame border.\n\n    sliderGroup.add(new Rect({\n      silent: true,\n      subPixelOptimize: true,\n      shape: {\n        x: 0,\n        y: 0,\n        width: size[0],\n        height: size[1],\n        r: borderRadius\n      },\n      style: {\n        // deprecated option\n        stroke: dataZoomModel.get('dataBackgroundColor') || dataZoomModel.get('borderColor'),\n        lineWidth: DEFAULT_FRAME_BORDER_WIDTH,\n        fill: 'rgba(0,0,0,0)'\n      }\n    })); // Left and right handle to resize\n\n    each([0, 1], function (handleIndex) {\n      var iconStr = dataZoomModel.get('handleIcon');\n\n      if (!symbolBuildProxies[iconStr] && iconStr.indexOf('path://') < 0 && iconStr.indexOf('image://') < 0) {\n        // Compatitable with the old icon parsers. Which can use a path string without path://\n        iconStr = 'path://' + iconStr;\n\n        if (process.env.NODE_ENV !== 'production') {\n          deprecateLog('handleIcon now needs \\'path://\\' prefix when using a path string');\n        }\n      }\n\n      var path = createSymbol(iconStr, -1, 0, 2, 2, null, true);\n      path.attr({\n        cursor: getCursor(this._orient),\n        draggable: true,\n        drift: bind(this._onDragMove, this, handleIndex),\n        ondragend: bind(this._onDragEnd, this),\n        onmouseover: bind(this._showDataInfo, this, true),\n        onmouseout: bind(this._showDataInfo, this, false),\n        z2: 5\n      });\n      var bRect = path.getBoundingRect();\n      var handleSize = dataZoomModel.get('handleSize');\n      this._handleHeight = parsePercent(handleSize, this._size[1]);\n      this._handleWidth = bRect.width / bRect.height * this._handleHeight;\n      path.setStyle(dataZoomModel.getModel('handleStyle').getItemStyle());\n      path.style.strokeNoScale = true;\n      path.rectHover = true;\n      path.ensureState('emphasis').style = dataZoomModel.getModel(['emphasis', 'handleStyle']).getItemStyle();\n      enableHoverEmphasis(path);\n      var handleColor = dataZoomModel.get('handleColor'); // deprecated option\n      // Compatitable with previous version\n\n      if (handleColor != null) {\n        path.style.fill = handleColor;\n      }\n\n      sliderGroup.add(handles[handleIndex] = path);\n      var textStyleModel = dataZoomModel.getModel('textStyle');\n      thisGroup.add(handleLabels[handleIndex] = new graphic.Text({\n        silent: true,\n        invisible: true,\n        style: createTextStyle(textStyleModel, {\n          x: 0,\n          y: 0,\n          text: '',\n          verticalAlign: 'middle',\n          align: 'center',\n          fill: textStyleModel.getTextColor(),\n          font: textStyleModel.getFont()\n        }),\n        z2: 10\n      }));\n    }, this); // Handle to move. Only visible when brushSelect is set true.\n\n    var actualMoveZone = filler;\n\n    if (brushSelect) {\n      var moveHandleHeight = parsePercent(dataZoomModel.get('moveHandleSize'), size[1]);\n      var moveHandle_1 = displayables.moveHandle = new graphic.Rect({\n        style: dataZoomModel.getModel('moveHandleStyle').getItemStyle(),\n        silent: true,\n        shape: {\n          r: [0, 0, 2, 2],\n          y: size[1] - 0.5,\n          height: moveHandleHeight\n        }\n      });\n      var iconSize = moveHandleHeight * 0.8;\n      var moveHandleIcon = displayables.moveHandleIcon = createSymbol(dataZoomModel.get('moveHandleIcon'), -iconSize / 2, -iconSize / 2, iconSize, iconSize, '#fff', true);\n      moveHandleIcon.silent = true;\n      moveHandleIcon.y = size[1] + moveHandleHeight / 2 - 0.5;\n      moveHandle_1.ensureState('emphasis').style = dataZoomModel.getModel(['emphasis', 'moveHandleStyle']).getItemStyle();\n      var moveZoneExpandSize = Math.min(size[1] / 2, Math.max(moveHandleHeight, 10));\n      actualMoveZone = displayables.moveZone = new graphic.Rect({\n        invisible: true,\n        shape: {\n          y: size[1] - moveZoneExpandSize,\n          height: moveHandleHeight + moveZoneExpandSize\n        }\n      });\n      actualMoveZone.on('mouseover', function () {\n        api.enterEmphasis(moveHandle_1);\n      }).on('mouseout', function () {\n        api.leaveEmphasis(moveHandle_1);\n      });\n      sliderGroup.add(moveHandle_1);\n      sliderGroup.add(moveHandleIcon);\n      sliderGroup.add(actualMoveZone);\n    }\n\n    actualMoveZone.attr({\n      draggable: true,\n      cursor: getCursor(this._orient),\n      drift: bind(this._onDragMove, this, 'all'),\n      ondragstart: bind(this._showDataInfo, this, true),\n      ondragend: bind(this._onDragEnd, this),\n      onmouseover: bind(this._showDataInfo, this, true),\n      onmouseout: bind(this._showDataInfo, this, false)\n    });\n  };\n\n  SliderZoomView.prototype._resetInterval = function () {\n    var range = this._range = this.dataZoomModel.getPercentRange();\n\n    var viewExtent = this._getViewExtent();\n\n    this._handleEnds = [linearMap(range[0], [0, 100], viewExtent, true), linearMap(range[1], [0, 100], viewExtent, true)];\n  };\n\n  SliderZoomView.prototype._updateInterval = function (handleIndex, delta) {\n    var dataZoomModel = this.dataZoomModel;\n    var handleEnds = this._handleEnds;\n\n    var viewExtend = this._getViewExtent();\n\n    var minMaxSpan = dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();\n    var percentExtent = [0, 100];\n    sliderMove(delta, handleEnds, viewExtend, dataZoomModel.get('zoomLock') ? 'all' : handleIndex, minMaxSpan.minSpan != null ? linearMap(minMaxSpan.minSpan, percentExtent, viewExtend, true) : null, minMaxSpan.maxSpan != null ? linearMap(minMaxSpan.maxSpan, percentExtent, viewExtend, true) : null);\n    var lastRange = this._range;\n    var range = this._range = asc([linearMap(handleEnds[0], viewExtend, percentExtent, true), linearMap(handleEnds[1], viewExtend, percentExtent, true)]);\n    return !lastRange || lastRange[0] !== range[0] || lastRange[1] !== range[1];\n  };\n\n  SliderZoomView.prototype._updateView = function (nonRealtime) {\n    var displaybles = this._displayables;\n    var handleEnds = this._handleEnds;\n    var handleInterval = asc(handleEnds.slice());\n    var size = this._size;\n    each([0, 1], function (handleIndex) {\n      // Handles\n      var handle = displaybles.handles[handleIndex];\n      var handleHeight = this._handleHeight;\n      handle.attr({\n        scaleX: handleHeight / 2,\n        scaleY: handleHeight / 2,\n        // This is a trick, by adding an extra tiny offset to let the default handle's end point align to the drag window.\n        // NOTE: It may affect some custom shapes a bit. But we prefer to have better result by default.\n        x: handleEnds[handleIndex] + (handleIndex ? -1 : 1),\n        y: size[1] / 2 - handleHeight / 2\n      });\n    }, this); // Filler\n\n    displaybles.filler.setShape({\n      x: handleInterval[0],\n      y: 0,\n      width: handleInterval[1] - handleInterval[0],\n      height: size[1]\n    });\n    var viewExtent = {\n      x: handleInterval[0],\n      width: handleInterval[1] - handleInterval[0]\n    }; // Move handle\n\n    if (displaybles.moveHandle) {\n      displaybles.moveHandle.setShape(viewExtent);\n      displaybles.moveZone.setShape(viewExtent); // Force update path on the invisible object\n\n      displaybles.moveZone.getBoundingRect();\n      displaybles.moveHandleIcon && displaybles.moveHandleIcon.attr('x', viewExtent.x + viewExtent.width / 2);\n    } // update clip path of shadow.\n\n\n    var dataShadowSegs = displaybles.dataShadowSegs;\n    var segIntervals = [0, handleInterval[0], handleInterval[1], size[0]];\n\n    for (var i = 0; i < dataShadowSegs.length; i++) {\n      var segGroup = dataShadowSegs[i];\n      var clipPath = segGroup.getClipPath();\n\n      if (!clipPath) {\n        clipPath = new graphic.Rect();\n        segGroup.setClipPath(clipPath);\n      }\n\n      clipPath.setShape({\n        x: segIntervals[i],\n        y: 0,\n        width: segIntervals[i + 1] - segIntervals[i],\n        height: size[1]\n      });\n    }\n\n    this._updateDataInfo(nonRealtime);\n  };\n\n  SliderZoomView.prototype._updateDataInfo = function (nonRealtime) {\n    var dataZoomModel = this.dataZoomModel;\n    var displaybles = this._displayables;\n    var handleLabels = displaybles.handleLabels;\n    var orient = this._orient;\n    var labelTexts = ['', '']; // FIXME\n    // date型，支持formatter，autoformatter（ec2 date.getAutoFormatter）\n\n    if (dataZoomModel.get('showDetail')) {\n      var axisProxy = dataZoomModel.findRepresentativeAxisProxy();\n\n      if (axisProxy) {\n        var axis = axisProxy.getAxisModel().axis;\n        var range = this._range;\n        var dataInterval = nonRealtime // See #4434, data and axis are not processed and reset yet in non-realtime mode.\n        ? axisProxy.calculateDataWindow({\n          start: range[0],\n          end: range[1]\n        }).valueWindow : axisProxy.getDataValueWindow();\n        labelTexts = [this._formatLabel(dataInterval[0], axis), this._formatLabel(dataInterval[1], axis)];\n      }\n    }\n\n    var orderedHandleEnds = asc(this._handleEnds.slice());\n    setLabel.call(this, 0);\n    setLabel.call(this, 1);\n\n    function setLabel(handleIndex) {\n      // Label\n      // Text should not transform by barGroup.\n      // Ignore handlers transform\n      var barTransform = graphic.getTransform(displaybles.handles[handleIndex].parent, this.group);\n      var direction = graphic.transformDirection(handleIndex === 0 ? 'right' : 'left', barTransform);\n      var offset = this._handleWidth / 2 + LABEL_GAP;\n      var textPoint = graphic.applyTransform([orderedHandleEnds[handleIndex] + (handleIndex === 0 ? -offset : offset), this._size[1] / 2], barTransform);\n      handleLabels[handleIndex].setStyle({\n        x: textPoint[0],\n        y: textPoint[1],\n        verticalAlign: orient === HORIZONTAL ? 'middle' : direction,\n        align: orient === HORIZONTAL ? direction : 'center',\n        text: labelTexts[handleIndex]\n      });\n    }\n  };\n\n  SliderZoomView.prototype._formatLabel = function (value, axis) {\n    var dataZoomModel = this.dataZoomModel;\n    var labelFormatter = dataZoomModel.get('labelFormatter');\n    var labelPrecision = dataZoomModel.get('labelPrecision');\n\n    if (labelPrecision == null || labelPrecision === 'auto') {\n      labelPrecision = axis.getPixelPrecision();\n    }\n\n    var valueStr = value == null || isNaN(value) ? '' // FIXME Glue code\n    : axis.type === 'category' || axis.type === 'time' ? axis.scale.getLabel({\n      value: Math.round(value)\n    }) // param of toFixed should less then 20.\n    : value.toFixed(Math.min(labelPrecision, 20));\n    return isFunction(labelFormatter) ? labelFormatter(value, valueStr) : isString(labelFormatter) ? labelFormatter.replace('{value}', valueStr) : valueStr;\n  };\n  /**\n   * @param showOrHide true: show, false: hide\n   */\n\n\n  SliderZoomView.prototype._showDataInfo = function (showOrHide) {\n    // Always show when drgging.\n    showOrHide = this._dragging || showOrHide;\n    var displayables = this._displayables;\n    var handleLabels = displayables.handleLabels;\n    handleLabels[0].attr('invisible', !showOrHide);\n    handleLabels[1].attr('invisible', !showOrHide); // Highlight move handle\n\n    displayables.moveHandle && this.api[showOrHide ? 'enterEmphasis' : 'leaveEmphasis'](displayables.moveHandle, 1);\n  };\n\n  SliderZoomView.prototype._onDragMove = function (handleIndex, dx, dy, event) {\n    this._dragging = true; // For mobile device, prevent screen slider on the button.\n\n    eventTool.stop(event.event); // Transform dx, dy to bar coordination.\n\n    var barTransform = this._displayables.sliderGroup.getLocalTransform();\n\n    var vertex = graphic.applyTransform([dx, dy], barTransform, true);\n\n    var changed = this._updateInterval(handleIndex, vertex[0]);\n\n    var realtime = this.dataZoomModel.get('realtime');\n\n    this._updateView(!realtime); // Avoid dispatch dataZoom repeatly but range not changed,\n    // which cause bad visual effect when progressive enabled.\n\n\n    changed && realtime && this._dispatchZoomAction(true);\n  };\n\n  SliderZoomView.prototype._onDragEnd = function () {\n    this._dragging = false;\n\n    this._showDataInfo(false); // While in realtime mode and stream mode, dispatch action when\n    // drag end will cause the whole view rerender, which is unnecessary.\n\n\n    var realtime = this.dataZoomModel.get('realtime');\n    !realtime && this._dispatchZoomAction(false);\n  };\n\n  SliderZoomView.prototype._onClickPanel = function (e) {\n    var size = this._size;\n\n    var localPoint = this._displayables.sliderGroup.transformCoordToLocal(e.offsetX, e.offsetY);\n\n    if (localPoint[0] < 0 || localPoint[0] > size[0] || localPoint[1] < 0 || localPoint[1] > size[1]) {\n      return;\n    }\n\n    var handleEnds = this._handleEnds;\n    var center = (handleEnds[0] + handleEnds[1]) / 2;\n\n    var changed = this._updateInterval('all', localPoint[0] - center);\n\n    this._updateView();\n\n    changed && this._dispatchZoomAction(false);\n  };\n\n  SliderZoomView.prototype._onBrushStart = function (e) {\n    var x = e.offsetX;\n    var y = e.offsetY;\n    this._brushStart = new graphic.Point(x, y);\n    this._brushing = true;\n    this._brushStartTime = +new Date(); // this._updateBrushRect(x, y);\n  };\n\n  SliderZoomView.prototype._onBrushEnd = function (e) {\n    if (!this._brushing) {\n      return;\n    }\n\n    var brushRect = this._displayables.brushRect;\n    this._brushing = false;\n\n    if (!brushRect) {\n      return;\n    }\n\n    brushRect.attr('ignore', true);\n    var brushShape = brushRect.shape;\n    var brushEndTime = +new Date(); // console.log(brushEndTime - this._brushStartTime);\n\n    if (brushEndTime - this._brushStartTime < 200 && Math.abs(brushShape.width) < 5) {\n      // Will treat it as a click\n      return;\n    }\n\n    var viewExtend = this._getViewExtent();\n\n    var percentExtent = [0, 100];\n    this._range = asc([linearMap(brushShape.x, viewExtend, percentExtent, true), linearMap(brushShape.x + brushShape.width, viewExtend, percentExtent, true)]);\n    this._handleEnds = [brushShape.x, brushShape.x + brushShape.width];\n\n    this._updateView();\n\n    this._dispatchZoomAction(false);\n  };\n\n  SliderZoomView.prototype._onBrush = function (e) {\n    if (this._brushing) {\n      // For mobile device, prevent screen slider on the button.\n      eventTool.stop(e.event);\n\n      this._updateBrushRect(e.offsetX, e.offsetY);\n    }\n  };\n\n  SliderZoomView.prototype._updateBrushRect = function (mouseX, mouseY) {\n    var displayables = this._displayables;\n    var dataZoomModel = this.dataZoomModel;\n    var brushRect = displayables.brushRect;\n\n    if (!brushRect) {\n      brushRect = displayables.brushRect = new Rect({\n        silent: true,\n        style: dataZoomModel.getModel('brushStyle').getItemStyle()\n      });\n      displayables.sliderGroup.add(brushRect);\n    }\n\n    brushRect.attr('ignore', false);\n    var brushStart = this._brushStart;\n    var sliderGroup = this._displayables.sliderGroup;\n    var endPoint = sliderGroup.transformCoordToLocal(mouseX, mouseY);\n    var startPoint = sliderGroup.transformCoordToLocal(brushStart.x, brushStart.y);\n    var size = this._size;\n    endPoint[0] = Math.max(Math.min(size[0], endPoint[0]), 0);\n    brushRect.setShape({\n      x: startPoint[0],\n      y: 0,\n      width: endPoint[0] - startPoint[0],\n      height: size[1]\n    });\n  };\n  /**\n   * This action will be throttled.\n   */\n\n\n  SliderZoomView.prototype._dispatchZoomAction = function (realtime) {\n    var range = this._range;\n    this.api.dispatchAction({\n      type: 'dataZoom',\n      from: this.uid,\n      dataZoomId: this.dataZoomModel.id,\n      animation: realtime ? REALTIME_ANIMATION_CONFIG : null,\n      start: range[0],\n      end: range[1]\n    });\n  };\n\n  SliderZoomView.prototype._findCoordRect = function () {\n    // Find the grid coresponding to the first axis referred by dataZoom.\n    var rect;\n    var coordSysInfoList = collectReferCoordSysModelInfo(this.dataZoomModel).infoList;\n\n    if (!rect && coordSysInfoList.length) {\n      var coordSys = coordSysInfoList[0].model.coordinateSystem;\n      rect = coordSys.getRect && coordSys.getRect();\n    }\n\n    if (!rect) {\n      var width = this.api.getWidth();\n      var height = this.api.getHeight();\n      rect = {\n        x: width * 0.2,\n        y: height * 0.2,\n        width: width * 0.6,\n        height: height * 0.6\n      };\n    }\n\n    return rect;\n  };\n\n  SliderZoomView.type = 'dataZoom.slider';\n  return SliderZoomView;\n}(DataZoomView);\n\nfunction getOtherDim(thisDim) {\n  // FIXME\n  // 这个逻辑和getOtherAxis里一致，但是写在这里是否不好\n  var map = {\n    x: 'y',\n    y: 'x',\n    radius: 'angle',\n    angle: 'radius'\n  };\n  return map[thisDim];\n}\n\nfunction getCursor(orient) {\n  return orient === 'vertical' ? 'ns-resize' : 'ew-resize';\n}\n\nexport default SliderZoomView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,0BAA0B;AACpF,OAAO,KAAKC,SAAS,MAAM,2BAA2B;AACtD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAO,KAAKC,QAAQ,MAAM,wBAAwB;AAClD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,SAASC,SAAS,EAAEC,GAAG,EAAEC,YAAY,QAAQ,sBAAsB;AACnE,OAAO,KAAKC,MAAM,MAAM,sBAAsB;AAC9C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,SAASC,eAAe,EAAEC,6BAA6B,QAAQ,aAAa;AAC5E,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,sBAAsB;AACvE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,IAAIC,IAAI,GAAGf,OAAO,CAACe,IAAI,CAAC,CAAC;;AAEzB,IAAIC,yBAAyB,GAAG,CAAC;AACjC,IAAIC,0BAA0B,GAAG,CAAC;AAClC,IAAIC,mBAAmB,GAAG,EAAE;AAC5B,IAAIC,wBAAwB,GAAG,CAAC;AAChC,IAAIC,UAAU,GAAG,YAAY;AAC7B,IAAIC,QAAQ,GAAG,UAAU;AACzB,IAAIC,SAAS,GAAG,CAAC;AACjB,IAAIC,4BAA4B,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,SAAS,CAAC;AAC5E,IAAIC,yBAAyB,GAAG;EAC9BC,MAAM,EAAE,UAAU;EAClBC,QAAQ,EAAE,GAAG;EACbC,KAAK,EAAE;AACT,CAAC;AAED,IAAIC,cAAc,GAClB;AACA,UAAUC,MAAM,EAAE;EAChBpC,SAAS,CAACmC,cAAc,EAAEC,MAAM,CAAC;EAEjC,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IAEpEF,KAAK,CAACG,IAAI,GAAGL,cAAc,CAACK,IAAI;IAChCH,KAAK,CAACI,aAAa,GAAG,CAAC,CAAC;IACxB,OAAOJ,KAAK;EACd;EAEAF,cAAc,CAACO,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACtD,IAAI,CAACA,GAAG,GAAGA,GAAG,CAAC,CAAC;;IAEhB,IAAI,CAACC,QAAQ,GAAG7C,IAAI,CAAC,IAAI,CAAC6C,QAAQ,EAAE,IAAI,CAAC;IACzC,IAAI,CAACC,WAAW,GAAG9C,IAAI,CAAC,IAAI,CAAC8C,WAAW,EAAE,IAAI,CAAC;EACjD,CAAC;EAEDZ,cAAc,CAACO,SAAS,CAACM,MAAM,GAAG,UAAUC,aAAa,EAAEL,OAAO,EAAEC,GAAG,EAAEK,OAAO,EAAE;IAChFd,MAAM,CAACM,SAAS,CAACM,MAAM,CAACV,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAE9C/B,QAAQ,CAAC2C,cAAc,CAAC,IAAI,EAAE,qBAAqB,EAAEF,aAAa,CAACG,GAAG,CAAC,UAAU,CAAC,EAAE,SAAS,CAAC;IAC9F,IAAI,CAACC,OAAO,GAAGJ,aAAa,CAACK,SAAS,CAAC,CAAC;IAExC,IAAIL,aAAa,CAACG,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,EAAE;MACvC,IAAI,CAACG,KAAK,CAACC,SAAS,CAAC,CAAC;MACtB;IACF;IAEA,IAAIP,aAAa,CAACQ,QAAQ,CAAC,CAAC,EAAE;MAC5B,IAAI,CAACC,MAAM,CAAC,CAAC;MAEb,IAAI,CAACH,KAAK,CAACC,SAAS,CAAC,CAAC;MACtB;IACF,CAAC,CAAC;IACF;IACA;;IAGA,IAAI,CAACN,OAAO,IAAIA,OAAO,CAACV,IAAI,KAAK,UAAU,IAAIU,OAAO,CAACS,IAAI,KAAK,IAAI,CAACC,GAAG,EAAE;MACxE,IAAI,CAACC,UAAU,CAAC,CAAC;IACnB;IAEA,IAAI,CAACC,WAAW,CAAC,CAAC;EACpB,CAAC;EAED3B,cAAc,CAACO,SAAS,CAACqB,OAAO,GAAG,YAAY;IAC7C,IAAI,CAACL,MAAM,CAAC,CAAC;IAEbtB,MAAM,CAACM,SAAS,CAACqB,OAAO,CAACzB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACjD,CAAC;EAEDJ,cAAc,CAACO,SAAS,CAACgB,MAAM,GAAG,YAAY;IAC5ClD,QAAQ,CAACwD,KAAK,CAAC,IAAI,EAAE,qBAAqB,CAAC;IAC3C,IAAIC,EAAE,GAAG,IAAI,CAACpB,GAAG,CAACqB,KAAK,CAAC,CAAC;IACzBD,EAAE,CAACE,GAAG,CAAC,WAAW,EAAE,IAAI,CAACrB,QAAQ,CAAC;IAClCmB,EAAE,CAACE,GAAG,CAAC,SAAS,EAAE,IAAI,CAACpB,WAAW,CAAC;EACrC,CAAC;EAEDZ,cAAc,CAACO,SAAS,CAACmB,UAAU,GAAG,YAAY;IAChD,IAAIO,SAAS,GAAG,IAAI,CAACb,KAAK;IAC1Ba,SAAS,CAACZ,SAAS,CAAC,CAAC;IACrB,IAAI,CAACa,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC5B,aAAa,CAAC6B,SAAS,GAAG,IAAI;IAEnC,IAAI,CAACC,cAAc,CAAC,CAAC;IAErB,IAAI,CAACC,cAAc,CAAC,CAAC;IAErB,IAAIC,QAAQ,GAAG,IAAI,CAAChC,aAAa,CAACiC,WAAW,GAAG,IAAInE,OAAO,CAACoE,KAAK,CAAC,CAAC;IAEnE,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAExB,IAAI,CAACC,aAAa,CAAC,CAAC;IAEpB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAExBV,SAAS,CAACW,GAAG,CAACN,QAAQ,CAAC;IAEvB,IAAI,CAACO,cAAc,CAAC,CAAC;EACvB,CAAC;EAED7C,cAAc,CAACO,SAAS,CAAC6B,cAAc,GAAG,YAAY;IACpD,IAAItB,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIJ,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIoC,cAAc,GAAGhC,aAAa,CAACG,GAAG,CAAC,aAAa,CAAC;IACrD,IAAI8B,cAAc,GAAGD,cAAc,GAAGvD,wBAAwB,GAAG,CAAC,CAAC,CAAC;IACpE;;IAEA,IAAIyD,SAAS,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IAErC,IAAIC,MAAM,GAAG;MACXC,KAAK,EAAEzC,GAAG,CAAC0C,QAAQ,CAAC,CAAC;MACrBC,MAAM,EAAE3C,GAAG,CAAC4C,SAAS,CAAC;IACxB,CAAC,CAAC,CAAC;;IAEH,IAAIC,YAAY,GAAG,IAAI,CAACrC,OAAO,KAAK1B,UAAU,GAAG;MAC/C;MACA;MACAgE,KAAK,EAAEN,MAAM,CAACC,KAAK,GAAGH,SAAS,CAACS,CAAC,GAAGT,SAAS,CAACG,KAAK;MACnDO,GAAG,EAAER,MAAM,CAACG,MAAM,GAAG/D,mBAAmB,GAAGF,yBAAyB,GAAG2D,cAAc;MACrFI,KAAK,EAAEH,SAAS,CAACG,KAAK;MACtBE,MAAM,EAAE/D;IACV,CAAC,GAAG;MACFkE,KAAK,EAAEpE,yBAAyB;MAChCsE,GAAG,EAAEV,SAAS,CAACW,CAAC;MAChBR,KAAK,EAAE7D,mBAAmB;MAC1B+D,MAAM,EAAEL,SAAS,CAACK;IACpB,CAAC,CAAC,CAAC;IACH;;IAEA,IAAIO,YAAY,GAAGlF,MAAM,CAACmF,eAAe,CAAC/C,aAAa,CAACgD,MAAM,CAAC,CAAC,CAAC;;IAEjE/F,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,UAAUgG,IAAI,EAAE;MACxD,IAAIH,YAAY,CAACG,IAAI,CAAC,KAAK,IAAI,EAAE;QAC/BH,YAAY,CAACG,IAAI,CAAC,GAAGR,YAAY,CAACQ,IAAI,CAAC;MACzC;IACF,CAAC,CAAC;IACF,IAAIC,UAAU,GAAGtF,MAAM,CAACuF,aAAa,CAACL,YAAY,EAAEV,MAAM,CAAC;IAC3D,IAAI,CAACgB,SAAS,GAAG;MACfT,CAAC,EAAEO,UAAU,CAACP,CAAC;MACfE,CAAC,EAAEK,UAAU,CAACL;IAChB,CAAC;IACD,IAAI,CAACQ,KAAK,GAAG,CAACH,UAAU,CAACb,KAAK,EAAEa,UAAU,CAACX,MAAM,CAAC;IAClD,IAAI,CAACnC,OAAO,KAAKzB,QAAQ,IAAI,IAAI,CAAC0E,KAAK,CAACC,OAAO,CAAC,CAAC;EACnD,CAAC;EAEDpE,cAAc,CAACO,SAAS,CAACsC,cAAc,GAAG,YAAY;IACpD,IAAIZ,SAAS,GAAG,IAAI,CAACb,KAAK;IAC1B,IAAIiD,QAAQ,GAAG,IAAI,CAACH,SAAS;IAC7B,IAAII,MAAM,GAAG,IAAI,CAACpD,OAAO,CAAC,CAAC;;IAE3B,IAAIqD,eAAe,GAAG,IAAI,CAACzD,aAAa,CAAC0D,uBAAuB,CAAC,CAAC;IAClE,IAAIC,OAAO,GAAGF,eAAe,IAAIA,eAAe,CAACtD,GAAG,CAAC,SAAS,CAAC;IAC/D,IAAIsB,WAAW,GAAG,IAAI,CAACjC,aAAa,CAACiC,WAAW;IAChD,IAAImC,gBAAgB,GAAG,CAAC,IAAI,CAACC,eAAe,IAAI,CAAC,CAAC,EAAED,gBAAgB,CAAC,CAAC;;IAEtEnC,WAAW,CAACqC,IAAI,CAACN,MAAM,KAAK9E,UAAU,IAAI,CAACiF,OAAO,GAAG;MACnDI,MAAM,EAAEH,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;MACjCI,MAAM,EAAE;IACV,CAAC,GAAGR,MAAM,KAAK9E,UAAU,IAAIiF,OAAO,GAAG;MACrCI,MAAM,EAAEH,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;MACjCI,MAAM,EAAE,CAAC;IACX,CAAC,GAAGR,MAAM,KAAK7E,QAAQ,IAAI,CAACgF,OAAO,GAAG;MACpCI,MAAM,EAAEH,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC;MACjCI,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAEC,IAAI,CAACC,EAAE,GAAG;IACtB,CAAC,CAAC;IAAA,EACA;MACAJ,MAAM,EAAEH,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC;MACjCI,MAAM,EAAE,CAAC,CAAC;MACVC,QAAQ,EAAEC,IAAI,CAACC,EAAE,GAAG;IACtB,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIC,IAAI,GAAGjD,SAAS,CAACkD,eAAe,CAAC,CAAC5C,WAAW,CAAC,CAAC;IACnDN,SAAS,CAACwB,CAAC,GAAGY,QAAQ,CAACZ,CAAC,GAAGyB,IAAI,CAACzB,CAAC;IACjCxB,SAAS,CAAC0B,CAAC,GAAGU,QAAQ,CAACV,CAAC,GAAGuB,IAAI,CAACvB,CAAC;IACjC1B,SAAS,CAACmD,UAAU,CAAC,CAAC;EACxB,CAAC;EAEDpF,cAAc,CAACO,SAAS,CAAC8E,cAAc,GAAG,YAAY;IACpD,OAAO,CAAC,CAAC,EAAE,IAAI,CAAClB,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3B,CAAC;EAEDnE,cAAc,CAACO,SAAS,CAACkC,iBAAiB,GAAG,YAAY;IACvD,IAAI3B,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIwE,IAAI,GAAG,IAAI,CAACnB,KAAK;IACrB,IAAI7B,QAAQ,GAAG,IAAI,CAAChC,aAAa,CAACiC,WAAW;IAC7C,IAAIgD,WAAW,GAAGzE,aAAa,CAACG,GAAG,CAAC,aAAa,CAAC;IAClDqB,QAAQ,CAACM,GAAG,CAAC,IAAIzD,IAAI,CAAC;MACpBqG,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;QACLhC,CAAC,EAAE,CAAC;QACJE,CAAC,EAAE,CAAC;QACJR,KAAK,EAAEmC,IAAI,CAAC,CAAC,CAAC;QACdjC,MAAM,EAAEiC,IAAI,CAAC,CAAC;MAChB,CAAC;MACDI,KAAK,EAAE;QACLC,IAAI,EAAE7E,aAAa,CAACG,GAAG,CAAC,iBAAiB;MAC3C,CAAC;MACD2E,EAAE,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,IAAIC,UAAU,GAAG,IAAI1G,IAAI,CAAC;MACxBsG,KAAK,EAAE;QACLhC,CAAC,EAAE,CAAC;QACJE,CAAC,EAAE,CAAC;QACJR,KAAK,EAAEmC,IAAI,CAAC,CAAC,CAAC;QACdjC,MAAM,EAAEiC,IAAI,CAAC,CAAC;MAChB,CAAC;MACDI,KAAK,EAAE;QACLC,IAAI,EAAE;MACR,CAAC;MACDC,EAAE,EAAE,CAAC;MACLE,OAAO,EAAEhI,IAAI,CAAC,IAAI,CAACiI,aAAa,EAAE,IAAI;IACxC,CAAC,CAAC;IACF,IAAIjE,EAAE,GAAG,IAAI,CAACpB,GAAG,CAACqB,KAAK,CAAC,CAAC;IAEzB,IAAIwD,WAAW,EAAE;MACfM,UAAU,CAACG,EAAE,CAAC,WAAW,EAAE,IAAI,CAACC,aAAa,EAAE,IAAI,CAAC;MACpDJ,UAAU,CAACK,MAAM,GAAG,WAAW;MAC/BpE,EAAE,CAACkE,EAAE,CAAC,WAAW,EAAE,IAAI,CAACrF,QAAQ,CAAC;MACjCmB,EAAE,CAACkE,EAAE,CAAC,SAAS,EAAE,IAAI,CAACpF,WAAW,CAAC;IACpC,CAAC,MAAM;MACLkB,EAAE,CAACE,GAAG,CAAC,WAAW,EAAE,IAAI,CAACrB,QAAQ,CAAC;MAClCmB,EAAE,CAACE,GAAG,CAAC,SAAS,EAAE,IAAI,CAACpB,WAAW,CAAC;IACrC;IAEA0B,QAAQ,CAACM,GAAG,CAACiD,UAAU,CAAC;EAC1B,CAAC;EAED7F,cAAc,CAACO,SAAS,CAACoC,iBAAiB,GAAG,YAAY;IACvD,IAAIwD,IAAI,GAAG,IAAI,CAACxB,eAAe,GAAG,IAAI,CAACyB,sBAAsB,CAAC,CAAC;IAE/D,IAAI,CAAC9F,aAAa,CAAC+F,cAAc,GAAG,EAAE;IAEtC,IAAI,CAACF,IAAI,EAAE;MACT;IACF;IAEA,IAAIb,IAAI,GAAG,IAAI,CAACnB,KAAK;IACrB,IAAImC,OAAO,GAAG,IAAI,CAACC,WAAW,IAAI,EAAE;IACpC,IAAIC,WAAW,GAAGL,IAAI,CAACM,MAAM;IAC7B,IAAIC,IAAI,GAAGF,WAAW,CAACG,UAAU,CAAC,CAAC;IACnC,IAAIC,QAAQ,GAAGJ,WAAW,CAACK,YAAY,GAAGL,WAAW,CAACK,YAAY,CAAC,CAAC,CAAC;IAAA,EACnEV,IAAI,CAACS,QAAQ;IAEf,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACpB;IACF;IAEA,IAAIE,UAAU,GAAG,IAAI,CAACC,iBAAiB;IACvC,IAAIC,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;;IAE3C,IAAIP,IAAI,KAAK,IAAI,CAACQ,WAAW,IAAIN,QAAQ,KAAK,IAAI,CAACO,UAAU,IAAI7B,IAAI,CAAC,CAAC,CAAC,KAAKgB,OAAO,CAAC,CAAC,CAAC,IAAIhB,IAAI,CAAC,CAAC,CAAC,KAAKgB,OAAO,CAAC,CAAC,CAAC,EAAE;MACjH,IAAIc,iBAAiB,GAAGV,IAAI,CAACW,aAAa,CAACT,QAAQ,CAAC,CAAC,CAAC;;MAEtD,IAAIU,WAAW,GAAG,CAACF,iBAAiB,CAAC,CAAC,CAAC,GAAGA,iBAAiB,CAAC,CAAC,CAAC,IAAI,GAAG;MACrEA,iBAAiB,GAAG,CAACA,iBAAiB,CAAC,CAAC,CAAC,GAAGE,WAAW,EAAEF,iBAAiB,CAAC,CAAC,CAAC,GAAGE,WAAW,CAAC;MAC5F,IAAIC,mBAAmB,GAAG,CAAC,CAAC,EAAEjC,IAAI,CAAC,CAAC,CAAC,CAAC;MACtC,IAAIkC,gBAAgB,GAAG,CAAC,CAAC,EAAElC,IAAI,CAAC,CAAC,CAAC,CAAC;MACnC,IAAImC,YAAY,GAAG,CAAC,CAACnC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACzC,IAAIoC,YAAY,GAAG,EAAE;MACrB,IAAIC,MAAM,GAAGH,gBAAgB,CAAC,CAAC,CAAC,IAAId,IAAI,CAACkB,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;MACrD,IAAIC,WAAW,GAAG,CAAC,CAAC,CAAC;;MAErB,IAAIC,QAAQ,GAAG9C,IAAI,CAAC+C,KAAK,CAACrB,IAAI,CAACkB,KAAK,CAAC,CAAC,GAAGtC,IAAI,CAAC,CAAC,CAAC,CAAC;MACjD,IAAI0C,aAAa;MACjBtB,IAAI,CAAC3I,IAAI,CAAC,CAAC6I,QAAQ,CAAC,EAAE,UAAUqB,KAAK,EAAEC,KAAK,EAAE;QAC5C,IAAIJ,QAAQ,GAAG,CAAC,IAAII,KAAK,GAAGJ,QAAQ,EAAE;UACpCD,WAAW,IAAIF,MAAM;UACrB;QACF,CAAC,CAAC;QACF;QACA;QACA;;QAGA,IAAIQ,OAAO,GAAGF,KAAK,IAAI,IAAI,IAAIG,KAAK,CAACH,KAAK,CAAC,IAAIA,KAAK,KAAK,EAAE,CAAC,CAAC;;QAE7D,IAAII,UAAU,GAAGF,OAAO,GAAG,CAAC,GAAG5J,SAAS,CAAC0J,KAAK,EAAEb,iBAAiB,EAAEG,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAAC;;QAE/F,IAAIY,OAAO,IAAI,CAACH,aAAa,IAAIE,KAAK,EAAE;UACtCT,YAAY,CAACa,IAAI,CAAC,CAACb,YAAY,CAACA,YAAY,CAACc,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChEb,YAAY,CAACY,IAAI,CAAC,CAACZ,YAAY,CAACA,YAAY,CAACa,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClE,CAAC,MAAM,IAAI,CAACJ,OAAO,IAAIH,aAAa,EAAE;UACpCP,YAAY,CAACa,IAAI,CAAC,CAACT,WAAW,EAAE,CAAC,CAAC,CAAC;UACnCH,YAAY,CAACY,IAAI,CAAC,CAACT,WAAW,EAAE,CAAC,CAAC,CAAC;QACrC;QAEAJ,YAAY,CAACa,IAAI,CAAC,CAACT,WAAW,EAAEQ,UAAU,CAAC,CAAC;QAC5CX,YAAY,CAACY,IAAI,CAAC,CAACT,WAAW,EAAEQ,UAAU,CAAC,CAAC;QAC5CR,WAAW,IAAIF,MAAM;QACrBK,aAAa,GAAGG,OAAO;MACzB,CAAC,CAAC;MACFrB,UAAU,GAAG,IAAI,CAACC,iBAAiB,GAAGU,YAAY;MAClDT,WAAW,GAAG,IAAI,CAACC,kBAAkB,GAAGS,YAAY;IACtD;IAEA,IAAI,CAACR,WAAW,GAAGR,IAAI;IACvB,IAAI,CAACS,UAAU,GAAGP,QAAQ;IAC1B,IAAI,CAACL,WAAW,GAAG,CAACjB,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IACrC,IAAIxE,aAAa,GAAG,IAAI,CAACA,aAAa;IAEtC,SAAS0H,qBAAqBA,CAACC,cAAc,EAAE;MAC7C,IAAIC,KAAK,GAAG5H,aAAa,CAAC6H,QAAQ,CAACF,cAAc,GAAG,wBAAwB,GAAG,gBAAgB,CAAC;MAChG,IAAIrH,KAAK,GAAG,IAAIhD,OAAO,CAACoE,KAAK,CAAC,CAAC;MAC/B,IAAIoG,OAAO,GAAG,IAAIxK,OAAO,CAACyK,OAAO,CAAC;QAChCpD,KAAK,EAAE;UACLqD,MAAM,EAAEhC;QACV,CAAC;QACDiC,sBAAsB,EAAE,CAAC;QACzBrD,KAAK,EAAEgD,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC,CAACK,YAAY,CAAC,CAAC;QACjDxD,MAAM,EAAE,IAAI;QACZI,EAAE,EAAE,CAAC;MACP,CAAC,CAAC;MACF,IAAIqD,QAAQ,GAAG,IAAI7K,OAAO,CAAC8K,QAAQ,CAAC;QAClCzD,KAAK,EAAE;UACLqD,MAAM,EAAE9B;QACV,CAAC;QACD+B,sBAAsB,EAAE,CAAC;QACzBrD,KAAK,EAAEgD,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC,CAACQ,YAAY,CAAC,CAAC;QACjD3D,MAAM,EAAE,IAAI;QACZI,EAAE,EAAE,CAAC;MACP,CAAC,CAAC;MACFxE,KAAK,CAACwB,GAAG,CAACgG,OAAO,CAAC;MAClBxH,KAAK,CAACwB,GAAG,CAACqG,QAAQ,CAAC;MACnB,OAAO7H,KAAK;IACd,CAAC,CAAC;;IAGF,KAAK,IAAIgI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAIhI,KAAK,GAAGoH,qBAAqB,CAACY,CAAC,KAAK,CAAC,CAAC;MAE1C,IAAI,CAAC9I,aAAa,CAACiC,WAAW,CAACK,GAAG,CAACxB,KAAK,CAAC;MAEzC,IAAI,CAACd,aAAa,CAAC+F,cAAc,CAACiC,IAAI,CAAClH,KAAK,CAAC;IAC/C;EACF,CAAC;EAEDpB,cAAc,CAACO,SAAS,CAAC6F,sBAAsB,GAAG,YAAY;IAC5D,IAAItF,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIuI,cAAc,GAAGvI,aAAa,CAACG,GAAG,CAAC,gBAAgB,CAAC;IAExD,IAAIoI,cAAc,KAAK,KAAK,EAAE;MAC5B;IACF,CAAC,CAAC;;IAGF,IAAIC,MAAM;IACV,IAAI7I,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1BK,aAAa,CAACyI,cAAc,CAAC,UAAUC,OAAO,EAAEC,SAAS,EAAE;MACzD,IAAIC,YAAY,GAAG5I,aAAa,CAAC6I,YAAY,CAACH,OAAO,EAAEC,SAAS,CAAC,CAACG,qBAAqB,CAAC,CAAC;MACzF7L,IAAI,CAAC2L,YAAY,EAAE,UAAUlD,WAAW,EAAE;QACxC,IAAI8C,MAAM,EAAE;UACV;QACF;QAEA,IAAID,cAAc,KAAK,IAAI,IAAInL,OAAO,CAACyB,4BAA4B,EAAE6G,WAAW,CAACvF,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;UACjG;QACF;QAEA,IAAI4I,QAAQ,GAAGpJ,OAAO,CAACqJ,YAAY,CAAClL,eAAe,CAAC4K,OAAO,CAAC,EAAEC,SAAS,CAAC,CAACM,IAAI;QAC7E,IAAInD,QAAQ,GAAGoD,WAAW,CAACR,OAAO,CAAC;QACnC,IAAI9E,gBAAgB;QACpB,IAAIuF,QAAQ,GAAGzD,WAAW,CAAC0D,gBAAgB;QAE3C,IAAItD,QAAQ,IAAI,IAAI,IAAIqD,QAAQ,CAACE,YAAY,EAAE;UAC7CzF,gBAAgB,GAAGuF,QAAQ,CAACE,YAAY,CAACN,QAAQ,CAAC,CAACpF,OAAO;QAC5D;QAEAmC,QAAQ,GAAGJ,WAAW,CAAC4D,OAAO,CAAC,CAAC,CAACC,YAAY,CAACzD,QAAQ,CAAC;QACvD0C,MAAM,GAAG;UACPO,QAAQ,EAAEA,QAAQ;UAClBpD,MAAM,EAAED,WAAW;UACnB8D,OAAO,EAAEd,OAAO;UAChB5C,QAAQ,EAAEA,QAAQ;UAClBlC,gBAAgB,EAAEA;QACpB,CAAC;MACH,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,EAAE,IAAI,CAAC;IACR,OAAO4E,MAAM;EACf,CAAC;EAEDtJ,cAAc,CAACO,SAAS,CAACmC,aAAa,GAAG,YAAY;IACnD,IAAIT,SAAS,GAAG,IAAI,CAACb,KAAK;IAC1B,IAAImJ,YAAY,GAAG,IAAI,CAACjK,aAAa;IACrC,IAAIkK,OAAO,GAAGD,YAAY,CAACC,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IACjD,IAAIC,YAAY,GAAGF,YAAY,CAACE,YAAY,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC3D,IAAIlI,WAAW,GAAG,IAAI,CAACjC,aAAa,CAACiC,WAAW;IAChD,IAAI+C,IAAI,GAAG,IAAI,CAACnB,KAAK;IACrB,IAAIrD,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIJ,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIgK,YAAY,GAAG5J,aAAa,CAACG,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC;IACzD,IAAIsE,WAAW,GAAGzE,aAAa,CAACG,GAAG,CAAC,aAAa,CAAC;IAClD,IAAI0J,MAAM,GAAGJ,YAAY,CAACI,MAAM,GAAG,IAAIxL,IAAI,CAAC;MAC1CqG,MAAM,EAAED,WAAW;MACnBG,KAAK,EAAE;QACLC,IAAI,EAAE7E,aAAa,CAACG,GAAG,CAAC,aAAa;MACvC,CAAC;MACD2J,UAAU,EAAE;QACVC,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;IACFtI,WAAW,CAACK,GAAG,CAAC+H,MAAM,CAAC,CAAC,CAAC;;IAEzBpI,WAAW,CAACK,GAAG,CAAC,IAAIzD,IAAI,CAAC;MACvBqG,MAAM,EAAE,IAAI;MACZsF,gBAAgB,EAAE,IAAI;MACtBrF,KAAK,EAAE;QACLhC,CAAC,EAAE,CAAC;QACJE,CAAC,EAAE,CAAC;QACJR,KAAK,EAAEmC,IAAI,CAAC,CAAC,CAAC;QACdjC,MAAM,EAAEiC,IAAI,CAAC,CAAC,CAAC;QACfyF,CAAC,EAAEL;MACL,CAAC;MACDhF,KAAK,EAAE;QACL;QACAsF,MAAM,EAAElK,aAAa,CAACG,GAAG,CAAC,qBAAqB,CAAC,IAAIH,aAAa,CAACG,GAAG,CAAC,aAAa,CAAC;QACpFgK,SAAS,EAAE5L,0BAA0B;QACrCsG,IAAI,EAAE;MACR;IACF,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL5H,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAUmN,WAAW,EAAE;MAClC,IAAIC,OAAO,GAAGrK,aAAa,CAACG,GAAG,CAAC,YAAY,CAAC;MAE7C,IAAI,CAACjC,kBAAkB,CAACmM,OAAO,CAAC,IAAIA,OAAO,CAACjN,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAIiN,OAAO,CAACjN,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACrG;QACAiN,OAAO,GAAG,SAAS,GAAGA,OAAO;QAE7B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCrM,YAAY,CAAC,kEAAkE,CAAC;QAClF;MACF;MAEA,IAAIsM,IAAI,GAAGxM,YAAY,CAACoM,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;MACzDI,IAAI,CAAC3G,IAAI,CAAC;QACRsB,MAAM,EAAEsF,SAAS,CAAC,IAAI,CAACtK,OAAO,CAAC;QAC/BuK,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE5N,IAAI,CAAC,IAAI,CAAC6N,WAAW,EAAE,IAAI,EAAET,WAAW,CAAC;QAChDU,SAAS,EAAE9N,IAAI,CAAC,IAAI,CAAC+N,UAAU,EAAE,IAAI,CAAC;QACtCC,WAAW,EAAEhO,IAAI,CAAC,IAAI,CAACiO,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;QACjDC,UAAU,EAAElO,IAAI,CAAC,IAAI,CAACiO,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC;QACjDnG,EAAE,EAAE;MACN,CAAC,CAAC;MACF,IAAIqG,KAAK,GAAGV,IAAI,CAACpG,eAAe,CAAC,CAAC;MAClC,IAAI+G,UAAU,GAAGpL,aAAa,CAACG,GAAG,CAAC,YAAY,CAAC;MAChD,IAAI,CAACkL,aAAa,GAAG1N,YAAY,CAACyN,UAAU,EAAE,IAAI,CAAC/H,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5D,IAAI,CAACiI,YAAY,GAAGH,KAAK,CAAC9I,KAAK,GAAG8I,KAAK,CAAC5I,MAAM,GAAG,IAAI,CAAC8I,aAAa;MACnEZ,IAAI,CAACc,QAAQ,CAACvL,aAAa,CAAC6H,QAAQ,CAAC,aAAa,CAAC,CAAC2D,YAAY,CAAC,CAAC,CAAC;MACnEf,IAAI,CAAC7F,KAAK,CAAC6G,aAAa,GAAG,IAAI;MAC/BhB,IAAI,CAACiB,SAAS,GAAG,IAAI;MACrBjB,IAAI,CAACkB,WAAW,CAAC,UAAU,CAAC,CAAC/G,KAAK,GAAG5E,aAAa,CAAC6H,QAAQ,CAAC,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC2D,YAAY,CAAC,CAAC;MACvGxN,mBAAmB,CAACyM,IAAI,CAAC;MACzB,IAAImB,WAAW,GAAG5L,aAAa,CAACG,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;MACpD;;MAEA,IAAIyL,WAAW,IAAI,IAAI,EAAE;QACvBnB,IAAI,CAAC7F,KAAK,CAACC,IAAI,GAAG+G,WAAW;MAC/B;MAEAnK,WAAW,CAACK,GAAG,CAAC4H,OAAO,CAACU,WAAW,CAAC,GAAGK,IAAI,CAAC;MAC5C,IAAIoB,cAAc,GAAG7L,aAAa,CAAC6H,QAAQ,CAAC,WAAW,CAAC;MACxD1G,SAAS,CAACW,GAAG,CAAC6H,YAAY,CAACS,WAAW,CAAC,GAAG,IAAI9M,OAAO,CAACwO,IAAI,CAAC;QACzDpH,MAAM,EAAE,IAAI;QACZqH,SAAS,EAAE,IAAI;QACfnH,KAAK,EAAExG,eAAe,CAACyN,cAAc,EAAE;UACrClJ,CAAC,EAAE,CAAC;UACJE,CAAC,EAAE,CAAC;UACJmJ,IAAI,EAAE,EAAE;UACRC,aAAa,EAAE,QAAQ;UACvBC,KAAK,EAAE,QAAQ;UACfrH,IAAI,EAAEgH,cAAc,CAACM,YAAY,CAAC,CAAC;UACnCC,IAAI,EAAEP,cAAc,CAACQ,OAAO,CAAC;QAC/B,CAAC,CAAC;QACFvH,EAAE,EAAE;MACN,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,IAAIwH,cAAc,GAAGzC,MAAM;IAE3B,IAAIpF,WAAW,EAAE;MACf,IAAI8H,gBAAgB,GAAG5O,YAAY,CAACqC,aAAa,CAACG,GAAG,CAAC,gBAAgB,CAAC,EAAEqE,IAAI,CAAC,CAAC,CAAC,CAAC;MACjF,IAAIgI,YAAY,GAAG/C,YAAY,CAACgD,UAAU,GAAG,IAAInP,OAAO,CAACe,IAAI,CAAC;QAC5DuG,KAAK,EAAE5E,aAAa,CAAC6H,QAAQ,CAAC,iBAAiB,CAAC,CAAC2D,YAAY,CAAC,CAAC;QAC/D9G,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE;UACLsF,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACfpH,CAAC,EAAE2B,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;UAChBjC,MAAM,EAAEgK;QACV;MACF,CAAC,CAAC;MACF,IAAIG,QAAQ,GAAGH,gBAAgB,GAAG,GAAG;MACrC,IAAII,cAAc,GAAGlD,YAAY,CAACkD,cAAc,GAAG1O,YAAY,CAAC+B,aAAa,CAACG,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAACuM,QAAQ,GAAG,CAAC,EAAE,CAACA,QAAQ,GAAG,CAAC,EAAEA,QAAQ,EAAEA,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC;MACpKC,cAAc,CAACjI,MAAM,GAAG,IAAI;MAC5BiI,cAAc,CAAC9J,CAAC,GAAG2B,IAAI,CAAC,CAAC,CAAC,GAAG+H,gBAAgB,GAAG,CAAC,GAAG,GAAG;MACvDC,YAAY,CAACb,WAAW,CAAC,UAAU,CAAC,CAAC/G,KAAK,GAAG5E,aAAa,CAAC6H,QAAQ,CAAC,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC,CAAC2D,YAAY,CAAC,CAAC;MACnH,IAAIoB,kBAAkB,GAAG1I,IAAI,CAAC2I,GAAG,CAACrI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEN,IAAI,CAAC4I,GAAG,CAACP,gBAAgB,EAAE,EAAE,CAAC,CAAC;MAC9ED,cAAc,GAAG7C,YAAY,CAACsD,QAAQ,GAAG,IAAIzP,OAAO,CAACe,IAAI,CAAC;QACxD0N,SAAS,EAAE,IAAI;QACfpH,KAAK,EAAE;UACL9B,CAAC,EAAE2B,IAAI,CAAC,CAAC,CAAC,GAAGoI,kBAAkB;UAC/BrK,MAAM,EAAEgK,gBAAgB,GAAGK;QAC7B;MACF,CAAC,CAAC;MACFN,cAAc,CAACpH,EAAE,CAAC,WAAW,EAAE,YAAY;QACzCtF,GAAG,CAACoN,aAAa,CAACR,YAAY,CAAC;MACjC,CAAC,CAAC,CAACtH,EAAE,CAAC,UAAU,EAAE,YAAY;QAC5BtF,GAAG,CAACqN,aAAa,CAACT,YAAY,CAAC;MACjC,CAAC,CAAC;MACF/K,WAAW,CAACK,GAAG,CAAC0K,YAAY,CAAC;MAC7B/K,WAAW,CAACK,GAAG,CAAC6K,cAAc,CAAC;MAC/BlL,WAAW,CAACK,GAAG,CAACwK,cAAc,CAAC;IACjC;IAEAA,cAAc,CAACxI,IAAI,CAAC;MAClB6G,SAAS,EAAE,IAAI;MACfvF,MAAM,EAAEsF,SAAS,CAAC,IAAI,CAACtK,OAAO,CAAC;MAC/BwK,KAAK,EAAE5N,IAAI,CAAC,IAAI,CAAC6N,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC;MAC1CqC,WAAW,EAAElQ,IAAI,CAAC,IAAI,CAACiO,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;MACjDH,SAAS,EAAE9N,IAAI,CAAC,IAAI,CAAC+N,UAAU,EAAE,IAAI,CAAC;MACtCC,WAAW,EAAEhO,IAAI,CAAC,IAAI,CAACiO,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;MACjDC,UAAU,EAAElO,IAAI,CAAC,IAAI,CAACiO,aAAa,EAAE,IAAI,EAAE,KAAK;IAClD,CAAC,CAAC;EACJ,CAAC;EAED/L,cAAc,CAACO,SAAS,CAAC8B,cAAc,GAAG,YAAY;IACpD,IAAI4L,KAAK,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACpN,aAAa,CAACqN,eAAe,CAAC,CAAC;IAE9D,IAAIC,UAAU,GAAG,IAAI,CAAC/I,cAAc,CAAC,CAAC;IAEtC,IAAI,CAACgJ,WAAW,GAAG,CAAC9P,SAAS,CAAC0P,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAEG,UAAU,EAAE,IAAI,CAAC,EAAE7P,SAAS,CAAC0P,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAEG,UAAU,EAAE,IAAI,CAAC,CAAC;EACvH,CAAC;EAEDpO,cAAc,CAACO,SAAS,CAAC+N,eAAe,GAAG,UAAUpD,WAAW,EAAEqD,KAAK,EAAE;IACvE,IAAIzN,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAI0N,UAAU,GAAG,IAAI,CAACH,WAAW;IAEjC,IAAII,UAAU,GAAG,IAAI,CAACpJ,cAAc,CAAC,CAAC;IAEtC,IAAIqJ,UAAU,GAAG5N,aAAa,CAAC6N,2BAA2B,CAAC,CAAC,CAACC,aAAa,CAAC,CAAC;IAC5E,IAAIC,aAAa,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;IAC5BlQ,UAAU,CAAC4P,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAE3N,aAAa,CAACG,GAAG,CAAC,UAAU,CAAC,GAAG,KAAK,GAAGiK,WAAW,EAAEwD,UAAU,CAACI,OAAO,IAAI,IAAI,GAAGvQ,SAAS,CAACmQ,UAAU,CAACI,OAAO,EAAED,aAAa,EAAEJ,UAAU,EAAE,IAAI,CAAC,GAAG,IAAI,EAAEC,UAAU,CAACK,OAAO,IAAI,IAAI,GAAGxQ,SAAS,CAACmQ,UAAU,CAACK,OAAO,EAAEF,aAAa,EAAEJ,UAAU,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;IACtS,IAAIO,SAAS,GAAG,IAAI,CAACd,MAAM;IAC3B,IAAID,KAAK,GAAG,IAAI,CAACC,MAAM,GAAG1P,GAAG,CAAC,CAACD,SAAS,CAACiQ,UAAU,CAAC,CAAC,CAAC,EAAEC,UAAU,EAAEI,aAAa,EAAE,IAAI,CAAC,EAAEtQ,SAAS,CAACiQ,UAAU,CAAC,CAAC,CAAC,EAAEC,UAAU,EAAEI,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;IACrJ,OAAO,CAACG,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAKf,KAAK,CAAC,CAAC,CAAC,IAAIe,SAAS,CAAC,CAAC,CAAC,KAAKf,KAAK,CAAC,CAAC,CAAC;EAC7E,CAAC;EAEDjO,cAAc,CAACO,SAAS,CAACoB,WAAW,GAAG,UAAUsN,WAAW,EAAE;IAC5D,IAAIC,WAAW,GAAG,IAAI,CAAC5O,aAAa;IACpC,IAAIkO,UAAU,GAAG,IAAI,CAACH,WAAW;IACjC,IAAIc,cAAc,GAAG3Q,GAAG,CAACgQ,UAAU,CAACY,KAAK,CAAC,CAAC,CAAC;IAC5C,IAAI9J,IAAI,GAAG,IAAI,CAACnB,KAAK;IACrBpG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,UAAUmN,WAAW,EAAE;MAClC;MACA,IAAImE,MAAM,GAAGH,WAAW,CAAC1E,OAAO,CAACU,WAAW,CAAC;MAC7C,IAAIoE,YAAY,GAAG,IAAI,CAACnD,aAAa;MACrCkD,MAAM,CAACzK,IAAI,CAAC;QACVE,MAAM,EAAEwK,YAAY,GAAG,CAAC;QACxBzK,MAAM,EAAEyK,YAAY,GAAG,CAAC;QACxB;QACA;QACA7L,CAAC,EAAE+K,UAAU,CAACtD,WAAW,CAAC,IAAIA,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACnDvH,CAAC,EAAE2B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGgK,YAAY,GAAG;MAClC,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEVJ,WAAW,CAACvE,MAAM,CAAC4E,QAAQ,CAAC;MAC1B9L,CAAC,EAAE0L,cAAc,CAAC,CAAC,CAAC;MACpBxL,CAAC,EAAE,CAAC;MACJR,KAAK,EAAEgM,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC;MAC5C9L,MAAM,EAAEiC,IAAI,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,IAAI8I,UAAU,GAAG;MACf3K,CAAC,EAAE0L,cAAc,CAAC,CAAC,CAAC;MACpBhM,KAAK,EAAEgM,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;;IAEH,IAAID,WAAW,CAAC3B,UAAU,EAAE;MAC1B2B,WAAW,CAAC3B,UAAU,CAACgC,QAAQ,CAACnB,UAAU,CAAC;MAC3Cc,WAAW,CAACrB,QAAQ,CAAC0B,QAAQ,CAACnB,UAAU,CAAC,CAAC,CAAC;;MAE3Cc,WAAW,CAACrB,QAAQ,CAAC1I,eAAe,CAAC,CAAC;MACtC+J,WAAW,CAACzB,cAAc,IAAIyB,WAAW,CAACzB,cAAc,CAAC7I,IAAI,CAAC,GAAG,EAAEwJ,UAAU,CAAC3K,CAAC,GAAG2K,UAAU,CAACjL,KAAK,GAAG,CAAC,CAAC;IACzG,CAAC,CAAC;;IAGF,IAAIkD,cAAc,GAAG6I,WAAW,CAAC7I,cAAc;IAC/C,IAAImJ,YAAY,GAAG,CAAC,CAAC,EAAEL,cAAc,CAAC,CAAC,CAAC,EAAEA,cAAc,CAAC,CAAC,CAAC,EAAE7J,IAAI,CAAC,CAAC,CAAC,CAAC;IAErE,KAAK,IAAI8D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,cAAc,CAACkC,MAAM,EAAEa,CAAC,EAAE,EAAE;MAC9C,IAAIqG,QAAQ,GAAGpJ,cAAc,CAAC+C,CAAC,CAAC;MAChC,IAAIsG,QAAQ,GAAGD,QAAQ,CAACE,WAAW,CAAC,CAAC;MAErC,IAAI,CAACD,QAAQ,EAAE;QACbA,QAAQ,GAAG,IAAItR,OAAO,CAACe,IAAI,CAAC,CAAC;QAC7BsQ,QAAQ,CAACG,WAAW,CAACF,QAAQ,CAAC;MAChC;MAEAA,QAAQ,CAACH,QAAQ,CAAC;QAChB9L,CAAC,EAAE+L,YAAY,CAACpG,CAAC,CAAC;QAClBzF,CAAC,EAAE,CAAC;QACJR,KAAK,EAAEqM,YAAY,CAACpG,CAAC,GAAG,CAAC,CAAC,GAAGoG,YAAY,CAACpG,CAAC,CAAC;QAC5C/F,MAAM,EAAEiC,IAAI,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ;IAEA,IAAI,CAACuK,eAAe,CAACZ,WAAW,CAAC;EACnC,CAAC;EAEDjP,cAAc,CAACO,SAAS,CAACsP,eAAe,GAAG,UAAUZ,WAAW,EAAE;IAChE,IAAInO,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIoO,WAAW,GAAG,IAAI,CAAC5O,aAAa;IACpC,IAAImK,YAAY,GAAGyE,WAAW,CAACzE,YAAY;IAC3C,IAAInG,MAAM,GAAG,IAAI,CAACpD,OAAO;IACzB,IAAI4O,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3B;;IAEA,IAAIhP,aAAa,CAACG,GAAG,CAAC,YAAY,CAAC,EAAE;MACnC,IAAI8O,SAAS,GAAGjP,aAAa,CAAC6N,2BAA2B,CAAC,CAAC;MAE3D,IAAIoB,SAAS,EAAE;QACb,IAAIhG,IAAI,GAAGgG,SAAS,CAACC,YAAY,CAAC,CAAC,CAACjG,IAAI;QACxC,IAAIkE,KAAK,GAAG,IAAI,CAACC,MAAM;QACvB,IAAI+B,YAAY,GAAGhB,WAAW,CAAC;QAAA,EAC7Bc,SAAS,CAACG,mBAAmB,CAAC;UAC9BC,KAAK,EAAElC,KAAK,CAAC,CAAC,CAAC;UACfmC,GAAG,EAAEnC,KAAK,CAAC,CAAC;QACd,CAAC,CAAC,CAACoC,WAAW,GAAGN,SAAS,CAACO,kBAAkB,CAAC,CAAC;QAC/CR,UAAU,GAAG,CAAC,IAAI,CAACS,YAAY,CAACN,YAAY,CAAC,CAAC,CAAC,EAAElG,IAAI,CAAC,EAAE,IAAI,CAACwG,YAAY,CAACN,YAAY,CAAC,CAAC,CAAC,EAAElG,IAAI,CAAC,CAAC;MACnG;IACF;IAEA,IAAIyG,iBAAiB,GAAGhS,GAAG,CAAC,IAAI,CAAC6P,WAAW,CAACe,KAAK,CAAC,CAAC,CAAC;IACrDqB,QAAQ,CAACC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IACtBD,QAAQ,CAACC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IAEtB,SAASD,QAAQA,CAACvF,WAAW,EAAE;MAC7B;MACA;MACA;MACA,IAAIyF,YAAY,GAAGvS,OAAO,CAACwS,YAAY,CAAC1B,WAAW,CAAC1E,OAAO,CAACU,WAAW,CAAC,CAAC2F,MAAM,EAAE,IAAI,CAACzP,KAAK,CAAC;MAC5F,IAAI0P,SAAS,GAAG1S,OAAO,CAAC2S,kBAAkB,CAAC7F,WAAW,KAAK,CAAC,GAAG,OAAO,GAAG,MAAM,EAAEyF,YAAY,CAAC;MAC9F,IAAIK,MAAM,GAAG,IAAI,CAAC5E,YAAY,GAAG,CAAC,GAAG1M,SAAS;MAC9C,IAAIuR,SAAS,GAAG7S,OAAO,CAAC8S,cAAc,CAAC,CAACV,iBAAiB,CAACtF,WAAW,CAAC,IAAIA,WAAW,KAAK,CAAC,GAAG,CAAC8F,MAAM,GAAGA,MAAM,CAAC,EAAE,IAAI,CAAC7M,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEwM,YAAY,CAAC;MAClJlG,YAAY,CAACS,WAAW,CAAC,CAACmB,QAAQ,CAAC;QACjC5I,CAAC,EAAEwN,SAAS,CAAC,CAAC,CAAC;QACftN,CAAC,EAAEsN,SAAS,CAAC,CAAC,CAAC;QACflE,aAAa,EAAEzI,MAAM,KAAK9E,UAAU,GAAG,QAAQ,GAAGsR,SAAS;QAC3D9D,KAAK,EAAE1I,MAAM,KAAK9E,UAAU,GAAGsR,SAAS,GAAG,QAAQ;QACnDhE,IAAI,EAAEgD,UAAU,CAAC5E,WAAW;MAC9B,CAAC,CAAC;IACJ;EACF,CAAC;EAEDlL,cAAc,CAACO,SAAS,CAACgQ,YAAY,GAAG,UAAUtI,KAAK,EAAE8B,IAAI,EAAE;IAC7D,IAAIjJ,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIqQ,cAAc,GAAGrQ,aAAa,CAACG,GAAG,CAAC,gBAAgB,CAAC;IACxD,IAAImQ,cAAc,GAAGtQ,aAAa,CAACG,GAAG,CAAC,gBAAgB,CAAC;IAExD,IAAImQ,cAAc,IAAI,IAAI,IAAIA,cAAc,KAAK,MAAM,EAAE;MACvDA,cAAc,GAAGrH,IAAI,CAACsH,iBAAiB,CAAC,CAAC;IAC3C;IAEA,IAAIC,QAAQ,GAAGrJ,KAAK,IAAI,IAAI,IAAIG,KAAK,CAACH,KAAK,CAAC,GAAG,EAAE,CAAC;IAAA,EAChD8B,IAAI,CAAC1J,IAAI,KAAK,UAAU,IAAI0J,IAAI,CAAC1J,IAAI,KAAK,MAAM,GAAG0J,IAAI,CAACwH,KAAK,CAACC,QAAQ,CAAC;MACvEvJ,KAAK,EAAEjD,IAAI,CAAC+C,KAAK,CAACE,KAAK;IACzB,CAAC,CAAC,CAAC;IAAA,EACDA,KAAK,CAACwJ,OAAO,CAACzM,IAAI,CAAC2I,GAAG,CAACyD,cAAc,EAAE,EAAE,CAAC,CAAC;IAC7C,OAAOpT,UAAU,CAACmT,cAAc,CAAC,GAAGA,cAAc,CAAClJ,KAAK,EAAEqJ,QAAQ,CAAC,GAAGrT,QAAQ,CAACkT,cAAc,CAAC,GAAGA,cAAc,CAACO,OAAO,CAAC,SAAS,EAAEJ,QAAQ,CAAC,GAAGA,QAAQ;EACzJ,CAAC;EACD;AACF;AACA;;EAGEtR,cAAc,CAACO,SAAS,CAACwL,aAAa,GAAG,UAAU4F,UAAU,EAAE;IAC7D;IACAA,UAAU,GAAG,IAAI,CAACC,SAAS,IAAID,UAAU;IACzC,IAAIpH,YAAY,GAAG,IAAI,CAACjK,aAAa;IACrC,IAAImK,YAAY,GAAGF,YAAY,CAACE,YAAY;IAC5CA,YAAY,CAAC,CAAC,CAAC,CAAC7F,IAAI,CAAC,WAAW,EAAE,CAAC+M,UAAU,CAAC;IAC9ClH,YAAY,CAAC,CAAC,CAAC,CAAC7F,IAAI,CAAC,WAAW,EAAE,CAAC+M,UAAU,CAAC,CAAC,CAAC;;IAEhDpH,YAAY,CAACgD,UAAU,IAAI,IAAI,CAAC7M,GAAG,CAACiR,UAAU,GAAG,eAAe,GAAG,eAAe,CAAC,CAACpH,YAAY,CAACgD,UAAU,EAAE,CAAC,CAAC;EACjH,CAAC;EAEDvN,cAAc,CAACO,SAAS,CAACoL,WAAW,GAAG,UAAUT,WAAW,EAAE2G,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAE;IAC3E,IAAI,CAACH,SAAS,GAAG,IAAI,CAAC,CAAC;;IAEvBzT,SAAS,CAAC6T,IAAI,CAACD,KAAK,CAACA,KAAK,CAAC,CAAC,CAAC;;IAE7B,IAAIpB,YAAY,GAAG,IAAI,CAACrQ,aAAa,CAACiC,WAAW,CAAC0P,iBAAiB,CAAC,CAAC;IAErE,IAAIC,MAAM,GAAG9T,OAAO,CAAC8S,cAAc,CAAC,CAACW,EAAE,EAAEC,EAAE,CAAC,EAAEnB,YAAY,EAAE,IAAI,CAAC;IAEjE,IAAIwB,OAAO,GAAG,IAAI,CAAC7D,eAAe,CAACpD,WAAW,EAAEgH,MAAM,CAAC,CAAC,CAAC,CAAC;IAE1D,IAAIE,QAAQ,GAAG,IAAI,CAACtR,aAAa,CAACG,GAAG,CAAC,UAAU,CAAC;IAEjD,IAAI,CAACU,WAAW,CAAC,CAACyQ,QAAQ,CAAC,CAAC,CAAC;IAC7B;;IAGAD,OAAO,IAAIC,QAAQ,IAAI,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAC;EACvD,CAAC;EAEDrS,cAAc,CAACO,SAAS,CAACsL,UAAU,GAAG,YAAY;IAChD,IAAI,CAAC+F,SAAS,GAAG,KAAK;IAEtB,IAAI,CAAC7F,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3B;;IAGA,IAAIqG,QAAQ,GAAG,IAAI,CAACtR,aAAa,CAACG,GAAG,CAAC,UAAU,CAAC;IACjD,CAACmR,QAAQ,IAAI,IAAI,CAACC,mBAAmB,CAAC,KAAK,CAAC;EAC9C,CAAC;EAEDrS,cAAc,CAACO,SAAS,CAACwF,aAAa,GAAG,UAAUuM,CAAC,EAAE;IACpD,IAAIhN,IAAI,GAAG,IAAI,CAACnB,KAAK;IAErB,IAAIoO,UAAU,GAAG,IAAI,CAACjS,aAAa,CAACiC,WAAW,CAACiQ,qBAAqB,CAACF,CAAC,CAACG,OAAO,EAAEH,CAAC,CAACI,OAAO,CAAC;IAE3F,IAAIH,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,GAAGjN,IAAI,CAAC,CAAC,CAAC,IAAIiN,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,GAAGjN,IAAI,CAAC,CAAC,CAAC,EAAE;MAChG;IACF;IAEA,IAAIkJ,UAAU,GAAG,IAAI,CAACH,WAAW;IACjC,IAAIsE,MAAM,GAAG,CAACnE,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;IAEhD,IAAI2D,OAAO,GAAG,IAAI,CAAC7D,eAAe,CAAC,KAAK,EAAEiE,UAAU,CAAC,CAAC,CAAC,GAAGI,MAAM,CAAC;IAEjE,IAAI,CAAChR,WAAW,CAAC,CAAC;IAElBwQ,OAAO,IAAI,IAAI,CAACE,mBAAmB,CAAC,KAAK,CAAC;EAC5C,CAAC;EAEDrS,cAAc,CAACO,SAAS,CAAC0F,aAAa,GAAG,UAAUqM,CAAC,EAAE;IACpD,IAAI7O,CAAC,GAAG6O,CAAC,CAACG,OAAO;IACjB,IAAI9O,CAAC,GAAG2O,CAAC,CAACI,OAAO;IACjB,IAAI,CAACE,WAAW,GAAG,IAAIxU,OAAO,CAACyU,KAAK,CAACpP,CAAC,EAAEE,CAAC,CAAC;IAC1C,IAAI,CAACzB,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC4Q,eAAe,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC,CAAC;EACtC,CAAC;EAED/S,cAAc,CAACO,SAAS,CAACK,WAAW,GAAG,UAAU0R,CAAC,EAAE;IAClD,IAAI,CAAC,IAAI,CAACpQ,SAAS,EAAE;MACnB;IACF;IAEA,IAAIC,SAAS,GAAG,IAAI,CAAC7B,aAAa,CAAC6B,SAAS;IAC5C,IAAI,CAACD,SAAS,GAAG,KAAK;IAEtB,IAAI,CAACC,SAAS,EAAE;MACd;IACF;IAEAA,SAAS,CAACyC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC;IAC9B,IAAIoO,UAAU,GAAG7Q,SAAS,CAACsD,KAAK;IAChC,IAAIwN,YAAY,GAAG,CAAC,IAAIF,IAAI,CAAC,CAAC,CAAC,CAAC;;IAEhC,IAAIE,YAAY,GAAG,IAAI,CAACH,eAAe,GAAG,GAAG,IAAI9N,IAAI,CAACkO,GAAG,CAACF,UAAU,CAAC7P,KAAK,CAAC,GAAG,CAAC,EAAE;MAC/E;MACA;IACF;IAEA,IAAIsL,UAAU,GAAG,IAAI,CAACpJ,cAAc,CAAC,CAAC;IAEtC,IAAIwJ,aAAa,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;IAC5B,IAAI,CAACX,MAAM,GAAG1P,GAAG,CAAC,CAACD,SAAS,CAACyU,UAAU,CAACvP,CAAC,EAAEgL,UAAU,EAAEI,aAAa,EAAE,IAAI,CAAC,EAAEtQ,SAAS,CAACyU,UAAU,CAACvP,CAAC,GAAGuP,UAAU,CAAC7P,KAAK,EAAEsL,UAAU,EAAEI,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1J,IAAI,CAACR,WAAW,GAAG,CAAC2E,UAAU,CAACvP,CAAC,EAAEuP,UAAU,CAACvP,CAAC,GAAGuP,UAAU,CAAC7P,KAAK,CAAC;IAElE,IAAI,CAACxB,WAAW,CAAC,CAAC;IAElB,IAAI,CAAC0Q,mBAAmB,CAAC,KAAK,CAAC;EACjC,CAAC;EAEDrS,cAAc,CAACO,SAAS,CAACI,QAAQ,GAAG,UAAU2R,CAAC,EAAE;IAC/C,IAAI,IAAI,CAACpQ,SAAS,EAAE;MAClB;MACA/D,SAAS,CAAC6T,IAAI,CAACM,CAAC,CAACP,KAAK,CAAC;MAEvB,IAAI,CAACoB,gBAAgB,CAACb,CAAC,CAACG,OAAO,EAAEH,CAAC,CAACI,OAAO,CAAC;IAC7C;EACF,CAAC;EAED1S,cAAc,CAACO,SAAS,CAAC4S,gBAAgB,GAAG,UAAUC,MAAM,EAAEC,MAAM,EAAE;IACpE,IAAI9I,YAAY,GAAG,IAAI,CAACjK,aAAa;IACrC,IAAIQ,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC,IAAIqB,SAAS,GAAGoI,YAAY,CAACpI,SAAS;IAEtC,IAAI,CAACA,SAAS,EAAE;MACdA,SAAS,GAAGoI,YAAY,CAACpI,SAAS,GAAG,IAAIhD,IAAI,CAAC;QAC5CqG,MAAM,EAAE,IAAI;QACZE,KAAK,EAAE5E,aAAa,CAAC6H,QAAQ,CAAC,YAAY,CAAC,CAAC2D,YAAY,CAAC;MAC3D,CAAC,CAAC;MACF/B,YAAY,CAAChI,WAAW,CAACK,GAAG,CAACT,SAAS,CAAC;IACzC;IAEAA,SAAS,CAACyC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;IAC/B,IAAI0O,UAAU,GAAG,IAAI,CAACV,WAAW;IACjC,IAAIrQ,WAAW,GAAG,IAAI,CAACjC,aAAa,CAACiC,WAAW;IAChD,IAAIgR,QAAQ,GAAGhR,WAAW,CAACiQ,qBAAqB,CAACY,MAAM,EAAEC,MAAM,CAAC;IAChE,IAAIG,UAAU,GAAGjR,WAAW,CAACiQ,qBAAqB,CAACc,UAAU,CAAC7P,CAAC,EAAE6P,UAAU,CAAC3P,CAAC,CAAC;IAC9E,IAAI2B,IAAI,GAAG,IAAI,CAACnB,KAAK;IACrBoP,QAAQ,CAAC,CAAC,CAAC,GAAGvO,IAAI,CAAC4I,GAAG,CAAC5I,IAAI,CAAC2I,GAAG,CAACrI,IAAI,CAAC,CAAC,CAAC,EAAEiO,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACzDpR,SAAS,CAACoN,QAAQ,CAAC;MACjB9L,CAAC,EAAE+P,UAAU,CAAC,CAAC,CAAC;MAChB7P,CAAC,EAAE,CAAC;MACJR,KAAK,EAAEoQ,QAAQ,CAAC,CAAC,CAAC,GAAGC,UAAU,CAAC,CAAC,CAAC;MAClCnQ,MAAM,EAAEiC,IAAI,CAAC,CAAC;IAChB,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;;EAGEtF,cAAc,CAACO,SAAS,CAAC8R,mBAAmB,GAAG,UAAUD,QAAQ,EAAE;IACjE,IAAInE,KAAK,GAAG,IAAI,CAACC,MAAM;IACvB,IAAI,CAACxN,GAAG,CAAC+S,cAAc,CAAC;MACtBpT,IAAI,EAAE,UAAU;MAChBmB,IAAI,EAAE,IAAI,CAACC,GAAG;MACdiS,UAAU,EAAE,IAAI,CAAC5S,aAAa,CAAC6S,EAAE;MACjCC,SAAS,EAAExB,QAAQ,GAAGxS,yBAAyB,GAAG,IAAI;MACtDuQ,KAAK,EAAElC,KAAK,CAAC,CAAC,CAAC;MACfmC,GAAG,EAAEnC,KAAK,CAAC,CAAC;IACd,CAAC,CAAC;EACJ,CAAC;EAEDjO,cAAc,CAACO,SAAS,CAAC0C,cAAc,GAAG,YAAY;IACpD;IACA,IAAIiC,IAAI;IACR,IAAI2O,gBAAgB,GAAGhV,6BAA6B,CAAC,IAAI,CAACiC,aAAa,CAAC,CAACgT,QAAQ;IAEjF,IAAI,CAAC5O,IAAI,IAAI2O,gBAAgB,CAACtL,MAAM,EAAE;MACpC,IAAI0B,QAAQ,GAAG4J,gBAAgB,CAAC,CAAC,CAAC,CAACnL,KAAK,CAACwB,gBAAgB;MACzDhF,IAAI,GAAG+E,QAAQ,CAAC8J,OAAO,IAAI9J,QAAQ,CAAC8J,OAAO,CAAC,CAAC;IAC/C;IAEA,IAAI,CAAC7O,IAAI,EAAE;MACT,IAAI/B,KAAK,GAAG,IAAI,CAACzC,GAAG,CAAC0C,QAAQ,CAAC,CAAC;MAC/B,IAAIC,MAAM,GAAG,IAAI,CAAC3C,GAAG,CAAC4C,SAAS,CAAC,CAAC;MACjC4B,IAAI,GAAG;QACLzB,CAAC,EAAEN,KAAK,GAAG,GAAG;QACdQ,CAAC,EAAEN,MAAM,GAAG,GAAG;QACfF,KAAK,EAAEA,KAAK,GAAG,GAAG;QAClBE,MAAM,EAAEA,MAAM,GAAG;MACnB,CAAC;IACH;IAEA,OAAO6B,IAAI;EACb,CAAC;EAEDlF,cAAc,CAACK,IAAI,GAAG,iBAAiB;EACvC,OAAOL,cAAc;AACvB,CAAC,CAAC1B,YAAY,CAAC;AAEf,SAAS0L,WAAWA,CAACM,OAAO,EAAE;EAC5B;EACA;EACA,IAAI0J,GAAG,GAAG;IACRvQ,CAAC,EAAE,GAAG;IACNE,CAAC,EAAE,GAAG;IACNsQ,MAAM,EAAE,OAAO;IACfC,KAAK,EAAE;EACT,CAAC;EACD,OAAOF,GAAG,CAAC1J,OAAO,CAAC;AACrB;AAEA,SAASkB,SAASA,CAAClH,MAAM,EAAE;EACzB,OAAOA,MAAM,KAAK,UAAU,GAAG,WAAW,GAAG,WAAW;AAC1D;AAEA,eAAetE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module"}