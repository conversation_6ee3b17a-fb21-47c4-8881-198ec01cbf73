{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { noCase } from \"no-case\";\nimport { upperCaseFirst } from \"upper-case-first\";\nexport function capitalCaseTransform(input) {\n  return upperCaseFirst(input.toLowerCase());\n}\nexport function capitalCase(input, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return noCase(input, __assign({\n    delimiter: \" \",\n    transform: capitalCaseTransform\n  }, options));\n}", "map": {"version": 3, "names": ["noCase", "upperCaseFirst", "capitalCaseTransform", "input", "toLowerCase", "capitalCase", "options", "__assign", "delimiter", "transform"], "sources": ["../src/index.ts"], "sourcesContent": ["import { noCase, Options } from \"no-case\";\nimport { upperCaseFirst } from \"upper-case-first\";\n\nexport { Options };\n\nexport function capitalCaseTransform(input: string) {\n  return upperCaseFirst(input.toLowerCase());\n}\n\nexport function capitalCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \" \",\n    transform: capitalCaseTransform,\n    ...options,\n  });\n}\n"], "mappings": ";AAAA,SAASA,MAAM,QAAiB,SAAS;AACzC,SAASC,cAAc,QAAQ,kBAAkB;AAIjD,OAAM,SAAUC,oBAAoBA,CAACC,KAAa;EAChD,OAAOF,cAAc,CAACE,KAAK,CAACC,WAAW,EAAE,CAAC;AAC5C;AAEA,OAAM,SAAUC,WAAWA,CAACF,KAAa,EAAEG,OAAqB;EAArB,IAAAA,OAAA;IAAAA,OAAA,KAAqB;EAAA;EAC9D,OAAON,MAAM,CAACG,KAAK,EAAAI,QAAA;IACjBC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAEP;EAAoB,GAC5BI,OAAO,EACV;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}