{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { noCase } from \"no-case\";\nimport { upperCaseFirst } from \"upper-case-first\";\nexport function sentenceCaseTransform(input, index) {\n  var result = input.toLowerCase();\n  if (index === 0) return upperCaseFirst(result);\n  return result;\n}\nexport function sentenceCase(input, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return noCase(input, __assign({\n    delimiter: \" \",\n    transform: sentenceCaseTransform\n  }, options));\n}", "map": {"version": 3, "names": ["noCase", "upperCaseFirst", "sentenceCaseTransform", "input", "index", "result", "toLowerCase", "sentenceCase", "options", "__assign", "delimiter", "transform"], "sources": ["../src/index.ts"], "sourcesContent": ["import { noCase, Options } from \"no-case\";\nimport { upperCaseFirst } from \"upper-case-first\";\n\nexport { Options };\n\nexport function sentenceCaseTransform(input: string, index: number) {\n  const result = input.toLowerCase();\n  if (index === 0) return upperCaseFirst(result);\n  return result;\n}\n\nexport function sentenceCase(input: string, options: Options = {}) {\n  return noCase(input, {\n    delimiter: \" \",\n    transform: sentenceCaseTransform,\n    ...options,\n  });\n}\n"], "mappings": ";AAAA,SAASA,MAAM,QAAiB,SAAS;AACzC,SAASC,cAAc,QAAQ,kBAAkB;AAIjD,OAAM,SAAUC,qBAAqBA,CAACC,KAAa,EAAEC,KAAa;EAChE,IAAMC,MAAM,GAAGF,KAAK,CAACG,WAAW,EAAE;EAClC,IAAIF,KAAK,KAAK,CAAC,EAAE,OAAOH,cAAc,CAACI,MAAM,CAAC;EAC9C,OAAOA,MAAM;AACf;AAEA,OAAM,SAAUE,YAAYA,CAACJ,KAAa,EAAEK,OAAqB;EAArB,IAAAA,OAAA;IAAAA,OAAA,KAAqB;EAAA;EAC/D,OAAOR,MAAM,CAACG,KAAK,EAAAM,QAAA;IACjBC,SAAS,EAAE,GAAG;IACdC,SAAS,EAAET;EAAqB,GAC7BM,OAAO,EACV;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}