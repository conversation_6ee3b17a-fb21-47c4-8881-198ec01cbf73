{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name areIntervalsOverlapping\n * @category Interval Helpers\n * @summary Is the given time interval overlapping with another time interval?\n *\n * @description\n * Is the given time interval overlapping with another time interval? Adjacent intervals do not count as overlapping.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - The function was renamed from `areRangesOverlapping` to `areIntervalsOverlapping`.\n *   This change was made to mirror the use of the word \"interval\" in standard ISO 8601:2004 terminology:\n *\n *   ```\n *   2.1.3\n *   time interval\n *   part of the time axis limited by two instants\n *   ```\n *\n *   Also, this function now accepts an object with `start` and `end` properties\n *   instead of two arguments as an interval.\n *   This function now throws `RangeError` if the start of the interval is after its end\n *   or if any date in the interval is `Invalid Date`.\n *\n *   ```javascript\n *   // Before v2.0.0\n *\n *   areRangesOverlapping(\n *     new Date(2014, 0, 10), new Date(2014, 0, 20),\n *     new Date(2014, 0, 17), new Date(2014, 0, 21)\n *   )\n *\n *   // v2.0.0 onward\n *\n *   areIntervalsOverlapping(\n *     { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *     { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n *   )\n *   ```\n *\n * @param {Interval} intervalLeft - the first interval to compare. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Interval} intervalRight - the second interval to compare. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - the object with options\n * @param {Boolean} [options.inclusive=false] - whether the comparison is inclusive or not\n * @returns {Boolean} whether the time intervals are overlapping\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> true\n *\n * @example\n * // For non-overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> false\n *\n * @example\n * // For adjacent time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 30) }\n * )\n * //=> false\n *\n * @example\n * // Using the inclusive option:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) }\n * )\n * //=> false\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) },\n *   { inclusive: true }\n * )\n * //=> true\n */\n\nexport default function areIntervalsOverlapping(dirtyIntervalLeft, dirtyIntervalRight) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    inclusive: false\n  };\n  requiredArgs(2, arguments);\n  var intervalLeft = dirtyIntervalLeft || {};\n  var intervalRight = dirtyIntervalRight || {};\n  var leftStartTime = toDate(intervalLeft.start).getTime();\n  var leftEndTime = toDate(intervalLeft.end).getTime();\n  var rightStartTime = toDate(intervalRight.start).getTime();\n  var rightEndTime = toDate(intervalRight.end).getTime(); // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(leftStartTime <= leftEndTime && rightStartTime <= rightEndTime)) {\n    throw new RangeError('Invalid interval');\n  }\n  if (options.inclusive) {\n    return leftStartTime <= rightEndTime && rightStartTime <= leftEndTime;\n  }\n  return leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "areIntervalsOverlapping", "dirtyIntervalLeft", "dirtyIntervalRight", "options", "arguments", "length", "undefined", "inclusive", "intervalLeft", "intervalRight", "leftStartTime", "start", "getTime", "leftEndTime", "end", "rightStartTime", "rightEndTime", "RangeError"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/areIntervalsOverlapping/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name areIntervalsOverlapping\n * @category Interval Helpers\n * @summary Is the given time interval overlapping with another time interval?\n *\n * @description\n * Is the given time interval overlapping with another time interval? Adjacent intervals do not count as overlapping.\n *\n * ### v2.0.0 breaking changes:\n *\n * - [Changes that are common for the whole library](https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#Common-Changes).\n *\n * - The function was renamed from `areRangesOverlapping` to `areIntervalsOverlapping`.\n *   This change was made to mirror the use of the word \"interval\" in standard ISO 8601:2004 terminology:\n *\n *   ```\n *   2.1.3\n *   time interval\n *   part of the time axis limited by two instants\n *   ```\n *\n *   Also, this function now accepts an object with `start` and `end` properties\n *   instead of two arguments as an interval.\n *   This function now throws `RangeError` if the start of the interval is after its end\n *   or if any date in the interval is `Invalid Date`.\n *\n *   ```javascript\n *   // Before v2.0.0\n *\n *   areRangesOverlapping(\n *     new Date(2014, 0, 10), new Date(2014, 0, 20),\n *     new Date(2014, 0, 17), new Date(2014, 0, 21)\n *   )\n *\n *   // v2.0.0 onward\n *\n *   areIntervalsOverlapping(\n *     { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *     { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n *   )\n *   ```\n *\n * @param {Interval} intervalLeft - the first interval to compare. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Interval} intervalRight - the second interval to compare. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @param {Object} [options] - the object with options\n * @param {Boolean} [options.inclusive=false] - whether the comparison is inclusive or not\n * @returns {Boolean} whether the time intervals are overlapping\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // For overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> true\n *\n * @example\n * // For non-overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> false\n *\n * @example\n * // For adjacent time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 30) }\n * )\n * //=> false\n *\n * @example\n * // Using the inclusive option:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) }\n * )\n * //=> false\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) },\n *   { inclusive: true }\n * )\n * //=> true\n */\n\nexport default function areIntervalsOverlapping(dirtyIntervalLeft, dirtyIntervalRight) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    inclusive: false\n  };\n  requiredArgs(2, arguments);\n  var intervalLeft = dirtyIntervalLeft || {};\n  var intervalRight = dirtyIntervalRight || {};\n  var leftStartTime = toDate(intervalLeft.start).getTime();\n  var leftEndTime = toDate(intervalLeft.end).getTime();\n  var rightStartTime = toDate(intervalRight.start).getTime();\n  var rightEndTime = toDate(intervalRight.end).getTime(); // Throw an exception if start date is after end date or if any date is `Invalid Date`\n\n  if (!(leftStartTime <= leftEndTime && rightStartTime <= rightEndTime)) {\n    throw new RangeError('Invalid interval');\n  }\n\n  if (options.inclusive) {\n    return leftStartTime <= rightEndTime && rightStartTime <= leftEndTime;\n  }\n\n  return leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,uBAAuBA,CAACC,iBAAiB,EAAEC,kBAAkB,EAAE;EACrF,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;IAChFG,SAAS,EAAE;EACb,CAAC;EACDR,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAII,YAAY,GAAGP,iBAAiB,IAAI,CAAC,CAAC;EAC1C,IAAIQ,aAAa,GAAGP,kBAAkB,IAAI,CAAC,CAAC;EAC5C,IAAIQ,aAAa,GAAGZ,MAAM,CAACU,YAAY,CAACG,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC;EACxD,IAAIC,WAAW,GAAGf,MAAM,CAACU,YAAY,CAACM,GAAG,CAAC,CAACF,OAAO,CAAC,CAAC;EACpD,IAAIG,cAAc,GAAGjB,MAAM,CAACW,aAAa,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC;EAC1D,IAAII,YAAY,GAAGlB,MAAM,CAACW,aAAa,CAACK,GAAG,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;;EAExD,IAAI,EAAEF,aAAa,IAAIG,WAAW,IAAIE,cAAc,IAAIC,YAAY,CAAC,EAAE;IACrE,MAAM,IAAIC,UAAU,CAAC,kBAAkB,CAAC;EAC1C;EAEA,IAAId,OAAO,CAACI,SAAS,EAAE;IACrB,OAAOG,aAAa,IAAIM,YAAY,IAAID,cAAc,IAAIF,WAAW;EACvE;EAEA,OAAOH,aAAa,GAAGM,YAAY,IAAID,cAAc,GAAGF,WAAW;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module"}