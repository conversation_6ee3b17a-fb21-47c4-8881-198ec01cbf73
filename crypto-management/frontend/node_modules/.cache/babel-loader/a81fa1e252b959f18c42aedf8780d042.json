{"ast": null, "code": "import subDays from \"../subDays/index.js\";\nimport subMonths from \"../subMonths/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name sub\n * @category Common Helpers\n * @summary Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @description\n * Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Duration} duration - the object with years, months, weeks, days, hours, minutes and seconds to be subtracted\n *\n * | Key     | Description                        |\n * |---------|------------------------------------|\n * | years   | Amount of years to be subtracted   |\n * | months  | Amount of months to be subtracted  |\n * | weeks   | Amount of weeks to be subtracted   |\n * | days    | Amount of days to be subtracted    |\n * | hours   | Amount of hours to be subtracted   |\n * | minutes | Amount of minutes to be subtracted |\n * | seconds | Amount of seconds to be subtracted |\n *\n * All values default to 0\n *\n * @returns {Date} the new date with the seconds subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract the following duration from 15 June 2017 15:29:20\n * const result = sub(new Date(2017, 5, 15, 15, 29, 20), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> Mon Sep 1 2014 10:19:50\n */\n\nexport default function sub(date, duration) {\n  requiredArgs(2, arguments);\n  if (!duration || typeof duration !== 'object') return new Date(NaN);\n  var years = duration.years ? toInteger(duration.years) : 0;\n  var months = duration.months ? toInteger(duration.months) : 0;\n  var weeks = duration.weeks ? toInteger(duration.weeks) : 0;\n  var days = duration.days ? toInteger(duration.days) : 0;\n  var hours = duration.hours ? toInteger(duration.hours) : 0;\n  var minutes = duration.minutes ? toInteger(duration.minutes) : 0;\n  var seconds = duration.seconds ? toInteger(duration.seconds) : 0; // Subtract years and months\n\n  var dateWithoutMonths = subMonths(date, months + years * 12); // Subtract weeks and days\n\n  var dateWithoutDays = subDays(dateWithoutMonths, days + weeks * 7); // Subtract hours, minutes and seconds\n\n  var minutestoSub = minutes + hours * 60;\n  var secondstoSub = seconds + minutestoSub * 60;\n  var mstoSub = secondstoSub * 1000;\n  var finalDate = new Date(dateWithoutDays.getTime() - mstoSub);\n  return finalDate;\n}", "map": {"version": 3, "names": ["subDays", "subMonths", "requiredArgs", "toInteger", "sub", "date", "duration", "arguments", "Date", "NaN", "years", "months", "weeks", "days", "hours", "minutes", "seconds", "dateWithoutMonths", "dateWithoutDays", "minutestoSub", "secondstoSub", "mstoSub", "finalDate", "getTime"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/date-fns/esm/sub/index.js"], "sourcesContent": ["import subDays from \"../subDays/index.js\";\nimport subMonths from \"../subMonths/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\n/**\n * @name sub\n * @category Common Helpers\n * @summary Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @description\n * Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Duration} duration - the object with years, months, weeks, days, hours, minutes and seconds to be subtracted\n *\n * | Key     | Description                        |\n * |---------|------------------------------------|\n * | years   | Amount of years to be subtracted   |\n * | months  | Amount of months to be subtracted  |\n * | weeks   | Amount of weeks to be subtracted   |\n * | days    | Amount of days to be subtracted    |\n * | hours   | Amount of hours to be subtracted   |\n * | minutes | Amount of minutes to be subtracted |\n * | seconds | Amount of seconds to be subtracted |\n *\n * All values default to 0\n *\n * @returns {Date} the new date with the seconds subtracted\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Subtract the following duration from 15 June 2017 15:29:20\n * const result = sub(new Date(2017, 5, 15, 15, 29, 20), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> Mon Sep 1 2014 10:19:50\n */\n\nexport default function sub(date, duration) {\n  requiredArgs(2, arguments);\n  if (!duration || typeof duration !== 'object') return new Date(NaN);\n  var years = duration.years ? toInteger(duration.years) : 0;\n  var months = duration.months ? toInteger(duration.months) : 0;\n  var weeks = duration.weeks ? toInteger(duration.weeks) : 0;\n  var days = duration.days ? toInteger(duration.days) : 0;\n  var hours = duration.hours ? toInteger(duration.hours) : 0;\n  var minutes = duration.minutes ? toInteger(duration.minutes) : 0;\n  var seconds = duration.seconds ? toInteger(duration.seconds) : 0; // Subtract years and months\n\n  var dateWithoutMonths = subMonths(date, months + years * 12); // Subtract weeks and days\n\n  var dateWithoutDays = subDays(dateWithoutMonths, days + weeks * 7); // Subtract hours, minutes and seconds\n\n  var minutestoSub = minutes + hours * 60;\n  var secondstoSub = seconds + minutestoSub * 60;\n  var mstoSub = secondstoSub * 1000;\n  var finalDate = new Date(dateWithoutDays.getTime() - mstoSub);\n  return finalDate;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,qBAAqB;AACzC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,SAAS,MAAM,4BAA4B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,GAAGA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC1CJ,YAAY,CAAC,CAAC,EAAEK,SAAS,CAAC;EAC1B,IAAI,CAACD,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE,OAAO,IAAIE,IAAI,CAACC,GAAG,CAAC;EACnE,IAAIC,KAAK,GAAGJ,QAAQ,CAACI,KAAK,GAAGP,SAAS,CAACG,QAAQ,CAACI,KAAK,CAAC,GAAG,CAAC;EAC1D,IAAIC,MAAM,GAAGL,QAAQ,CAACK,MAAM,GAAGR,SAAS,CAACG,QAAQ,CAACK,MAAM,CAAC,GAAG,CAAC;EAC7D,IAAIC,KAAK,GAAGN,QAAQ,CAACM,KAAK,GAAGT,SAAS,CAACG,QAAQ,CAACM,KAAK,CAAC,GAAG,CAAC;EAC1D,IAAIC,IAAI,GAAGP,QAAQ,CAACO,IAAI,GAAGV,SAAS,CAACG,QAAQ,CAACO,IAAI,CAAC,GAAG,CAAC;EACvD,IAAIC,KAAK,GAAGR,QAAQ,CAACQ,KAAK,GAAGX,SAAS,CAACG,QAAQ,CAACQ,KAAK,CAAC,GAAG,CAAC;EAC1D,IAAIC,OAAO,GAAGT,QAAQ,CAACS,OAAO,GAAGZ,SAAS,CAACG,QAAQ,CAACS,OAAO,CAAC,GAAG,CAAC;EAChE,IAAIC,OAAO,GAAGV,QAAQ,CAACU,OAAO,GAAGb,SAAS,CAACG,QAAQ,CAACU,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;;EAElE,IAAIC,iBAAiB,GAAGhB,SAAS,CAACI,IAAI,EAAEM,MAAM,GAAGD,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;;EAE9D,IAAIQ,eAAe,GAAGlB,OAAO,CAACiB,iBAAiB,EAAEJ,IAAI,GAAGD,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEpE,IAAIO,YAAY,GAAGJ,OAAO,GAAGD,KAAK,GAAG,EAAE;EACvC,IAAIM,YAAY,GAAGJ,OAAO,GAAGG,YAAY,GAAG,EAAE;EAC9C,IAAIE,OAAO,GAAGD,YAAY,GAAG,IAAI;EACjC,IAAIE,SAAS,GAAG,IAAId,IAAI,CAACU,eAAe,CAACK,OAAO,CAAC,CAAC,GAAGF,OAAO,CAAC;EAC7D,OAAOC,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module"}