{"ast": null, "code": "// Copyright (c) 2017, 2021 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nvar bech32 = require('./bech32');\nfunction convertbits(data, frombits, tobits, pad) {\n  var acc = 0;\n  var bits = 0;\n  var ret = [];\n  var maxv = (1 << tobits) - 1;\n  for (var p = 0; p < data.length; ++p) {\n    var value = data[p];\n    if (value < 0 || value >> frombits !== 0) {\n      return null;\n    }\n    acc = acc << frombits | value;\n    bits += frombits;\n    while (bits >= tobits) {\n      bits -= tobits;\n      ret.push(acc >> bits & maxv);\n    }\n  }\n  if (pad) {\n    if (bits > 0) {\n      ret.push(acc << tobits - bits & maxv);\n    }\n  } else if (bits >= frombits || acc << tobits - bits & maxv) {\n    return null;\n  }\n  return ret;\n}\nfunction decode(hrp, addr) {\n  var bech32m = false;\n  var dec = bech32.decode(addr, bech32.encodings.BECH32);\n  if (dec === null) {\n    dec = bech32.decode(addr, bech32.encodings.BECH32M);\n    bech32m = true;\n  }\n  if (dec === null || dec.hrp !== hrp || dec.data.length < 1 || dec.data[0] > 16) {\n    return null;\n  }\n  var res = convertbits(dec.data.slice(1), 5, 8, false);\n  if (res === null || res.length < 2 || res.length > 40) {\n    return null;\n  }\n  if (dec.data[0] === 0 && res.length !== 20 && res.length !== 32) {\n    return null;\n  }\n  if (dec.data[0] === 0 && bech32m) {\n    return null;\n  }\n  if (dec.data[0] !== 0 && !bech32m) {\n    return null;\n  }\n  return {\n    version: dec.data[0],\n    program: res\n  };\n}\nfunction encode(hrp, version, program) {\n  var enc = bech32.encodings.BECH32;\n  if (version > 0) {\n    enc = bech32.encodings.BECH32M;\n  }\n  var ret = bech32.encode(hrp, [version].concat(convertbits(program, 8, 5, true)), enc);\n  if (decode(hrp, ret, enc) === null) {\n    return null;\n  }\n  return ret;\n}\n\n/////////////////////////////////////////////////////\n\nvar DEFAULT_NETWORK_TYPE = 'prod';\nfunction isValidAddress(address, currency, opts = {}) {\n  if (!currency.bech32Hrp || currency.bech32Hrp.length === 0) {\n    return false;\n  }\n  const {\n    networkType = DEFAULT_NETWORK_TYPE\n  } = opts;\n  var correctBech32Hrps;\n  if (networkType === 'prod' || networkType === 'testnet') {\n    correctBech32Hrps = currency.bech32Hrp[networkType];\n  } else if (currency.bech32Hrp) {\n    correctBech32Hrps = currency.bech32Hrp.prod.concat(currency.bech32Hrp.testnet);\n  } else {\n    return false;\n  }\n  for (var chrp of correctBech32Hrps) {\n    var ret = decode(chrp, address);\n    if (ret) {\n      return encode(chrp, ret.version, ret.program) === address.toLowerCase();\n    }\n  }\n  return false;\n}\nmodule.exports = {\n  encode: encode,\n  decode: decode,\n  isValidAddress: isValidAddress\n};", "map": {"version": 3, "names": ["bech32", "require", "convertbits", "data", "frombits", "tobits", "pad", "acc", "bits", "ret", "maxv", "p", "length", "value", "push", "decode", "hrp", "addr", "bech32m", "dec", "encodings", "BECH32", "BECH32M", "res", "slice", "version", "program", "encode", "enc", "concat", "DEFAULT_NETWORK_TYPE", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "address", "currency", "opts", "bech32Hrp", "networkType", "correctBech32Hrps", "prod", "testnet", "chrp", "toLowerCase", "module", "exports"], "sources": ["/Users/<USER>/Projects/linkedin-crypto-cloth-com/crypto-management/frontend/node_modules/multicoin-address-validator/src/crypto/segwit_addr.js"], "sourcesContent": ["// Copyright (c) 2017, 2021 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nvar bech32 = require('./bech32');\n\nfunction convertbits (data, frombits, tobits, pad) {\n    var acc = 0;\n    var bits = 0;\n    var ret = [];\n    var maxv = (1 << tobits) - 1;\n    for (var p = 0; p < data.length; ++p) {\n        var value = data[p];\n        if (value < 0 || (value >> frombits) !== 0) {\n            return null;\n        }\n        acc = (acc << frombits) | value;\n        bits += frombits;\n        while (bits >= tobits) {\n            bits -= tobits;\n            ret.push((acc >> bits) & maxv);\n        }\n    }\n    if (pad) {\n        if (bits > 0) {\n            ret.push((acc << (tobits - bits)) & maxv);\n        }\n    } else if (bits >= frombits || ((acc << (tobits - bits)) & maxv)) {\n        return null;\n    }\n    return ret;\n}\n\nfunction decode (hrp, addr) {\n    var bech32m = false;\n    var dec = bech32.decode(addr, bech32.encodings.BECH32);\n    if (dec === null) {\n        dec = bech32.decode(addr, bech32.encodings.BECH32M);\n        bech32m = true;\n    }\n    if (dec === null || dec.hrp !== hrp || dec.data.length < 1 || dec.data[0] > 16) {\n        return null;\n    }\n    var res = convertbits(dec.data.slice(1), 5, 8, false);\n    if (res === null || res.length < 2 || res.length > 40) {\n        return null;\n    }\n    if (dec.data[0] === 0 && res.length !== 20 && res.length !== 32) {\n        return null;\n    }\n    if (dec.data[0] === 0 && bech32m) {\n        return null;\n    }\n    if (dec.data[0] !== 0 && !bech32m) {\n        return null;\n    }\n    return {version: dec.data[0], program: res};\n}\n\nfunction encode (hrp, version, program) {\n    var enc = bech32.encodings.BECH32;\n    if (version > 0) {\n        enc = bech32.encodings.BECH32M;\n    }\n    var ret = bech32.encode(hrp, [version].concat(convertbits(program, 8, 5, true)), enc);\n    if (decode(hrp, ret, enc) === null) {\n        return null;\n    }\n    return ret;\n}\n\n/////////////////////////////////////////////////////\n\nvar DEFAULT_NETWORK_TYPE = 'prod'\n\nfunction isValidAddress(address, currency, opts = {}) {\n\n    if(!currency.bech32Hrp || currency.bech32Hrp.length === 0) {\n        return false;\n    }\n\n    const { networkType = DEFAULT_NETWORK_TYPE} = opts;\n\n    var correctBech32Hrps;\n    if (networkType === 'prod' || networkType === 'testnet') {\n        correctBech32Hrps = currency.bech32Hrp[networkType];\n    } else if(currency.bech32Hrp) {\n        correctBech32Hrps = currency.bech32Hrp.prod.concat(currency.bech32Hrp.testnet)\n    } else {\n        return false;\n    }\n\n    for(var chrp of correctBech32Hrps) {\n        var ret = decode(chrp, address);\n        if(ret) {\n            return encode(chrp, ret.version, ret.program) === address.toLowerCase();\n        }\n    }\n\n    return false;\n}\n\nmodule.exports = {\n    encode: encode,\n    decode: decode,\n    isValidAddress: isValidAddress,\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIA,MAAM,GAAGC,OAAO,CAAC,UAAU,CAAC;AAEhC,SAASC,WAAWA,CAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,GAAG,EAAE;EAC/C,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,IAAI,GAAG,CAAC,CAAC,IAAIL,MAAM,IAAI,CAAC;EAC5B,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,IAAI,CAACS,MAAM,EAAE,EAAED,CAAC,EAAE;IAClC,IAAIE,KAAK,GAAGV,IAAI,CAACQ,CAAC,CAAC;IACnB,IAAIE,KAAK,GAAG,CAAC,IAAKA,KAAK,IAAIT,QAAQ,KAAM,CAAC,EAAE;MACxC,OAAO,IAAI;IACf;IACAG,GAAG,GAAIA,GAAG,IAAIH,QAAQ,GAAIS,KAAK;IAC/BL,IAAI,IAAIJ,QAAQ;IAChB,OAAOI,IAAI,IAAIH,MAAM,EAAE;MACnBG,IAAI,IAAIH,MAAM;MACdI,GAAG,CAACK,IAAI,CAAEP,GAAG,IAAIC,IAAI,GAAIE,IAAI,CAAC;IAClC;EACJ;EACA,IAAIJ,GAAG,EAAE;IACL,IAAIE,IAAI,GAAG,CAAC,EAAE;MACVC,GAAG,CAACK,IAAI,CAAEP,GAAG,IAAKF,MAAM,GAAGG,IAAK,GAAIE,IAAI,CAAC;IAC7C;EACJ,CAAC,MAAM,IAAIF,IAAI,IAAIJ,QAAQ,IAAMG,GAAG,IAAKF,MAAM,GAAGG,IAAK,GAAIE,IAAK,EAAE;IAC9D,OAAO,IAAI;EACf;EACA,OAAOD,GAAG;AACd;AAEA,SAASM,MAAMA,CAAEC,GAAG,EAAEC,IAAI,EAAE;EACxB,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIC,GAAG,GAAGnB,MAAM,CAACe,MAAM,CAACE,IAAI,EAAEjB,MAAM,CAACoB,SAAS,CAACC,MAAM,CAAC;EACtD,IAAIF,GAAG,KAAK,IAAI,EAAE;IACdA,GAAG,GAAGnB,MAAM,CAACe,MAAM,CAACE,IAAI,EAAEjB,MAAM,CAACoB,SAAS,CAACE,OAAO,CAAC;IACnDJ,OAAO,GAAG,IAAI;EAClB;EACA,IAAIC,GAAG,KAAK,IAAI,IAAIA,GAAG,CAACH,GAAG,KAAKA,GAAG,IAAIG,GAAG,CAAChB,IAAI,CAACS,MAAM,GAAG,CAAC,IAAIO,GAAG,CAAChB,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE;IAC5E,OAAO,IAAI;EACf;EACA,IAAIoB,GAAG,GAAGrB,WAAW,CAACiB,GAAG,CAAChB,IAAI,CAACqB,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;EACrD,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,CAACX,MAAM,GAAG,CAAC,IAAIW,GAAG,CAACX,MAAM,GAAG,EAAE,EAAE;IACnD,OAAO,IAAI;EACf;EACA,IAAIO,GAAG,CAAChB,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIoB,GAAG,CAACX,MAAM,KAAK,EAAE,IAAIW,GAAG,CAACX,MAAM,KAAK,EAAE,EAAE;IAC7D,OAAO,IAAI;EACf;EACA,IAAIO,GAAG,CAAChB,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIe,OAAO,EAAE;IAC9B,OAAO,IAAI;EACf;EACA,IAAIC,GAAG,CAAChB,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAACe,OAAO,EAAE;IAC/B,OAAO,IAAI;EACf;EACA,OAAO;IAACO,OAAO,EAAEN,GAAG,CAAChB,IAAI,CAAC,CAAC,CAAC;IAAEuB,OAAO,EAAEH;EAAG,CAAC;AAC/C;AAEA,SAASI,MAAMA,CAAEX,GAAG,EAAES,OAAO,EAAEC,OAAO,EAAE;EACpC,IAAIE,GAAG,GAAG5B,MAAM,CAACoB,SAAS,CAACC,MAAM;EACjC,IAAII,OAAO,GAAG,CAAC,EAAE;IACbG,GAAG,GAAG5B,MAAM,CAACoB,SAAS,CAACE,OAAO;EAClC;EACA,IAAIb,GAAG,GAAGT,MAAM,CAAC2B,MAAM,CAACX,GAAG,EAAE,CAACS,OAAO,CAAC,CAACI,MAAM,CAAC3B,WAAW,CAACwB,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAEE,GAAG,CAAC;EACrF,IAAIb,MAAM,CAACC,GAAG,EAAEP,GAAG,EAAEmB,GAAG,CAAC,KAAK,IAAI,EAAE;IAChC,OAAO,IAAI;EACf;EACA,OAAOnB,GAAG;AACd;;AAEA;;AAEA,IAAIqB,oBAAoB,GAAG,MAAM;AAEjC,SAASC,cAAcA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;EAElD,IAAG,CAACD,QAAQ,CAACE,SAAS,IAAIF,QAAQ,CAACE,SAAS,CAACvB,MAAM,KAAK,CAAC,EAAE;IACvD,OAAO,KAAK;EAChB;EAEA,MAAM;IAAEwB,WAAW,GAAGN;EAAoB,CAAC,GAAGI,IAAI;EAElD,IAAIG,iBAAiB;EACrB,IAAID,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,SAAS,EAAE;IACrDC,iBAAiB,GAAGJ,QAAQ,CAACE,SAAS,CAACC,WAAW,CAAC;EACvD,CAAC,MAAM,IAAGH,QAAQ,CAACE,SAAS,EAAE;IAC1BE,iBAAiB,GAAGJ,QAAQ,CAACE,SAAS,CAACG,IAAI,CAACT,MAAM,CAACI,QAAQ,CAACE,SAAS,CAACI,OAAO,CAAC;EAClF,CAAC,MAAM;IACH,OAAO,KAAK;EAChB;EAEA,KAAI,IAAIC,IAAI,IAAIH,iBAAiB,EAAE;IAC/B,IAAI5B,GAAG,GAAGM,MAAM,CAACyB,IAAI,EAAER,OAAO,CAAC;IAC/B,IAAGvB,GAAG,EAAE;MACJ,OAAOkB,MAAM,CAACa,IAAI,EAAE/B,GAAG,CAACgB,OAAO,EAAEhB,GAAG,CAACiB,OAAO,CAAC,KAAKM,OAAO,CAACS,WAAW,CAAC,CAAC;IAC3E;EACJ;EAEA,OAAO,KAAK;AAChB;AAEAC,MAAM,CAACC,OAAO,GAAG;EACbhB,MAAM,EAAEA,MAAM;EACdZ,MAAM,EAAEA,MAAM;EACdgB,cAAc,EAAEA;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script"}